import React, { ReactNode } from 'react';
import { Typography, Icon, IconName } from '@/shared/components/common';
import ViewBreadcrumb from '@/shared/components/layout/view-panel/ViewBreadcrumb';

interface MarketingViewHeaderProps {
  title: string;
  description?: string;
  icon?: IconName;
  actions?: ReactNode;
  showBreadcrumb?: boolean;
}

/**
 * Header cho view panel trong module Marketing
 * @param props Props của component
 * @returns Component header cho view panel
 */
const MarketingViewHeader: React.FC<MarketingViewHeaderProps> = ({
  title,
  description,
  icon,
  actions,
  showBreadcrumb = false,
}) => {
  return (
    <div className="mb-6">
      {showBreadcrumb && <ViewBreadcrumb title={title} />}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <div className="flex items-center gap-3">
            {icon && <Icon name={icon} size="lg" className="text-primary" />}
            <Typography variant="h4" className="font-bold">
              {title}
            </Typography>
          </div>
          {description && (
            <Typography variant="body1" className="text-muted-foreground mt-1">
              {description}
            </Typography>
          )}
        </div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
    </div>
  );
};

export { MarketingViewHeader };
