import { apiClient } from '@/shared/api';
import { EXTERNAL_AGENT_ENDPOINTS } from '../constants';
import {
  ProtocolConfig,
  ProtocolTemplate,
  ProtocolDetectionResult,
  ProtocolValidationResult,
  PaginatedResponse,
} from '../types';

export const protocolApi = {
  // Get available protocols
  getProtocols: async (): Promise<ProtocolConfig[]> => {
    const response = await apiClient.get<ProtocolConfig[]>(
      EXTERNAL_AGENT_ENDPOINTS.PROTOCOLS
    );
    return response.result;
  },

  // Detect protocol from endpoint
  detectProtocol: async (endpoint: string): Promise<ProtocolDetectionResult> => {
    const response = await apiClient.post<ProtocolDetectionResult>(
      EXTERNAL_AGENT_ENDPOINTS.PROTOCOL_DETECT,
      { endpoint }
    );
    return response.result;
  },

  // Validate protocol configuration
  validateProtocol: async (config: ProtocolConfig): Promise<ProtocolValidationResult> => {
    const response = await apiClient.post<ProtocolValidationResult>(
      EXTERNAL_AGENT_ENDPOINTS.PROTOCOL_VALIDATE,
      config
    );
    return response.result;
  },

  // Protocol Templates
  getProtocolTemplates: async (params?: { page?: number; limit?: number }): Promise<PaginatedResponse<ProtocolTemplate>> => {
    const response = await apiClient.get<PaginatedResponse<ProtocolTemplate>>(
      EXTERNAL_AGENT_ENDPOINTS.PROTOCOL_TEMPLATES,
      { params }
    );
    return response.result;
  },

  getProtocolTemplate: async (id: string): Promise<ProtocolTemplate> => {
    const response = await apiClient.get<ProtocolTemplate>(
      EXTERNAL_AGENT_ENDPOINTS.PROTOCOL_TEMPLATE_DETAIL(id)
    );
    return response.result;
  },

  createProtocolTemplate: async (data: Omit<ProtocolTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProtocolTemplate> => {
    const response = await apiClient.post<ProtocolTemplate>(
      EXTERNAL_AGENT_ENDPOINTS.PROTOCOL_TEMPLATES,
      data
    );
    return response.result;
  },

  updateProtocolTemplate: async (id: string, data: Partial<ProtocolTemplate>): Promise<ProtocolTemplate> => {
    const response = await apiClient.put<ProtocolTemplate>(
      EXTERNAL_AGENT_ENDPOINTS.PROTOCOL_TEMPLATE_DETAIL(id),
      data
    );
    return response.result;
  },

  deleteProtocolTemplate: async (id: string): Promise<void> => {
    await apiClient.delete(EXTERNAL_AGENT_ENDPOINTS.PROTOCOL_TEMPLATE_DETAIL(id));
  },
};
