import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminAgentRoleService } from '../services/agent-role.service';
import {
  AgentRoleDetail,
  CreateAgentRoleParams,
  UpdateAgentRoleParams,
  AgentRoleQueryParams,
} from '../types/agent-role.types';

// Query keys
export const ADMIN_AGENT_ROLE_QUERY_KEYS = {
  all: ['admin', 'agent-role'] as const,
  lists: () => [...ADMIN_AGENT_ROLE_QUERY_KEYS.all, 'list'] as const,
  list: (params: AgentRoleQueryParams) => [...ADMIN_AGENT_ROLE_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_AGENT_ROLE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_AGENT_ROLE_QUERY_KEYS.details(), id] as const,
  trash: () => [...ADMIN_AGENT_ROLE_QUERY_KEYS.all, 'trash'] as const,
  trashList: (params: AgentRoleQueryParams) =>
    [...ADMIN_AGENT_ROLE_QUERY_KEYS.trash(), params] as const,
};

/**
 * Hook để lấy danh sách agent role
 */
export const useAdminAgentRoles = (params: AgentRoleQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.list(params),
    queryFn: () => adminAgentRoleService.getAgentRoles(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy danh sách agent role đã xóa
 */
export const useAdminAgentRolesTrash = (params: AgentRoleQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.trashList(params),
    queryFn: () => adminAgentRoleService.getDeletedAgentRoles(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết agent role
 */
export const useAdminAgentRoleDetail = (id: string) => {
  return useQuery<AgentRoleDetail>({
    queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.detail(id),
    queryFn: () => adminAgentRoleService.getAgentRoleById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo agent role mới
 */
export const useCreateAdminAgentRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAgentRoleParams) => adminAgentRoleService.createAgentRole(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent role
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật agent role
 */
export const useUpdateAdminAgentRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentRoleParams }) =>
      adminAgentRoleService.updateAgentRole(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết agent role
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa agent role
 */
export const useDeleteAdminAgentRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentRoleService.deleteAgentRole(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent role
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách trash
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.trash(),
      });
    },
  });
};

/**
 * Hook để khôi phục agent role đã xóa
 */
export const useRestoreAdminAgentRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentRoleService.restoreAgentRole(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent role
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách trash
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_ROLE_QUERY_KEYS.trash(),
      });
    },
  });
};
