import { z } from 'zod';
import {
  MultiAgentSystemStatusEnum,
  MultiAgentSystemSortBy,
  SortDirection,
} from '../types/multi-agent-system.types';

export const createMultiAgentSystemSchema = z.object({
  name: z
    .string()
    .min(1, '<PERSON><PERSON><PERSON> hệ thống là bắt buộc')
    .max(255, '<PERSON>ê<PERSON> hệ thống không được quá 255 ký tự'),
  description: z.string().optional(),
  configuration: z.record(z.unknown()),
  status: z
    .nativeEnum(MultiAgentSystemStatusEnum)
    .optional()
    .default(MultiAgentSystemStatusEnum.DRAFT),
});

export const updateMultiAgentSystemSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên hệ thống là bắt buộc')
    .max(255, 'Tên hệ thống không được quá 255 ký tự')
    .optional(),
  description: z.string().optional(),
  configuration: z.record(z.unknown()).optional(),
  status: z.nativeEnum(MultiAgentSystemStatusEnum).optional(),
});

export const multiAgentSystemQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  status: z.nativeEnum(MultiAgentSystemStatusEnum).optional(),
  sortBy: z
    .nativeEnum(MultiAgentSystemSortBy)
    .optional()
    .default(MultiAgentSystemSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

export type CreateMultiAgentSystemFormData = z.infer<typeof createMultiAgentSystemSchema>;
export type UpdateMultiAgentSystemFormData = z.infer<typeof updateMultiAgentSystemSchema>;
export type MultiAgentSystemQueryFormData = z.infer<typeof multiAgentSystemQuerySchema>;
