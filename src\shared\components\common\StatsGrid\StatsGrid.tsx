import { LucideIcon } from 'lucide-react';
import { ResponsiveGrid } from '@/shared/components/common';
import { StatsCard } from '../StatsCard';

/**
 * Interface cho một item thống kê
 */
export interface StatsItem {
  /**
   * Tiêu đề của card
   */
  title: string;
  
  /**
   * Giá trị hiển thị
   */
  value: number | string;
  
  /**
   * Mô tả phụ
   */
  subtitle: string;
  
  /**
   * Icon hiển thị
   */
  icon: LucideIcon;
  
  /**
   * Màu của icon và text
   */
  color: 'blue' | 'orange' | 'green' | 'purple' | 'red' | 'yellow' | 'indigo' | 'pink';
}

/**
 * Props cho StatsGrid
 */
interface StatsGridProps {
  /**
   * Danh sách các items thống kê
   */
  items: StatsItem[];
  
  /**
   * Trạng thái loading
   */
  isLoading?: boolean;

  /**
   * <PERSON>ố cột tối đa cho mỗi breakpoint khi chatpanel đóng
   * @default { xs: 1, sm: 2, md: 4, lg: 4 }
   */
  maxColumns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Số cột tối đa cho mỗi breakpoint khi chatpanel mở
   * @default { xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }
   */
  maxColumnsWithChatPanel?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Class name bổ sung cho grid
   */
  className?: string;

  /**
   * Khoảng cách giữa các cards
   */
  gap?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
}

/**
 * Component grid hiển thị thống kê dùng chung theo quy tắc RedAI
 */
export function StatsGrid({
  items,
  isLoading = false,
  maxColumns = { xs: 1, sm: 2, md: 4, lg: 4 },
  maxColumnsWithChatPanel = { xs: 1, sm: 1, md: 2, lg: 3, xl: 3 },
  className = '',
  gap = 4,
}: StatsGridProps) {
  return (
    <ResponsiveGrid 
      maxColumns={maxColumns}
      maxColumnsWithChatPanel={maxColumnsWithChatPanel}
      className={className}
      gap={gap}
    >
      {items.map((item, index) => (
        <StatsCard
          key={index}
          title={item.title}
          value={item.value}
          subtitle={item.subtitle}
          icon={item.icon}
          color={item.color}
          isLoading={isLoading}
        />
      ))}
    </ResponsiveGrid>
  );
}

export default StatsGrid;
