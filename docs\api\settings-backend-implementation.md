# Settings Backend Implementation Guide

## Tổng quan

Tài liệu này mô tả chi tiết cách implement backend cho Settings API, bao gồm controllers, services, DTOs và database operations.

## Cấu trúc Backend

```
backend/
├── src/
│   ├── modules/
│   │   └── user/
│   │       ├── controllers/
│   │       │   └── user-settings.controller.ts
│   │       ├── services/
│   │       │   └── user-settings.service.ts
│   │       ├── dto/
│   │       │   ├── user-settings.dto.ts
│   │       │   └── chat-keywords.dto.ts
│   │       ├── entities/
│   │       │   ├── user-settings.entity.ts
│   │       │   ├── user-chat-keywords.entity.ts
│   │       │   └── user-preferences.entity.ts
│   │       └── repositories/
│   │           └── user-settings.repository.ts
```

## Entity Definitions

### UserSettings Entity

```typescript
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Index } from 'typeorm';
import { User } from './user.entity';

@Entity('user_settings')
@Index(['userId', 'settingKey'], { unique: true })
export class UserSettings {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'setting_key', length: 100 })
  settingKey: string;

  @Column({ name: 'setting_value', type: 'json' })
  settingValue: any;

  @Column({ 
    name: 'setting_type', 
    type: 'enum', 
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    default: 'string'
  })
  settingType: string;

  @Column({ name: 'is_encrypted', default: false })
  isEncrypted: boolean;

  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ 
    name: 'updated_at', 
    type: 'timestamp', 
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP'
  })
  updatedAt: Date;

  @ManyToOne(() => User, user => user.settings, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
```

### UserChatKeywords Entity

```typescript
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Index } from 'typeorm';
import { User } from './user.entity';

@Entity('user_chat_keywords')
@Index(['userId', 'keywordId'], { unique: true })
export class UserChatKeywords {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'keyword_id', length: 50 })
  keywordId: string;

  @Column({ length: 100 })
  keyword: string;

  @Column({ length: 255 })
  path: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ default: true })
  enabled: boolean;

  @Column({ name: 'is_custom', default: false })
  isCustom: boolean;

  @Column({ name: 'sort_order', default: 0 })
  sortOrder: number;

  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ 
    name: 'updated_at', 
    type: 'timestamp', 
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP'
  })
  updatedAt: Date;

  @ManyToOne(() => User, user => user.chatKeywords, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
```

### UserPreferences Entity

```typescript
import { Entity, PrimaryGeneratedColumn, Column, OneToOne, JoinColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('user_preferences')
export class UserPreferences {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'user_id', unique: true })
  userId: number;

  @Column({ 
    name: 'theme_mode', 
    type: 'enum', 
    enum: ['light', 'dark', 'custom', 'system'],
    default: 'system'
  })
  themeMode: string;

  @Column({ length: 50, default: 'Asia/Ho_Chi_Minh' })
  timezone: string;

  @Column({ length: 10, default: 'vi' })
  language: string;

  @Column({ name: 'date_format', length: 20, default: 'DD/MM/YYYY' })
  dateFormat: string;

  @Column({ name: 'time_format', length: 10, default: '24h' })
  timeFormat: string;

  @Column({ length: 10, default: 'VND' })
  currency: string;

  @Column({ type: 'json', nullable: true })
  notifications?: any;

  @Column({ name: 'ui_preferences', type: 'json', nullable: true })
  uiPreferences?: any;

  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ 
    name: 'updated_at', 
    type: 'timestamp', 
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP'
  })
  updatedAt: Date;

  @OneToOne(() => User, user => user.preferences, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
```

## DTOs

### UserSettingsDto

```typescript
import { IsString, IsOptional, IsBoolean, IsObject, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserSettingDto {
  @ApiProperty({ description: 'Setting key' })
  @IsString()
  settingKey: string;

  @ApiProperty({ description: 'Setting value' })
  settingValue: any;

  @ApiProperty({ description: 'Setting type', enum: ['string', 'number', 'boolean', 'object', 'array'] })
  @IsEnum(['string', 'number', 'boolean', 'object', 'array'])
  @IsOptional()
  settingType?: string;

  @ApiProperty({ description: 'Is encrypted', default: false })
  @IsBoolean()
  @IsOptional()
  isEncrypted?: boolean;
}

export class UpdateUserSettingDto {
  @ApiProperty({ description: 'Setting value' })
  settingValue: any;

  @ApiProperty({ description: 'Setting type', enum: ['string', 'number', 'boolean', 'object', 'array'] })
  @IsEnum(['string', 'number', 'boolean', 'object', 'array'])
  @IsOptional()
  settingType?: string;
}

export class UserSettingsResponseDto {
  @ApiProperty({ description: 'User preferences' })
  preferences: UserPreferencesDto;

  @ApiProperty({ description: 'Chat keywords' })
  chatKeywords: ChatKeywordDto[];

  @ApiProperty({ description: 'Custom settings' })
  customSettings: Record<string, any>;
}
```

### ChatKeywordDto

```typescript
import { IsString, IsOptional, IsBoolean, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateChatKeywordDto {
  @ApiProperty({ description: 'Keyword text' })
  @IsString()
  keyword: string;

  @ApiProperty({ description: 'Navigation path' })
  @IsString()
  path: string;

  @ApiProperty({ description: 'Keyword description', required: false })
  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdateChatKeywordDto {
  @ApiProperty({ description: 'Keyword text', required: false })
  @IsString()
  @IsOptional()
  keyword?: string;

  @ApiProperty({ description: 'Navigation path', required: false })
  @IsString()
  @IsOptional()
  path?: string;

  @ApiProperty({ description: 'Keyword description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Is enabled', required: false })
  @IsBoolean()
  @IsOptional()
  enabled?: boolean;

  @ApiProperty({ description: 'Sort order', required: false })
  @IsNumber()
  @IsOptional()
  sortOrder?: number;
}

export class ChatKeywordDto {
  @ApiProperty({ description: 'Keyword ID' })
  id: string;

  @ApiProperty({ description: 'Keyword text' })
  keyword: string;

  @ApiProperty({ description: 'Navigation path' })
  path: string;

  @ApiProperty({ description: 'Keyword description' })
  description?: string;

  @ApiProperty({ description: 'Is enabled' })
  enabled: boolean;

  @ApiProperty({ description: 'Is custom keyword' })
  isCustom: boolean;

  @ApiProperty({ description: 'Sort order' })
  sortOrder?: number;
}

export class ToggleChatKeywordDto {
  @ApiProperty({ description: 'Enable/disable keyword' })
  @IsBoolean()
  enabled: boolean;
}
```

### UserPreferencesDto

```typescript
import { IsString, IsOptional, IsEnum, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserPreferencesDto {
  @ApiProperty({ description: 'Theme mode', enum: ['light', 'dark', 'custom', 'system'] })
  @IsEnum(['light', 'dark', 'custom', 'system'])
  @IsOptional()
  themeMode?: string;

  @ApiProperty({ description: 'Timezone' })
  @IsString()
  @IsOptional()
  timezone?: string;

  @ApiProperty({ description: 'Language code' })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiProperty({ description: 'Date format' })
  @IsString()
  @IsOptional()
  dateFormat?: string;

  @ApiProperty({ description: 'Time format', enum: ['12h', '24h'] })
  @IsEnum(['12h', '24h'])
  @IsOptional()
  timeFormat?: string;

  @ApiProperty({ description: 'Currency code' })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({ description: 'Notification settings' })
  @IsObject()
  @IsOptional()
  notifications?: any;

  @ApiProperty({ description: 'UI preferences' })
  @IsObject()
  @IsOptional()
  uiPreferences?: any;
}

export class UserPreferencesDto {
  @ApiProperty({ description: 'Theme mode' })
  themeMode: string;

  @ApiProperty({ description: 'Timezone' })
  timezone: string;

  @ApiProperty({ description: 'Language code' })
  language: string;

  @ApiProperty({ description: 'Date format' })
  dateFormat: string;

  @ApiProperty({ description: 'Time format' })
  timeFormat: string;

  @ApiProperty({ description: 'Currency code' })
  currency: string;

  @ApiProperty({ description: 'Notification settings' })
  notifications?: any;

  @ApiProperty({ description: 'UI preferences' })
  uiPreferences?: any;
}
```

## Service Implementation

### UserSettingsService

```typescript
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserSettings } from '../entities/user-settings.entity';
import { UserChatKeywords } from '../entities/user-chat-keywords.entity';
import { UserPreferences } from '../entities/user-preferences.entity';
import { 
  CreateUserSettingDto, 
  UpdateUserSettingDto,
  CreateChatKeywordDto,
  UpdateChatKeywordDto,
  UpdateUserPreferencesDto 
} from '../dto/user-settings.dto';

@Injectable()
export class UserSettingsService {
  constructor(
    @InjectRepository(UserSettings)
    private userSettingsRepository: Repository<UserSettings>,
    @InjectRepository(UserChatKeywords)
    private chatKeywordsRepository: Repository<UserChatKeywords>,
    @InjectRepository(UserPreferences)
    private userPreferencesRepository: Repository<UserPreferences>,
  ) {}

  // Get all user settings
  async getUserSettings(userId: number) {
    const preferences = await this.getUserPreferences(userId);
    const chatKeywords = await this.getChatKeywords(userId);
    const customSettings = await this.getCustomSettings(userId);

    return {
      preferences,
      chatKeywords,
      customSettings,
    };
  }

  // Get user preferences
  async getUserPreferences(userId: number) {
    let preferences = await this.userPreferencesRepository.findOne({
      where: { userId }
    });

    if (!preferences) {
      // Create default preferences
      preferences = await this.createDefaultPreferences(userId);
    }

    return preferences;
  }

  // Update user preferences
  async updateUserPreferences(userId: number, updateDto: UpdateUserPreferencesDto) {
    let preferences = await this.userPreferencesRepository.findOne({
      where: { userId }
    });

    if (!preferences) {
      preferences = await this.createDefaultPreferences(userId);
    }

    Object.assign(preferences, updateDto);
    return await this.userPreferencesRepository.save(preferences);
  }

  // Create default preferences
  private async createDefaultPreferences(userId: number) {
    const preferences = this.userPreferencesRepository.create({
      userId,
      themeMode: 'system',
      timezone: 'Asia/Ho_Chi_Minh',
      language: 'vi',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      currency: 'VND',
    });

    return await this.userPreferencesRepository.save(preferences);
  }

  // Chat Keywords methods
  async getChatKeywords(userId: number) {
    const keywords = await this.chatKeywordsRepository.find({
      where: { userId },
      order: { sortOrder: 'ASC', createdAt: 'ASC' }
    });

    return {
      defaultKeywords: keywords.filter(k => !k.isCustom),
      customKeywords: keywords.filter(k => k.isCustom),
    };
  }

  async createChatKeyword(userId: number, createDto: CreateChatKeywordDto) {
    const keywordId = `custom_${Date.now()}`;
    
    const keyword = this.chatKeywordsRepository.create({
      userId,
      keywordId,
      keyword: createDto.keyword,
      path: createDto.path,
      description: createDto.description,
      enabled: true,
      isCustom: true,
    });

    return await this.chatKeywordsRepository.save(keyword);
  }

  async updateChatKeyword(userId: number, keywordId: string, updateDto: UpdateChatKeywordDto) {
    const keyword = await this.chatKeywordsRepository.findOne({
      where: { userId, keywordId }
    });

    if (!keyword) {
      throw new NotFoundException('Keyword not found');
    }

    Object.assign(keyword, updateDto);
    return await this.chatKeywordsRepository.save(keyword);
  }

  async deleteChatKeyword(userId: number, keywordId: string) {
    const keyword = await this.chatKeywordsRepository.findOne({
      where: { userId, keywordId }
    });

    if (!keyword) {
      throw new NotFoundException('Keyword not found');
    }

    if (!keyword.isCustom) {
      throw new BadRequestException('Cannot delete default keyword');
    }

    await this.chatKeywordsRepository.remove(keyword);
  }

  async toggleChatKeyword(userId: number, keywordId: string, enabled: boolean) {
    const keyword = await this.chatKeywordsRepository.findOne({
      where: { userId, keywordId }
    });

    if (!keyword) {
      throw new NotFoundException('Keyword not found');
    }

    keyword.enabled = enabled;
    return await this.chatKeywordsRepository.save(keyword);
  }

  async resetChatKeywords(userId: number) {
    // Delete all custom keywords
    await this.chatKeywordsRepository.delete({
      userId,
      isCustom: true
    });

    // Reset default keywords to enabled
    await this.chatKeywordsRepository.update(
      { userId, isCustom: false },
      { enabled: true }
    );

    return await this.getChatKeywords(userId);
  }

  // Custom settings methods
  async getCustomSettings(userId: number) {
    const settings = await this.userSettingsRepository.find({
      where: { userId }
    });

    const customSettings: Record<string, any> = {};
    settings.forEach(setting => {
      customSettings[setting.settingKey] = setting.settingValue;
    });

    return customSettings;
  }

  async updateSetting(userId: number, settingKey: string, updateDto: UpdateUserSettingDto) {
    let setting = await this.userSettingsRepository.findOne({
      where: { userId, settingKey }
    });

    if (!setting) {
      setting = this.userSettingsRepository.create({
        userId,
        settingKey,
        settingValue: updateDto.settingValue,
        settingType: updateDto.settingType || 'string',
      });
    } else {
      setting.settingValue = updateDto.settingValue;
      if (updateDto.settingType) {
        setting.settingType = updateDto.settingType;
      }
    }

    return await this.userSettingsRepository.save(setting);
  }
}
```
