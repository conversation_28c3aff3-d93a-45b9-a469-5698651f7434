import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';

import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import {
  useSmsProviders,
  useSmsIntegration,
} from '../sms/hooks';
import {
  SmsProviderConfig,
  SmsProviderQueryParams,
  SmsProviderFormData,
} from '../sms/types';

import SmsProviderForm from '../sms/components/SmsProviderForm';

/**
 * Trang quản lý SMS Integration
 */
const SMSIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [smsProviders, setSmsProviders] = useState<SmsProviderConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const [providerToDelete, setProviderToDelete] = useState<SmsProviderConfig | null>(null);
  const [providerToEdit, setProviderToEdit] = useState<SmsProviderConfig | null>(null);
  const [providerToView, setProviderToView] = useState<SmsProviderConfig | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sử dụng hook animation cho form tạo mới
  const { isVisible: isCreateFormVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const { isVisible: isEditFormVisible, showForm: showEditForm, hideForm: hideEditForm } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const { isVisible: isViewFormVisible, showForm: showViewForm, hideForm: hideViewForm } = useSlideForm();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((provider: SmsProviderConfig) => {
    setProviderToDelete(provider);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hiển thị form chỉnh sửa
  const handleShowEditForm = useCallback((provider: SmsProviderConfig) => {
    setProviderToEdit(provider);
    showEditForm();
  }, [showEditForm]);

  // Xử lý hiển thị form xem chi tiết
  const handleShowViewForm = useCallback((provider: SmsProviderConfig) => {
    setProviderToView(provider);
    showViewForm();
  }, [showViewForm]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<SmsProviderConfig>[]>(() => {
    const allColumns: TableColumn<SmsProviderConfig>[] = [
      {
        key: 'name',
        title: t('admin:integration.sms.list.columns.name'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
      },
      {
        key: 'displayName',
        title: t('admin:integration.sms.list.columns.displayName'),
        dataIndex: 'displayName',
        width: '20%',
        sortable: true,
      },
      {
        key: 'type',
        title: t('admin:integration.sms.list.columns.type'),
        dataIndex: 'type',
        width: '15%',
        sortable: true,
        render: (value: unknown) => (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {String(value).toUpperCase()}
          </span>
        ),
      },
      {
        key: 'status',
        title: t('admin:integration.sms.list.columns.status'),
        dataIndex: 'status',
        width: '15%',
        render: (value: unknown) => {
          const status = String(value);
          const statusColors = {
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-gray-100 text-gray-800',
            error: 'bg-red-100 text-red-800',
            testing: 'bg-yellow-100 text-yellow-800',
            pending: 'bg-blue-100 text-blue-800',
          };
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
            }`}>
              {t(`admin:integration.sms.status.${status}`, status)}
            </span>
          );
        },
      },
      {
        key: 'isDefault',
        title: t('admin:integration.sms.list.columns.default'),
        dataIndex: 'isDefault',
        width: '10%',
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {value ? t('common:yes') : t('common:no')}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('admin:integration.sms.list.columns.actions'),
        render: (_: unknown, record: SmsProviderConfig) => {
          const menuItems = [
            {
              label: t('admin:integration.sms.actions.edit'),
              icon: 'edit',
              onClick: () => handleShowEditForm(record),
            },
            {
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('admin:integration.sms.actions.delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowEditForm, handleShowViewForm]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<SmsProviderConfig, SmsProviderQueryParams>({
    columns,
    filterOptions: [],
    showDateFilter: false,
    createQueryParams: params => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
    }),
  });

  const dataTable = useDataTable(dataTableConfig);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<SmsProviderQueryParams>(() => {
    const params: SmsProviderQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm || undefined,
    };

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Hooks để gọi API
  const {
    data: smsProvidersData,
    isLoading: isLoadingProviders,
    error: providersError,
  } = useSmsProviders(queryParams);

  const {
    createProvider: createProviderMutation,
    updateProvider: updateProviderMutation,
    deleteProvider: deleteProviderMutation,
  } = useSmsIntegration();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (smsProvidersData?.items) {
      setSmsProviders(smsProvidersData.items || []);
      setTotalItems(smsProvidersData.total || 0);
    }

    setIsLoading(isLoadingProviders);
  }, [smsProvidersData, providersError, isLoadingProviders]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback(
    (term: string) => {
      dataTable.tableData.handleSearch(term);
    },
    [dataTable.tableData]
  );

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback(
    (column: string | null, order: SortOrder) => {
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProviderToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!providerToDelete) return;

    try {
      await deleteProviderMutation.mutateAsync(providerToDelete.id);
      setShowDeleteConfirm(false);
      setProviderToDelete(null);
    } catch (error) {
      console.error('Error deleting SMS provider:', error);
    }
  }, [providerToDelete, deleteProviderMutation]);

  // Xử lý submit form tạo mới
  const handleSubmitCreateProvider = useCallback(
    async (values: SmsProviderFormData) => {
      try {
        setIsSubmitting(true);
        await createProviderMutation.mutateAsync(values);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating SMS provider:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createProviderMutation, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditProvider = useCallback(
    async (values: SmsProviderFormData) => {
      if (!providerToEdit) return;

      try {
        setIsSubmitting(true);
        await updateProviderMutation.mutateAsync({
          id: providerToEdit.id,
          data: values,
        });
        hideEditForm();
        setProviderToEdit(null);
      } catch (error) {
        console.error('Error updating SMS provider:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [providerToEdit, updateProviderMutation, hideEditForm]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setProviderToEdit(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setTimeout(() => {
      setProviderToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<SmsProviderConfig>[]>(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns([
        { id: 'all', label: t('common:all'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-4">
        <div>
          {/* MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <SmsProviderForm
            onSubmit={handleSubmitCreateProvider}
            onCancel={hideCreateForm}
            loading={isSubmitting}
            mode="create"
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {providerToEdit && (
            <SmsProviderForm
              initialData={providerToEdit}
              onSubmit={handleSubmitEditProvider}
              onCancel={handleCloseEditForm}
              loading={isSubmitting}
              mode="edit"
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {providerToView && (
            <SmsProviderForm
              initialData={providerToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              loading={false}
              mode="edit"
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<SmsProviderConfig>
            columns={filteredColumns}
            data={smsProviders}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.sms.confirmations.deleteTitle')}
        message={t('admin:integration.sms.confirmations.delete')}
        itemName={providerToDelete?.name}
      />
    </div>
  );
};

export default SMSIntegrationPage;
