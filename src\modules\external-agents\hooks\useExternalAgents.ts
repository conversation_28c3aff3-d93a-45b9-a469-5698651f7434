import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { externalAgentService } from '../services';
import { EXTERNAL_AGENT_QUERY_KEYS } from '../constants';
import {
  ExternalAgent,
  ExternalAgentQueryDto,
  ExternalAgentCreateDto,
  ExternalAgentUpdateDto,
  ExternalAgentListResponse,
} from '../types';

// Get external agents list
export const useExternalAgents = (params?: ExternalAgentQueryDto) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.LIST(params || {}),
    queryFn: () => externalAgentService.getExternalAgents(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Create external agent mutation
export const useCreateExternalAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ExternalAgentCreateDto) => externalAgentService.createExternalAgent(data),
    onSuccess: () => {
      // Invalidate and refetch agents list
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
      });
    },
  });
};

// Update external agent mutation
export const useUpdateExternalAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ExternalAgentUpdateDto }) =>
      externalAgentService.updateExternalAgent(id, data),
    onSuccess: (updatedAgent) => {
      // Update specific agent in cache
      queryClient.setQueryData(
        EXTERNAL_AGENT_QUERY_KEYS.DETAIL(updatedAgent.id),
        updatedAgent
      );

      // Invalidate agents list to reflect changes
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
      });
    },
  });
};

// Delete external agent mutation
export const useDeleteExternalAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => externalAgentService.deleteExternalAgent(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.DETAIL(deletedId),
      });

      // Invalidate agents list
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
      });
    },
  });
};

// Bulk update status mutation
export const useBulkUpdateStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ ids, status }: { ids: string[]; status: string }) =>
      externalAgentService.bulkUpdateStatus(ids, status),
    onSuccess: () => {
      // Invalidate all agent queries
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
      });
    },
  });
};

// Bulk delete mutation
export const useBulkDeleteAgents = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => externalAgentService.bulkDelete(ids),
    onSuccess: (_, deletedIds) => {
      // Remove deleted agents from cache
      deletedIds.forEach(id => {
        queryClient.removeQueries({
          queryKey: EXTERNAL_AGENT_QUERY_KEYS.DETAIL(id),
        });
      });

      // Invalidate agents list
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
      });
    },
  });
};

// Custom hook for optimistic updates
export const useOptimisticAgentUpdate = () => {
  const queryClient = useQueryClient();

  const updateAgentOptimistically = (id: string, updates: Partial<ExternalAgentUpdateDto>) => {
    queryClient.setQueryData(
      EXTERNAL_AGENT_QUERY_KEYS.DETAIL(id),
      (oldData: ExternalAgent | undefined) => oldData ? { ...oldData, ...updates } : oldData
    );

    // Also update in lists
    queryClient.setQueriesData(
      { queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL },
      (oldData: ExternalAgentListResponse | undefined) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          items: oldData.items.map(agent =>
            agent.id === id ? { ...agent, ...updates } : agent
          ),
        };
      }
    );
  };

  return { updateAgentOptimistically };
};
