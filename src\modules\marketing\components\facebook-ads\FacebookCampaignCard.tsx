import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Badge,
  Modal,
  Progress,
  Tooltip,
} from '@/shared/components/common';

interface FacebookCampaignCardProps {
  /**
   * Facebook Campaign data
   */
  campaign: {
    id: string;
    campaignId: string;
    name: string;
    objective: string;
    status: 'active' | 'paused' | 'archived';
    budget: number;
    budgetType: 'daily' | 'lifetime';
    spend: number;
    impressions: number;
    clicks: number;
    ctr: number;
    cpc: number;
    startDate: string;
    endDate?: string;
    accountId: string;
    accountName: string;
  };
  
  /**
   * Show detailed metrics
   */
  showMetrics?: boolean;
  
  /**
   * Callback khi view campaign
   */
  onView?: (campaignId: string) => void;
  
  /**
   * Callback khi edit campaign
   */
  onEdit?: (campaignId: string) => void;
  
  /**
   * Callback khi toggle campaign status
   */
  onToggle?: (campaignId: string, currentStatus: string) => void;
  
  /**
   * Callback khi duplicate campaign
   */
  onDuplicate?: (campaignId: string) => void;
  
  /**
   * Loading state
   */
  isLoading?: boolean;
}

/**
 * Facebook Campaign Card Component
 * Hiển thị thông tin chiến dịch Facebook Ads dưới dạng card
 */
const FacebookCampaignCard: React.FC<FacebookCampaignCardProps> = ({
  campaign,
  showMetrics = true,
  onView,
  onEdit,
  onToggle,
  onDuplicate,
  isLoading = false,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const getStatusConfig = (status: string) => {
    const configs = {
      active: {
        label: t('common:status.active', 'Hoạt động'),
        variant: 'success' as const,
        icon: 'play',
      },
      paused: {
        label: t('common:status.paused', 'Tạm dừng'),
        variant: 'warning' as const,
        icon: 'pause',
      },
      archived: {
        label: t('common:status.archived', 'Lưu trữ'),
        variant: 'secondary' as const,
        icon: 'archive',
      },
    };
    return configs[status as keyof typeof configs] || configs.paused;
  };

  const getObjectiveLabel = (objective: string) => {
    const objectives: Record<string, string> = {
      CONVERSIONS: t('marketing:facebookAds.objectives.conversions', 'Chuyển đổi'),
      TRAFFIC: t('marketing:facebookAds.objectives.traffic', 'Lưu lượng'),
      BRAND_AWARENESS: t('marketing:facebookAds.objectives.brandAwareness', 'Nhận diện thương hiệu'),
      REACH: t('marketing:facebookAds.objectives.reach', 'Tiếp cận'),
      ENGAGEMENT: t('marketing:facebookAds.objectives.engagement', 'Tương tác'),
      LEAD_GENERATION: t('marketing:facebookAds.objectives.leadGeneration', 'Thu thập khách hàng tiềm năng'),
      MESSAGES: t('marketing:facebookAds.objectives.messages', 'Tin nhắn'),
      VIDEO_VIEWS: t('marketing:facebookAds.objectives.videoViews', 'Lượt xem video'),
    };
    return objectives[objective] || objective;
  };

  const statusConfig = getStatusConfig(campaign.status);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const getBudgetProgress = () => {
    if (campaign.budgetType === 'daily') {
      // For daily budget, show today's spend vs daily budget
      return Math.min((campaign.spend / campaign.budget) * 100, 100);
    } else {
      // For lifetime budget, show total spend vs lifetime budget
      return Math.min((campaign.spend / campaign.budget) * 100, 100);
    }
  };

  const handleView = () => {
    onView?.(campaign.id);
  };

  const handleEdit = () => {
    onEdit?.(campaign.id);
  };

  const handleToggle = () => {
    onToggle?.(campaign.id, campaign.status);
  };

  const handleDuplicate = () => {
    onDuplicate?.(campaign.id);
  };

  return (
    <>
      <Card className={`transition-all duration-200 hover:shadow-md ${isLoading ? 'opacity-50' : ''}`}>
        <div className="p-4">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <Icon name="campaign" className="text-primary flex-shrink-0" />
                <Typography variant="h6" className="font-semibold truncate">
                  {campaign.name}
                </Typography>
                <Badge variant={statusConfig.variant}>
                  <Icon name={statusConfig.icon} size="xs" className="mr-1" />
                  {statusConfig.label}
                </Badge>
              </div>
              
              <Typography variant="caption" className="text-muted-foreground block">
                {campaign.campaignId}
              </Typography>
              
              <Typography variant="body2" className="text-muted-foreground mt-1">
                {getObjectiveLabel(campaign.objective)}
              </Typography>
            </div>

            {/* Quick Actions */}
            <div className="flex space-x-1 flex-shrink-0">
              <Tooltip content={t('marketing:facebookAds.campaigns.actions.view', 'Xem chi tiết')}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleView}
                  disabled={isLoading}
                >
                  <Icon name="eye" size="sm" />
                </Button>
              </Tooltip>
              
              <Tooltip content={campaign.status === 'active' ? t('common:action.pause', 'Tạm dừng') : t('common:action.resume', 'Tiếp tục')}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleToggle}
                  disabled={isLoading}
                >
                  <Icon name={campaign.status === 'active' ? 'pause' : 'play'} size="sm" />
                </Button>
              </Tooltip>
            </div>
          </div>

          {/* Budget Info */}
          <div className="space-y-2 mb-3">
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                {campaign.budgetType === 'daily' 
                  ? t('marketing:facebookAds.budget.daily', 'Ngân sách hàng ngày')
                  : t('marketing:facebookAds.budget.lifetime', 'Ngân sách trọn đời')
                }:
              </Typography>
              <Typography variant="caption" className="font-medium">
                {formatCurrency(campaign.budget)}
              </Typography>
            </div>
            
            <div className="flex justify-between items-center">
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:facebookAds.metrics.spend', 'Đã chi tiêu')}:
              </Typography>
              <Typography variant="caption" className="font-medium">
                {formatCurrency(campaign.spend)}
              </Typography>
            </div>

            {/* Budget Progress */}
            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:facebookAds.budget.progress', 'Tiến độ')}:
                </Typography>
                <Typography variant="caption" className="font-medium">
                  {getBudgetProgress().toFixed(1)}%
                </Typography>
              </div>
              <Progress value={getBudgetProgress()} className="h-1" />
            </div>
          </div>

          {/* Metrics */}
          {showMetrics && (
            <div className="space-y-2 pt-3 border-t">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('marketing:facebookAds.metrics.impressions', 'Hiển thị')}:</span>
                  <span className="font-medium">{formatNumber(campaign.impressions)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('marketing:facebookAds.metrics.clicks', 'Nhấp')}:</span>
                  <span className="font-medium">{formatNumber(campaign.clicks)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">CTR:</span>
                  <span className="font-medium">{campaign.ctr.toFixed(2)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">CPC:</span>
                  <span className="font-medium">{formatCurrency(campaign.cpc)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2 pt-3 border-t mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              disabled={isLoading}
              className="flex-1"
            >
              <Icon name="edit" className="mr-2" />
              {t('common:button.edit', 'Chỉnh sửa')}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleDuplicate}
              disabled={isLoading}
              className="flex-1"
            >
              <Icon name="copy" className="mr-2" />
              {t('common:button.duplicate', 'Sao chép')}
            </Button>
          </div>

          {/* Campaign Period */}
          <div className="pt-2 mt-2 border-t">
            <Typography variant="caption" className="text-muted-foreground">
              {t('marketing:facebookAds.campaigns.period', 'Thời gian')}: {' '}
              {new Date(campaign.startDate).toLocaleDateString('vi-VN')}
              {campaign.endDate && (
                <> - {new Date(campaign.endDate).toLocaleDateString('vi-VN')}</>
              )}
            </Typography>
          </div>
        </div>
      </Card>

      {/* Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title={t('marketing:facebookAds.campaigns.details.title', 'Chi tiết chiến dịch')}
        size="lg"
      >
        <div className="space-y-4">
          {/* Campaign Info */}
          <Card variant="bordered" className="p-4">
            <Typography variant="h6" className="mb-3">
              {t('marketing:facebookAds.campaigns.details.info', 'Thông tin chiến dịch')}
            </Typography>
            
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:facebookAds.campaigns.name', 'Tên chiến dịch')}:
                </Typography>
                <Typography variant="body2" className="font-medium">
                  {campaign.name}
                </Typography>
              </div>
              
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  ID:
                </Typography>
                <Typography variant="body2" className="font-medium font-mono">
                  {campaign.campaignId}
                </Typography>
              </div>
              
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:facebookAds.campaigns.objective', 'Mục tiêu')}:
                </Typography>
                <Typography variant="body2" className="font-medium">
                  {getObjectiveLabel(campaign.objective)}
                </Typography>
              </div>
              
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:facebookAds.campaigns.status', 'Trạng thái')}:
                </Typography>
                <Badge variant={statusConfig.variant} className="mt-1">
                  <Icon name={statusConfig.icon} size="xs" className="mr-1" />
                  {statusConfig.label}
                </Badge>
              </div>
            </div>
          </Card>

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDetailsModal(false)}
            >
              {t('common:button.close', 'Đóng')}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default FacebookCampaignCard;
