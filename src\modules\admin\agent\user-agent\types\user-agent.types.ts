/**
 * Enum cho trạng thái user agent
 */
export enum UserAgentStatusEnum {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DEPRECATED = 'DEPRECATED',
}

/**
 * Enum cho các trường sắp xếp
 */
export enum UserAgentSortBy {
  ID = 'id',
  NAME = 'name',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  employeeId: string;
  name: string;
  avatar: string | null;
  date?: Date;
}

/**
 * Interface cho thông tin user
 */
export interface UserInfo {
  userId: string;
  name: string;
  email: string;
  avatar: string | null;
}

/**
 * Interface cho user agent trong danh sách
 */
export interface UserAgentListItem {
  id: string;
  name: string;
  avatar: string | null;
  status: UserAgentStatusEnum;
  userId: string;
  userName: string;
  model: string;
  createdAt: number;
}

/**
 * Interface cho thông tin chi tiết user agent
 */
export interface UserAgentDetail {
  id: string;
  name: string;
  avatar: string | null;
  status: UserAgentStatusEnum;
  description?: string;
  instruction?: string;
  modelConfig: Record<string, unknown>;
  user: UserInfo;
  created?: EmployeeInfo;
  updated?: EmployeeInfo;
  deleted?: EmployeeInfo;
}

/**
 * Interface cho tham số tạo user agent
 */
export interface CreateUserAgentParams {
  name: string;
  userId: string;
  avatarMimeType?: string;
  description?: string;
  instruction?: string;
  modelConfig: Record<string, unknown>;
  status?: UserAgentStatusEnum;
}

/**
 * Interface cho tham số cập nhật user agent
 */
export interface UpdateUserAgentParams {
  name?: string;
  avatarMimeType?: string;
  description?: string;
  instruction?: string;
  modelConfig?: Record<string, unknown>;
  status?: UserAgentStatusEnum;
}

/**
 * Interface cho tham số query
 */
export interface UserAgentQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: UserAgentStatusEnum;
  userId?: string;
  sortBy?: UserAgentSortBy;
  sortDirection?: SortDirection;
}
