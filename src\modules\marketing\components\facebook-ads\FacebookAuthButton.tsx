import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Icon,
  Modal,
  Typography,
  Card,
  Badge,
  Avatar,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';

interface FacebookAuthButtonProps {
  /**
   * Redirect URI sau khi OAuth thành công
   */
  redirectUri?: string;
  
  /**
   * Facebook permissions cần yêu cầu
   */
  scopes?: string[];
  
  /**
   * Variant của button
   */
  variant?: 'primary' | 'outline' | 'secondary';
  
  /**
   * Size của button
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Callback khi authentication thành công
   */
  onAuthSuccess?: () => void;
  
  /**
   * Callback khi có lỗi
   */
  onAuthError?: (error: Error) => void;
  
  /**
   * Hiển thị thông tin user khi đã authenticated
   */
  showUserInfo?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Facebook Authentication Button Component
 * Button để kết nối/ngắt kết nối Facebook account
 */
const FacebookAuthButton: React.FC<FacebookAuthButtonProps> = ({
  redirectUri = window.location.origin + window.location.pathname,
  scopes = ['ads_management', 'ads_read', 'business_management'],
  variant = 'primary',
  size = 'md',
  onAuthSuccess,
  onAuthError,
  showUserInfo = true,
  className,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [showUserModal, setShowUserModal] = useState(false);
  
  const {
    isAuthenticated,
    user,
    adAccounts,
    isLoading,
    isAuthenticating,
    isLoggingOut,
    error,
    authError,
    startAuth,
    logout,
  } = useFacebookAuth();

  const handleConnect = async () => {
    try {
      await startAuth(redirectUri, scopes);
      onAuthSuccess?.();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Authentication failed');
      onAuthError?.(error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await logout();
      setShowUserModal(false);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Logout failed');
      onAuthError?.(error);
    }
  };

  // Show error state
  if (error || authError) {
    return (
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size={size}
          onClick={handleConnect}
          isLoading={isAuthenticating}
          className={className}
        >
          <Icon name="alert-circle" className="mr-2 text-destructive" />
          {t('marketing:facebookAds.auth.reconnect', 'Kết nối lại Facebook')}
        </Button>
      </div>
    );
  }

  // Show authenticated state
  if (isAuthenticated && user) {
    return (
      <>
        <div className="flex items-center space-x-2">
          {showUserInfo && (
            <div className="flex items-center space-x-2">
              <Avatar
                src={user.picture?.data?.url}
                alt={user.name}
                size="sm"

              />
              <div className="hidden sm:block">
                <Typography variant="body2" className="font-medium">
                  {user.name}
                </Typography>
                <Typography variant="caption" className="text-muted-foreground">
                  {adAccounts.length} {t('marketing:facebookAds.auth.adAccounts', 'tài khoản quảng cáo')}
                </Typography>
              </div>
            </div>
          )}
          
          <Button
            variant="outline"
            size={size}
            onClick={() => setShowUserModal(true)}
            className={className}
          >
            <Icon name="facebook" className="mr-2" />
            {t('marketing:facebookAds.auth.connected', 'Đã kết nối')}
          </Button>
        </div>

        {/* User Info Modal */}
        <Modal
          isOpen={showUserModal}
          onClose={() => setShowUserModal(false)}
          title={t('marketing:facebookAds.auth.accountInfo', 'Thông tin tài khoản Facebook')}
          size="md"
        >
          <div className="space-y-4">
            {/* User Info */}
            <Card variant="bordered" className="p-4">
              <div className="flex items-center space-x-3">
                <Avatar
                  src={user.picture?.data?.url}
                  alt={user.name}
                  size="lg"

                />
                <div>
                  <Typography variant="h6">{user.name}</Typography>
                  {user.email && (
                    <Typography variant="body2" className="text-muted-foreground">
                      {user.email}
                    </Typography>
                  )}
                  <Typography variant="caption" className="text-muted-foreground">
                    ID: {user.id}
                  </Typography>
                </div>
              </div>
            </Card>

            {/* Ad Accounts */}
            <div>
              <Typography variant="h6" className="mb-2">
                {t('marketing:facebookAds.auth.adAccountsList', 'Tài khoản quảng cáo')}
              </Typography>
              
              {adAccounts.length === 0 ? (
                <Card variant="bordered" className="p-4 text-center">
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('marketing:facebookAds.auth.noAdAccounts', 'Không có tài khoản quảng cáo nào')}
                  </Typography>
                </Card>
              ) : (
                <div className="space-y-2">
                  {adAccounts.map((account) => (
                    <Card key={account.id} variant="bordered" className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Typography variant="body2" className="font-medium">
                            {account.name}
                          </Typography>
                          <Typography variant="caption" className="text-muted-foreground">
                            {account.accountId} • {account.currency}
                          </Typography>
                        </div>
                        <Badge
                          variant={account.accountStatus === 1 ? 'success' : 'secondary'}
                        >
                          {account.accountStatus === 1 
                            ? t('common:status.active', 'Hoạt động')
                            : t('common:status.inactive', 'Không hoạt động')
                          }
                        </Badge>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowUserModal(false)}
              >
                {t('common:button.close', 'Đóng')}
              </Button>
              <Button
                variant="danger"
                onClick={handleDisconnect}
                isLoading={isLoggingOut}
              >
                <Icon name="log-out" className="mr-2" />
                {t('marketing:facebookAds.auth.disconnect', 'Ngắt kết nối')}
              </Button>
            </div>
          </div>
        </Modal>
      </>
    );
  }

  // Show connect button
  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleConnect}
      isLoading={isLoading || isAuthenticating}
      className={className}
    >
      <Icon name="facebook" className="mr-2" />
      {t('marketing:facebookAds.auth.connect', 'Kết nối Facebook')}
    </Button>
  );
};

export default FacebookAuthButton;
