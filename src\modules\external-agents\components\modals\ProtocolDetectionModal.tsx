import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Button,
  Input,
  Typography,
  ProgressBar
} from '@/shared/components/common';
import ProtocolBadge from '../indicators/ProtocolBadge';
import ProtocolSelector from '../common/ProtocolSelector';
import { ProtocolType, ProtocolDetectionResult } from '../../types';
import { useProtocolDetectionWorkflow } from '../../hooks';

interface ProtocolDetectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (protocol: ProtocolType, result?: ProtocolDetectionResult) => void;
  initialEndpoint?: string;
  title?: string;
}

const ProtocolDetectionModal: React.FC<ProtocolDetectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  initialEndpoint = '',
  title,
}) => {
  const { t } = useTranslation(['external-agents']);
  const [endpoint, setEndpoint] = useState(initialEndpoint);
  const [selectedProtocol, setSelectedProtocol] = useState<ProtocolType | null>(null);
  const [detectionResult, setDetectionResult] = useState<ProtocolDetectionResult | null>(null);

  const { detectAndValidate, isWorking, error, reset } = useProtocolDetectionWorkflow();

  const handleDetect = async () => {
    if (!endpoint.trim()) return;

    try {
      const result = await detectAndValidate(endpoint);
      setDetectionResult(result.detection);
      if (result.detection.detectedProtocol) {
        setSelectedProtocol(result.detection.detectedProtocol);
      }
    } catch (err) {
      console.error('Detection failed:', err);
    }
  };

  const handleProtocolSelect = (protocol: ProtocolType) => {
    setSelectedProtocol(protocol);
  };

  const handleConfirm = () => {
    if (selectedProtocol) {
      onSelect(selectedProtocol, detectionResult || undefined);
      handleClose();
    }
  };

  const handleClose = () => {
    setEndpoint(initialEndpoint);
    setSelectedProtocol(null);
    setDetectionResult(null);
    reset();
    onClose();
  };

  const footer = (
    <div className="flex justify-end gap-3">
      <Button variant="outline" onClick={handleClose}>
        {t('external-agents:actions.cancel')}
      </Button>
      <Button
        variant="primary"
        onClick={handleConfirm}
        disabled={!selectedProtocol}
      >
        {t('external-agents:actions.confirm')}
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={title || t('external-agents:protocol.detectAndSelect')}
      size="xl"
      footer={footer}
    >

        <div className="space-y-6">
          {/* Endpoint Input */}
          <div className="space-y-3">
            <Typography variant="h4">
              {t('external-agents:protocol.enterEndpoint')}
            </Typography>
            <div className="flex gap-3">
              <div className="flex-1">
                <Input
                  value={endpoint}
                  onChange={(e) => setEndpoint(e.target.value)}
                  placeholder="https://api.example.com"

                />
              </div>
              <Button
                onClick={handleDetect}
                disabled={!endpoint.trim() || isWorking}
                variant="outline"
              >
                {isWorking 
                  ? t('external-agents:protocol.detecting')
                  : t('external-agents:protocol.detect')
                }
              </Button>
            </div>

            {isWorking && (
              <div className="space-y-2">
                <ProgressBar value={0} className="w-full" />
                <Typography variant="caption" className="text-muted-foreground">
                  {t('external-agents:protocol.analyzingEndpoint')}
                </Typography>
              </div>
            )}
          </div>

          {/* Detection Result */}
          {detectionResult && (
            <div className="p-4 bg-muted rounded-lg">
              <Typography variant="h4" className="mb-3">
                {t('external-agents:protocol.detectionResult')}
              </Typography>
              
              {detectionResult.detectedProtocol ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <ProtocolBadge protocol={detectionResult.detectedProtocol} />
                    <Typography variant="body2">
                      {t('external-agents:protocol.confidence')}: {Math.round(detectionResult.confidence * 100)}%
                    </Typography>
                  </div>

                  {detectionResult.supportedFeatures.length > 0 && (
                    <div>
                      <Typography variant="caption" className="text-muted-foreground">
                        {t('external-agents:protocol.supportedFeatures')}:
                      </Typography>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {detectionResult.supportedFeatures.map((feature, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-primary/10 text-primary text-xs rounded"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {detectionResult.recommendations.length > 0 && (
                    <div>
                      <Typography variant="caption" className="text-muted-foreground">
                        {t('external-agents:protocol.recommendations')}:
                      </Typography>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        {detectionResult.recommendations.map((rec, index) => (
                          <li key={index}>
                            <Typography variant="body2">{rec}</Typography>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ) : (
                <Typography variant="body2" className="text-muted-foreground">
                  {t('external-agents:protocol.noProtocolDetected')}
                </Typography>
              )}

              {detectionResult.errors && detectionResult.errors.length > 0 && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                  <Typography variant="caption" className="text-red-600 font-medium">
                    {t('external-agents:protocol.detectionErrors')}:
                  </Typography>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    {detectionResult.errors.map((err, index) => (
                      <li key={index}>
                        <Typography variant="body2" className="text-red-700">{err}</Typography>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <Typography variant="body2" className="text-red-700">
                {error.message}
              </Typography>
            </div>
          )}

          {/* Protocol Selector */}
          <div>
            <Typography variant="h4" className="mb-3">
              {t('external-agents:protocol.selectProtocol')}
            </Typography>
            <ProtocolSelector
              value={selectedProtocol || undefined}
              endpoint={endpoint}
              onSelect={handleProtocolSelect}
              showAutoDetect={false}
            />
          </div>
        </div>
    </Modal>
  );
};

export default ProtocolDetectionModal;
