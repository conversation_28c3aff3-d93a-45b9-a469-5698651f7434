import { ExternalAgentQueryDto, MessageQueryDto } from '../types';

export const EXTERNAL_AGENT_QUERY_KEYS = {
  ALL: ['external-agents'] as const,
  LIST: (params: ExternalAgentQueryDto) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'detail', id] as const,
  TEST_CONNECTION: (id: string) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'test', id] as const,
  PERFORMANCE: (id: string) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'performance', id] as const,
  MESSAGES: (params: MessageQueryDto) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'messages', params] as const,
  PROTOCOLS: ['protocols'] as const,
  PROTOCOL_TEMPLATES: ['protocol-templates'] as const,
  WEBHOOKS: ['webhooks'] as const,
  WEBHOOK_DELIVERIES: (webhookId: string) => ['webhook-deliveries', webhookId] as const,
} as const;
