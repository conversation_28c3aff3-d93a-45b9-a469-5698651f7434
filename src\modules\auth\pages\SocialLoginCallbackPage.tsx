import React, { useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Card, Typography, ResponsiveImage } from '@/shared/components/common';
import { useGoogleLogin, useFacebookLogin } from '../hooks/useAuthQuery';
import { useAuthCommon } from '@/shared/hooks';
import logoImage from '@/shared/assets/images/logo/logo.png';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { LoginResponse } from '../types/auth.types';
import { VerifyEmailResult, TwoFactorAuthResult } from '../types/auth-response.types';

/**
 * Trang xử lý callback từ đăng nhập bên thứ 3 (Google, Facebook)
 * Hiển thị loading và tự động xử lý đăng nhập
 */
const SocialLoginCallbackPage: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { setUserAuth } = useAuthCommon();

  // Sử dụng các hooks đăng nhập
  const googleLogin = useGoogleLogin();
  const facebookLogin = useFacebookLogin();

  /**
   * Xử lý khi đăng nhập thành công
   */
  const handleLoginSuccess = useCallback(
    (response: ApiResponseDto<unknown>) => {
      // Kiểm tra mã trạng thái
      const { code } = response;
      const result = response.result || {};

      if (code === 200) {
        // Đăng nhập thành công
        const loginResult = result as LoginResponse;
        setUserAuth({
          accessToken: loginResult.accessToken || '',
          expiresIn: loginResult.expiresIn,
          expiresAt: loginResult.expiresAt,
          user: loginResult.user,
        });

        // Chuyển hướng đến trang chính
        navigate('/', { replace: true });
      } else if (code === 202) {
        // Cần xác thực email
        const verifyResult = result as VerifyEmailResult;
        navigate('/auth/verify-email', {
          state: {
            verifyToken: verifyResult.verifyToken,
            expiresIn: verifyResult.expiresIn,
            info: verifyResult.info,
          },
          replace: true,
        });
      } else if (code === 203) {
        // Cần xác thực 2FA
        const twoFactorResult = result as TwoFactorAuthResult;
        navigate('/auth/two-factor', {
          state: {
            twoFactorToken: twoFactorResult.verifyToken, // Sử dụng verifyToken thay vì twoFactorToken
            expiresAt: twoFactorResult.expiresAt,
            enabledMethods: twoFactorResult.enabledMethods,
          },
          replace: true,
        });
      } else {
        // Trường hợp khác, chuyển hướng về trang đăng nhập
        navigate('/auth', { replace: true });
      }
    },
    [navigate, setUserAuth]
  );

  useEffect(() => {
    // Lấy các tham số từ URL
    const urlParams = new URLSearchParams(location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    // Nếu có code và state, tiến hành đăng nhập
    if (code && state) {
      const handleSocialLogin = async () => {
        try {
          // Xác định loại đăng nhập dựa vào state
          if (state === 'google') {
            // Đăng nhập bằng Google
            googleLogin.mutate(
              { code },
              {
                onSuccess: response => {
                  handleLoginSuccess(response);
                },
                onError: error => {
                  console.error('Google login failed:', error);
                  navigate('/auth', { replace: true });
                },
              }
            );
          } else if (state === 'facebook') {
            // Đăng nhập bằng Facebook
            facebookLogin.mutate(
              { code },
              {
                onSuccess: response => {
                  handleLoginSuccess(response);
                },
                onError: error => {
                  console.error('Facebook login failed:', error);
                  navigate('/auth', { replace: true });
                },
              }
            );
          } else {
            // State không hợp lệ, chuyển hướng về trang đăng nhập
            console.error('Invalid state parameter');
            navigate('/auth', { replace: true });
          }
        } catch (error) {
          console.error('Social login error:', error);
          navigate('/auth', { replace: true });
        }
      };

      handleSocialLogin();
    } else {
      // Không có code hoặc state, chuyển hướng về trang đăng nhập
      navigate('/auth', { replace: true });
    }
  }, [location, navigate, googleLogin, facebookLogin, handleLoginSuccess]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card variant="elevated" className="w-full max-w-md">
        <div className="flex flex-col items-center p-8">
          {/* Logo */}
          <div className="flex justify-center items-center w-full h-12 mb-8">
            <ResponsiveImage
              src={logoImage}
              alt="RedAI Logo"
              className="h-full object-contain max-w-[50%]"
            />
          </div>

          {/* Loading indicator */}
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
            <Typography variant="body1" align="center" className="mt-4">
              {t('auth.socialLogin.processing', 'Đang xử lý đăng nhập...')}
            </Typography>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SocialLoginCallbackPage;
