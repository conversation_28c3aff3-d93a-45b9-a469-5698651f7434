{"marketplace": {"title": "Marketplace Management", "description": "Manage products, orders, and shopping carts in the marketplace system.", "products": "Products", "productsDescription": "Manage products in the marketplace, including adding, editing, deleting, and approving products.", "totalProducts": "Total products", "manageProducts": "Manage products", "orders": "Orders", "ordersDescription": "Manage customer orders, track status, and process orders.", "totalOrders": "Total orders", "manageOrders": "Manage orders", "cartDescription": "View and manage customer shopping carts in the system.", "totalCarts": "Total carts", "manageCarts": "Manage carts", "product": {"addNew": "Add new product", "editProduct": "Edit product", "basicInfo": "Basic information", "priceInfo": "Price information", "mediaTypes": "Files & Images", "bulkUpdateStatus": "Update status", "confirmBulkDeleteMessage": "Are you sure you want to delete {{count}} selected products?", "confirmBulkStatusUpdateMessage": "Select new status for {{count}} selected products:", "selectStatus": "Select status", "publish": "Publish", "createSuccess": "Success", "createSuccessMessage": "Product has been created successfully", "createError": "Error", "createErrorMessage": "Unable to create product. Please try again.", "updateSuccess": "Success", "updateSuccessMessage": "Product has been updated successfully", "updateError": "Error", "updateErrorMessage": "Unable to update product. Please try again.", "publishSuccess": "Success", "publishSuccessMessage": "Product \"{{name}}\" has been published successfully", "publishError": "Error", "publishErrorMessage": "Unable to publish product. Please try again.", "bulkDeleteSuccess": "Success", "bulkDeleteSuccessMessage": "Successfully deleted {{count}} products", "bulkDeleteError": "Error", "bulkDeleteErrorMessage": "Unable to delete products. Please try again.", "bulkUpdateSuccess": "Success", "bulkUpdateSuccessMessage": "Updated status for {{count}} products", "bulkUpdateError": "Error", "bulkUpdateErrorMessage": "Unable to update product status. Please try again.", "table": {"name": "Product name", "category": "Category", "price": "Price", "status": "Status", "seller": "<PERSON><PERSON>", "actions": "Actions"}, "category": {"KNOWLEDGE_FILE": "Knowledge File", "AGENT": "Agent", "TEMPLATE": "Template", "OTHER": "Other"}, "form": {"name": "Product name", "namePlaceholder": "Enter product name", "description": "Description", "descriptionPlaceholder": "Enter product description", "category": "Product category", "categoryPlaceholder": "Select category", "sourceId": "Source ID", "sourceIdPlaceholder": "Search and select source", "listedPrice": "Listed price", "listedPricePlaceholder": "Enter listed price", "discountedPrice": "Discounted price", "discountedPricePlaceholder": "Enter discounted price", "images": "Product images", "imagesHelp": "Supported formats: JPG, PNG", "imagePlaceholder": "Drag and drop or click to upload product images", "imageSupport": "Support multiple images, formats: JPG, PNG", "selectedImages": "Selected {{count}} new images", "selectImages": "Select images", "userManual": "User manual", "userManualPlaceholder": "Enter user manual (if changed)", "userManualHelp": "Supported formats: PDF", "selectFiles": "Select files", "selectedFiles": "Selected files", "files": "files", "noFilesSelected": "No files selected", "removeAll": "Remove all", "detail": "Detailed documentation", "detailPlaceholder": "Enter detailed product information (if changed)", "detailHelp": "Supported formats: PDF", "selectFile": "Select file", "changeFile": "Change file", "additionalInfo": "Additional information", "publishAfterUpdate": "Publish after update"}, "validation": {"nameMin": "Product name must be at least 3 characters", "nameMax": "Product name must not exceed 500 characters", "descriptionRequired": "Product description is required", "listedPriceRequired": "Listed price is required", "listedPriceMin": "Listed price cannot be negative", "discountedPriceRequired": "Discounted price is required", "discountedPriceMin": "Discounted price cannot be negative", "categoryRequired": "Product category is required", "sourceIdRequired": "Source ID is required"}, "status": {"APPROVED": "Approved", "PENDING": "Pending", "REJECTED": "Rejected", "DRAFT": "Draft", "DELETED": "Deleted"}, "approve": "Approve", "reject": "Reject", "confirmDeleteMessage": "Are you sure you want to delete this product?", "confirmApproveMessage": "Are you sure you want to approve this product?", "rejectReason": "Rejection reason", "rejectReasonPlaceholder": "Enter reason for rejecting the product", "confirmRejectMessage": "Are you sure you want to reject this product?"}, "order": {"details": "Order details", "edit": "Edit order", "addNew": "Add new order", "info": "Order information", "table": {"id": "Order ID", "customer": "Customer", "total": "Total", "status": "Status", "type": "Type", "createdAt": "Created at", "actions": "Actions"}, "form": {"orderNumber": "Order number", "userId": "User ID", "status": "Status", "type": "Order type", "createdAt": "Created at", "totalPrice": "Total price"}, "item": {"product": "Product", "quantity": "Quantity", "price": "Price", "total": "Total"}, "items": {"empty": "No items"}, "status": {"PENDING": "Pending", "PROCESSING": "Processing", "COMPLETED": "Completed", "SHIPPED": "Shipped", "DELIVERED": "Delivered", "CANCELLED": "Cancelled", "FAILED": "Failed", "REFUNDED": "Refunded"}, "type": {"PURCHASE": "Purchase", "SUBSCRIPTION": "Subscription", "REFUND": "Refund"}, "confirmDeleteMessage": "Are you sure you want to delete this order?", "confirmCancelMessage": "Are you sure you want to cancel this order?", "cancelReason": "Cancellation reason", "cancelReasonPlaceholder": "Enter reason for cancelling the order"}, "cart": {"cart": "Shopping Cart", "details": "Cart details", "info": "Cart information", "allCarts": "All carts", "loading": "Loading...", "view": "View details", "edit": "Edit quantity", "delete": "Delete cart", "clearCart": "Clear cart", "moreActions": "More actions", "form": {"id": "Cart ID", "userId": "User ID", "userName": "User name", "userEmail": "Email", "totalItems": "Total items", "totalValue": "Total value", "createdAt": "Created at"}, "table": {"id": "ID", "customer": "Customer", "product": "Product", "price": "Unit price", "quantity": "Quantity", "total": "Total", "items": "Items"}, "items": {"items": "Cart items", "empty": "No items"}, "confirmDeleteMessage": "Are you sure you want to delete this cart?"}, "cartDetails": {"table": {"id": "Cart ID", "customer": "Customer", "product": "Product", "price": "Unit price", "quantity": "Quantity", "total": "Total", "items": "Items"}, "confirmDeleteMessage": "Are you sure you want to delete this cart?"}}}