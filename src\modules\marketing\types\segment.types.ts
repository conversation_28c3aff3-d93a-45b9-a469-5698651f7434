/**
 * Types for segment API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Segment status enum
 */
export enum SegmentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Condition operator enum
 */
export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  BETWEEN = 'between',
}

/**
 * Segment condition
 */
export interface SegmentCondition {
  id: string;
  field: string;
  operator: ConditionOperator;
  value: string | string[] | number | number[];
}

/**
 * Segment group
 */
export interface SegmentGroup {
  id: string;
  conditions: SegmentCondition[];
  logicalOperator: 'AND' | 'OR';
}

/**
 * Segment entity
 */
export interface Segment {
  id: number;
  name: string;
  description?: string;
  status: SegmentStatus;
  audienceId: number;
  audienceName: string;
  totalContacts: number;
  groups: SegmentGroup[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Create segment request
 */
export interface CreateSegmentRequest {
  name: string;
  description?: string;
  audienceId: number;
  status?: SegmentStatus;
  groups: Omit<SegmentGroup, 'id'>[];
}

/**
 * Update segment request
 */
export interface UpdateSegmentRequest {
  name?: string;
  description?: string;
  audienceId?: number;
  status?: SegmentStatus;
  groups?: Omit<SegmentGroup, 'id'>[];
}

/**
 * Segment stats
 */
export interface SegmentStats {
  segmentId: string;
  segmentName: string;
  totalAudiences: number;
  percentageOfTotal: number;
  updatedAt: number;
  // Legacy fields for backward compatibility
  totalContacts?: number;
  contactsInSegment?: number;
  percentage?: number;
  recentChanges?: {
    date: string;
    count: number;
  }[];
}

/**
 * Segment response
 */
export type SegmentResponse = Segment;

/**
 * Segment list response
 */
export type SegmentListResponse = ApiResponseDto<PaginatedResult<SegmentResponse>>;

/**
 * Segment detail response
 */
export type SegmentDetailResponse = ApiResponseDto<SegmentResponse>;

/**
 * Segment stats response
 */
export type SegmentStatsResponse = ApiResponseDto<SegmentStats>;

/**
 * Segment query params
 */
export interface SegmentQueryParams {
  search?: string;
  status?: SegmentStatus;
  audienceId?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
}
