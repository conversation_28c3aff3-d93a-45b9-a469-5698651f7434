import { messageApi } from '../api';
import {
  AgentMessage,
  MessageHistory,
  MessageQueryDto,
  MessageStats,
  MessageType,
} from '../types';

export const messageService = {
  // Get messages with enhanced filtering
  getMessages: async (params?: MessageQueryDto): Promise<MessageHistory> => {
    const queryParams = {
      page: 1,
      limit: 50,
      ...params,
    };

    // Validate parameters
    if (queryParams.limit && queryParams.limit > 1000) {
      throw new Error('Limit cannot exceed 1000');
    }

    if (queryParams.page && queryParams.page < 1) {
      queryParams.page = 1;
    }

    // Validate date range
    if (queryParams.startDate && queryParams.endDate) {
      const start = new Date(queryParams.startDate);
      const end = new Date(queryParams.endDate);
      
      if (start > end) {
        throw new Error('Start date cannot be after end date');
      }

      // Limit to 1 year range
      const oneYear = 365 * 24 * 60 * 60 * 1000;
      if (end.getTime() - start.getTime() > oneYear) {
        throw new Error('Date range cannot exceed 1 year');
      }
    }

    return messageApi.getMessages(queryParams);
  },

  // Get message history for specific agent
  getMessageHistory: async (agentId: string, params?: MessageQueryDto): Promise<MessageHistory> => {
    if (!agentId || agentId.trim() === '') {
      throw new Error('Agent ID is required');
    }

    const queryParams = {
      page: 1,
      limit: 50,
      ...params,
    };

    return messageApi.getMessageHistory(agentId, queryParams);
  },

  // Get message statistics with period validation
  getMessageStats: async (agentId: string, period?: { start: string; end: string }): Promise<MessageStats> => {
    if (!agentId || agentId.trim() === '') {
      throw new Error('Agent ID is required');
    }

    let validatedPeriod = period;

    // Default to last 30 days if no period provided
    if (!period) {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 30);
      
      validatedPeriod = {
        start: start.toISOString(),
        end: end.toISOString(),
      };
    }

    // Validate period if provided
    if (validatedPeriod) {
      const start = new Date(validatedPeriod.start);
      const end = new Date(validatedPeriod.end);
      
      if (start > end) {
        throw new Error('Start date cannot be after end date');
      }

      // Limit to 1 year range
      const oneYear = 365 * 24 * 60 * 60 * 1000;
      if (end.getTime() - start.getTime() > oneYear) {
        throw new Error('Statistics period cannot exceed 1 year');
      }
    }

    return messageApi.getMessageStats(agentId, validatedPeriod);
  },

  // Send message with validation
  sendMessage: async (agentId: string, content: Record<string, unknown>): Promise<AgentMessage> => {
    if (!agentId || agentId.trim() === '') {
      throw new Error('Agent ID is required');
    }

    if (!content || Object.keys(content).length === 0) {
      throw new Error('Message content is required');
    }

    // Validate content size (limit to 1MB)
    const contentSize = JSON.stringify(content).length;
    if (contentSize > 1024 * 1024) {
      throw new Error('Message content cannot exceed 1MB');
    }

    return messageApi.sendMessage(agentId, content);
  },

  // Delete message with validation
  deleteMessage: async (messageId: string): Promise<void> => {
    if (!messageId || messageId.trim() === '') {
      throw new Error('Message ID is required');
    }

    return messageApi.deleteMessage(messageId);
  },

  // Bulk delete messages with limits
  bulkDeleteMessages: async (messageIds: string[]): Promise<void> => {
    if (!messageIds || messageIds.length === 0) {
      throw new Error('At least one message ID is required');
    }

    if (messageIds.length > 100) {
      throw new Error('Cannot delete more than 100 messages at once');
    }

    return messageApi.bulkDeleteMessages(messageIds);
  },

  // Helper methods
  filterMessagesByType: (messages: AgentMessage[], type: MessageType): AgentMessage[] => {
    return messages.filter(message => message.type === type);
  },

  getMessagesByDateRange: async (
    agentId: string,
    startDate: string,
    endDate: string
  ): Promise<MessageHistory> => {
    return messageService.getMessageHistory(agentId, {
      startDate,
      endDate,
      limit: 1000,
    });
  },

  calculateResponseTime: (messages: AgentMessage[]): number => {
    const responseTimes = messages
      .filter(m => m.responseTime !== undefined)
      .map(m => m.responseTime!);
    
    if (responseTimes.length === 0) return 0;
    
    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  },
};
