import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon } from '@/shared/components/common';
import { ExternalAgentStatus } from '../../types';
import { getStatusLabel } from '../../utils';

interface StatusIndicatorProps {
  status: ExternalAgentStatus;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  showIcon?: boolean;
  className?: string;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'md',
  showLabel = true,
  showIcon = true,
  className,
}) => {
  const { t } = useTranslation(['external-agents']);

  const getStatusIcon = (status: ExternalAgentStatus) => {
    switch (status) {
      case ExternalAgentStatus.ACTIVE:
        return 'check-circle';
      case ExternalAgentStatus.INACTIVE:
        return 'x-circle';
      case ExternalAgentStatus.CONNECTING:
        return 'loader';
      case ExternalAgentStatus.ERROR:
        return 'alert-circle';
      case ExternalAgentStatus.MAINTENANCE:
        return 'tool';
      default:
        return 'help-circle';
    }
  };

  const getStatusColorClass = (status: ExternalAgentStatus) => {
    switch (status) {
      case ExternalAgentStatus.ACTIVE:
        return 'text-green-600 bg-green-50 border-green-200';
      case ExternalAgentStatus.INACTIVE:
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case ExternalAgentStatus.CONNECTING:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case ExternalAgentStatus.ERROR:
        return 'text-red-600 bg-red-50 border-red-200';
      case ExternalAgentStatus.MAINTENANCE:
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          container: 'px-2 py-1 text-xs',
          icon: 'w-3 h-3',
          text: 'text-xs',
        };
      case 'lg':
        return {
          container: 'px-4 py-2 text-base',
          icon: 'w-5 h-5',
          text: 'text-base',
        };
      default:
        return {
          container: 'px-3 py-1.5 text-sm',
          icon: 'w-4 h-4',
          text: 'text-sm',
        };
    }
  };

  const sizeClasses = getSizeClasses(size);
  const colorClasses = getStatusColorClass(status);
  const iconName = getStatusIcon(status);
  const label = getStatusLabel(status);

  const isAnimated = status === ExternalAgentStatus.CONNECTING;

  return (
    <div 
      className={`
        inline-flex items-center gap-2 rounded-full border font-medium
        ${sizeClasses.container}
        ${colorClasses}
        ${className}
      `}
    >
      {showIcon && (
        <Icon 
          name={iconName} 
          className={`
            ${sizeClasses.icon}
            ${isAnimated ? 'animate-spin' : ''}
          `}
        />
      )}
      {showLabel && (
        <Typography variant="caption" className={sizeClasses.text}>
          {t(`external-agents:status.${status}`, label)}
        </Typography>
      )}
    </div>
  );
};

export default StatusIndicator;
