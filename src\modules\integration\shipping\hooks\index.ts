import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ShippingProviderService, LocationService } from '../services';
import {
  ShippingProviderQueryParams,
  CreateShippingProviderDto,
  UpdateShippingProviderDto,
  TestShippingProviderDto,
  TestShippingProviderWithConfigDto,
  ShippingAddress,
} from '../types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Query Keys
 */
export const shippingProviderQueryKeys = {
  all: ['user', 'integration', 'shipping-provider'] as const,
  lists: () => [...shippingProviderQueryKeys.all, 'list'] as const,
  list: (params?: ShippingProviderQueryParams) => [...shippingProviderQueryKeys.lists(), params] as const,
  details: () => [...shippingProviderQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...shippingProviderQueryKeys.details(), id] as const,
  services: (id: number) => [...shippingProviderQueryKeys.all, 'services', id] as const,
};

export const locationQueryKeys = {
  all: ['location'] as const,
  provinces: () => [...locationQueryKeys.all, 'provinces'] as const,
  districts: (provinceId: number) => [...locationQueryKeys.all, 'districts', provinceId] as const,
  wards: (districtId: number) => [...locationQueryKeys.all, 'wards', districtId] as const,
};

/**
 * Hook to get shipping provider configurations
 */
export function useShippingProviderConfigurations(params?: ShippingProviderQueryParams) {
  return useQuery({
    queryKey: shippingProviderQueryKeys.list(params),
    queryFn: () => ShippingProviderService.getConfigurations(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get shipping provider configuration by ID
 */
export function useShippingProviderConfiguration(id: number) {
  return useQuery({
    queryKey: shippingProviderQueryKeys.detail(id),
    queryFn: () => ShippingProviderService.getConfiguration(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook to create shipping provider configuration
 */
export function useCreateShippingProviderConfiguration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateShippingProviderDto) => ShippingProviderService.createConfiguration(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: shippingProviderQueryKeys.lists() });
      NotificationUtil.success({
        message: 'Tạo cấu hình nhà vận chuyển thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Tạo cấu hình nhà vận chuyển thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to update shipping provider configuration
 */
export function useUpdateShippingProviderConfiguration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateShippingProviderDto }) =>
      ShippingProviderService.updateConfiguration(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: shippingProviderQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: shippingProviderQueryKeys.detail(id) });
      NotificationUtil.success({
        message: 'Cập nhật cấu hình nhà vận chuyển thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật cấu hình nhà vận chuyển thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to delete shipping provider configuration
 */
export function useDeleteShippingProviderConfiguration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ShippingProviderService.deleteConfiguration(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: shippingProviderQueryKeys.lists() });
      NotificationUtil.success({
        message: 'Xóa cấu hình nhà vận chuyển thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Xóa cấu hình nhà vận chuyển thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to test shipping provider configuration
 */
export function useTestShippingProviderConfiguration() {
  return useMutation({
    mutationFn: ({ id, testData }: { id: number; testData?: TestShippingProviderDto }) =>
      ShippingProviderService.testConfiguration(id, testData),
    onSuccess: (response) => {
      const result = response.result;
      if (result.success) {
        NotificationUtil.success({
          message: 'Kiểm tra kết nối nhà vận chuyển thành công!',
          title: result.message,
        });
      } else {
        NotificationUtil.warning({
          message: 'Kiểm tra kết nối nhà vận chuyển thất bại',
          title: result.message,
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Kiểm tra kết nối nhà vận chuyển thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to test shipping provider with configuration
 */
export function useTestShippingProviderWithConfiguration() {
  return useMutation({
    mutationFn: (data: TestShippingProviderWithConfigDto) =>
      ShippingProviderService.testWithConfiguration(data),
    onSuccess: (response) => {
      const result = response.result;
      if (result.success) {
        NotificationUtil.success({
          message: 'Kiểm tra cấu hình nhà vận chuyển thành công!',
          title: result.message,
        });
      } else {
        NotificationUtil.warning({
          message: 'Kiểm tra cấu hình nhà vận chuyển thất bại',
          title: result.message,
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Kiểm tra cấu hình nhà vận chuyển thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to set provider as default
 */
export function useSetShippingProviderAsDefault() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ShippingProviderService.setAsDefault(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: shippingProviderQueryKeys.lists() });
      NotificationUtil.success({
        message: 'Đặt nhà vận chuyển mặc định thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Đặt nhà vận chuyển mặc định thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to get shipping services
 */
export function useShippingServices(id: number) {
  return useQuery({
    queryKey: shippingProviderQueryKeys.services(id),
    queryFn: () => ShippingProviderService.getServices(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to calculate shipping rates
 */
export function useCalculateShippingRates() {
  return useMutation({
    mutationFn: ({ 
      id, 
      rateRequest 
    }: { 
      id: number; 
      rateRequest: {
        fromAddress: ShippingAddress;
        toAddress: ShippingAddress;
        weight: number;
        dimensions?: {
          length: number;
          width: number;
          height: number;
        };
      };
    }) => ShippingProviderService.calculateRates(id, rateRequest),
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Tính phí vận chuyển thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to get provinces
 */
export function useProvinces() {
  return useQuery({
    queryKey: locationQueryKeys.provinces(),
    queryFn: () => LocationService.getProvinces(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook to get districts
 */
export function useDistricts(provinceId: number) {
  return useQuery({
    queryKey: locationQueryKeys.districts(provinceId),
    queryFn: () => LocationService.getDistricts(provinceId),
    enabled: !!provinceId,
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Hook to get wards
 */
export function useWards(districtId: number) {
  return useQuery({
    queryKey: locationQueryKeys.wards(districtId),
    queryFn: () => LocationService.getWards(districtId),
    enabled: !!districtId,
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}
