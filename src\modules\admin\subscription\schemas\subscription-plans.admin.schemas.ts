import { z } from 'zod';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { PackageType } from '../types/subscription-plans.admin.types';

/**
 * Schema cho loại gói dịch vụ
 */
export const PackageTypeSchema = z.nativeEnum(PackageType);

/**
 * Schema cho thông tin gói dịch vụ
 */
export const SubscriptionPlanSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  packageType: PackageTypeSchema,
});

/**
 * Schema cho query danh sách gói dịch vụ
 */
export const GetSubscriptionPlansQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
  name: z.string().optional(),
  packageType: PackageTypeSchema.optional(),
});

/**
 * Schema cho tạo gói dịch vụ mới
 */
export const CreateSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Tên gói dịch vụ không được để trống'),
  description: z.string().min(1, 'Mô tả không được để trống'),
  packageType: PackageTypeSchema,
});

/**
 * Schema cho cập nhật gói dịch vụ
 */
export const UpdateSubscriptionPlanSchema = z.object({
  name: z.string().min(1, 'Tên gói dịch vụ không được để trống'),
  description: z.string().min(1, 'Mô tả không được để trống'),
  packageType: PackageTypeSchema,
});

/**
 * Schema cho thông tin phân trang
 */
export const PaginationMetaSchema = z.object({
  totalItems: z.number().min(0),
  itemCount: z.number().min(0),
  itemsPerPage: z.number().min(0),
  totalPages: z.number().min(0),
  currentPage: z.number().min(1),
});

/**
 * Schema cho kết quả phân trang
 */
export const PaginatedResultSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    items: z.array(itemSchema),
    meta: PaginationMetaSchema,
  });

/**
 * Schema cho phản hồi API
 */
export const ApiResponseSchema = <T extends z.ZodTypeAny>(resultSchema: T) =>
  z.object({
    code: z.number(),
    message: z.string(),
    result: resultSchema,
  });
