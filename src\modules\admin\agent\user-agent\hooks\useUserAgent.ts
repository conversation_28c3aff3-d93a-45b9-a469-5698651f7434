import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminUserAgentService } from '../services/user-agent.service';
import {
  UserAgentDetail,
  CreateUserAgentParams,
  UpdateUserAgentParams,
  UserAgentQueryParams,
} from '../types/user-agent.types';

// Query keys
export const ADMIN_USER_AGENT_QUERY_KEYS = {
  all: ['admin', 'user-agent'] as const,
  lists: () => [...ADMIN_USER_AGENT_QUERY_KEYS.all, 'list'] as const,
  list: (params: UserAgentQueryParams) => [...ADMIN_USER_AGENT_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_USER_AGENT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_USER_AGENT_QUERY_KEYS.details(), id] as const,
};

export const useAdminUserAgents = (params: UserAgentQueryParams) => {
  return useQuery({
    queryKey: ADMIN_USER_AGENT_QUERY_KEYS.list(params),
    queryFn: () => adminUserAgentService.getUserAgents(params),
    staleTime: 5 * 60 * 1000,
  });
};

export const useAdminUserAgentDetail = (id: string) => {
  return useQuery<UserAgentDetail>({
    queryKey: ADMIN_USER_AGENT_QUERY_KEYS.detail(id),
    queryFn: () => adminUserAgentService.getUserAgentById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateAdminUserAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserAgentParams) => adminUserAgentService.createUserAgent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_USER_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};

export const useUpdateAdminUserAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserAgentParams }) =>
      adminUserAgentService.updateUserAgent(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_USER_AGENT_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_USER_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};

export const useDeleteAdminUserAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminUserAgentService.deleteUserAgent(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_USER_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};
