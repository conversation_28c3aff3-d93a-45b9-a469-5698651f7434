import React from 'react';
import { Card, Typography, Button, Icon, EmptyState } from '@/shared/components/common';

interface EnterpriseStorageProviderListProps {
  onCreateNew?: () => void;
}

/**
 * Placeholder component for Enterprise Storage Provider List
 */
const EnterpriseStorageProviderList: React.FC<EnterpriseStorageProviderListProps> = ({ onCreateNew }) => {
  return (
    <div className="w-full space-y-6">
      <Card>
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <Typography variant="h3" className="mb-2">
                Danh sách Enterprise Storage
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                Quản lý các tích hợp enterprise storage
              </Typography>
            </div>

            <Button
              variant="primary"
              onClick={onCreateNew}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              Thêm Enterprise Storage
            </Button>
          </div>
        </div>
      </Card>

      <Card>
        <div className="p-8">
          <EmptyState
            icon="server"
            title="Chưa có enterprise storage nào"
            description="Bạn chưa thêm enterprise storage nào. Hãy thêm enterprise storage đầu tiên."
            actions={
              <Button
                variant="primary"
                onClick={onCreateNew}
                leftIcon={<Icon name="plus" size="sm" />}
              >
                Thêm enterprise storage đầu tiên
              </Button>
            }
          />
        </div>
      </Card>
    </div>
  );
};

export default EnterpriseStorageProviderList;
