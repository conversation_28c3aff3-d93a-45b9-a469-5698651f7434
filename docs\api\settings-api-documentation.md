# Chat Keywords API Documentation

## Tổng quan

Tài liệu này mô tả các API cần thiết để hỗ trợ tính năng **Cài đặt từ khóa ChatPanel** trong trang `/settings`. Tính năng này cho phép người dùng quản lý từ khóa để điều hướng nhanh trong ChatPanel.

## Cấu trúc Database

### Bảng `user_chat_keywords`

```sql
CREATE TABLE user_chat_keywords (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    keyword_id VARCHAR(50) NOT NULL,
    keyword VARCHAR(100) NOT NULL,
    path VARCHAR(255) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    is_custom BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY unique_user_keyword (user_id, keyword_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_keywords_user_id (user_id),
    INDEX idx_user_keywords_enabled (enabled)
);
```

## API Endpoints

### Chat Keywords Management

#### GET `/api/v1/user/settings/chat-keywords`
Lấy danh sách chat keywords

**Response:**
```json
{
  "success": true,
  "result": {
    "defaultKeywords": [
      {
        "id": "dashboard",
        "keyword": "dashboard",
        "path": "/dashboard",
        "description": "Trang tổng quan",
        "enabled": true,
        "isCustom": false
      }
    ],
    "customKeywords": [
      {
        "id": "custom_1640995200000",
        "keyword": "trang chủ",
        "path": "/dashboard",
        "description": "Từ khóa tùy chỉnh",
        "enabled": true,
        "isCustom": true
      }
    ]
  }
}
```

#### POST `/api/v1/user/settings/chat-keywords`
Thêm nhiều chat keywords mới cho một đường dẫn

**Request Body:**
```json
{
  "path": "/business/product",
  "keywords": ["sản phẩm", "product", "hàng hóa", "sp"],
  "description": "Từ khóa cho trang sản phẩm"
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "created": [
      {
        "id": "custom_1640995200000_0",
        "keyword": "sản phẩm",
        "path": "/business/product",
        "description": "Từ khóa cho trang sản phẩm",
        "enabled": true,
        "isCustom": true
      },
      {
        "id": "custom_1640995200000_1",
        "keyword": "product",
        "path": "/business/product",
        "description": "Từ khóa cho trang sản phẩm",
        "enabled": true,
        "isCustom": true
      }
    ]
  }
}
```

#### PUT `/api/v1/user/settings/chat-keywords/{keywordId}`
Cập nhật chat keyword

**Request Body:**
```json
{
  "keyword": "home page",
  "path": "/dashboard",
  "description": "Updated description",
  "enabled": true
}
```

#### DELETE `/api/v1/user/settings/chat-keywords/{keywordId}`
Xóa chat keyword (chỉ custom keywords)

#### PUT `/api/v1/user/settings/chat-keywords/{keywordId}/toggle`
Bật/tắt chat keyword

**Request Body:**
```json
{
  "enabled": false
}
```

#### POST `/api/v1/user/settings/chat-keywords/reset`
Reset về danh sách keywords mặc định

**Response:**
```json
{
  "success": true,
  "result": {
    "message": "Keywords đã được reset về mặc định",
    "defaultKeywords": [
      {
        "id": "home",
        "keyword": "trang chủ",
        "path": "/",
        "description": "Điều hướng về trang chủ",
        "enabled": true,
        "isCustom": false
      }
    ]
  }
}
```

#### GET `/api/v1/user/settings/chat-keywords/paths`
Lấy danh sách đường dẫn có sẵn để chọn

**Response:**
```json
{
  "success": true,
  "result": [
    {
      "value": "/",
      "label": "/ - Trang chủ"
    },
    {
      "value": "/business/product",
      "label": "/business/product - Sản phẩm"
    },
    {
      "value": "/business/customer",
      "label": "/business/customer - Khách hàng"
    },
    {
      "value": "/settings",
      "label": "/settings - Cài đặt"
    }
  ]
}
```

## Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 40001 | Invalid path | Đường dẫn không hợp lệ |
| 40002 | Keywords array is empty | Mảng keywords trống |
| 40003 | Keyword not found | Không tìm thấy keyword |
| 40004 | Keyword already exists | Keyword đã tồn tại cho đường dẫn này |
| 40005 | Cannot delete default keyword | Không thể xóa keyword mặc định |
| 40006 | Invalid keyword format | Format keyword không hợp lệ |
| 40007 | Path not found | Đường dẫn không tồn tại |
| 40008 | Too many keywords | Quá nhiều keywords cho một đường dẫn |

## Data Types

### CreateChatKeywordsDto
```typescript
interface CreateChatKeywordsDto {
  path: string;                    // Đường dẫn (bắt đầu bằng /)
  keywords: string[];              // Mảng từ khóa
  description?: string;            // Mô tả (tùy chọn)
}
```

### UpdateChatKeywordDto
```typescript
interface UpdateChatKeywordDto {
  keyword?: string;                // Từ khóa mới
  path?: string;                   // Đường dẫn mới
  description?: string;            // Mô tả mới
  enabled?: boolean;               // Trạng thái bật/tắt
}
```

### ChatKeywordDto
```typescript
interface ChatKeywordDto {
  id: string;                      // ID duy nhất
  keyword: string;                 // Từ khóa
  path: string;                    // Đường dẫn điều hướng
  description?: string;            // Mô tả
  enabled: boolean;                // Trạng thái bật/tắt
  isCustom: boolean;               // Có phải keyword tùy chỉnh
  sortOrder?: number;              // Thứ tự sắp xếp
  createdAt: string;               // Thời gian tạo
  updatedAt: string;               // Thời gian cập nhật
}
```

### PathOptionDto
```typescript
interface PathOptionDto {
  value: string;                   // Giá trị đường dẫn
  label: string;                   // Nhãn hiển thị
}
```

## Validation Rules

### Keywords
- Độ dài: 1-100 ký tự
- Không chứa ký tự đặc biệt: `<>{}[]|\"'`
- Không được trùng lặp trong cùng đường dẫn
- Tối đa 20 keywords cho một đường dẫn

### Path
- Phải bắt đầu bằng `/`
- Độ dài: 1-255 ký tự
- Phải tồn tại trong hệ thống routing

### Description
- Độ dài tối đa: 200 ký tự
- Có thể để trống

## Ví dụ sử dụng

### 1. Thêm keywords cho trang sản phẩm
```bash
POST /api/v1/user/settings/chat-keywords
Content-Type: application/json

{
  "path": "/business/product",
  "keywords": ["sản phẩm", "product", "hàng hóa", "sp"],
  "description": "Từ khóa điều hướng đến trang sản phẩm"
}
```

### 2. Lấy tất cả keywords
```bash
GET /api/v1/user/settings/chat-keywords
```

### 3. Tắt một keyword
```bash
PUT /api/v1/user/settings/chat-keywords/custom_1640995200000_0/toggle

{
  "enabled": false
}
```

### 4. Xóa keyword tùy chỉnh
```bash
DELETE /api/v1/user/settings/chat-keywords/custom_1640995200000_0
```

### 5. Reset về mặc định
```bash
POST /api/v1/user/settings/chat-keywords/reset
```

## Notes

- Tất cả API yêu cầu authentication
- Keywords mặc định không thể xóa, chỉ có thể tắt
- Keywords tùy chỉnh có thể xóa hoàn toàn
- Mỗi user có thể có tối đa 100 keywords tùy chỉnh
- Keywords được cache để tăng hiệu suất ChatPanel
