# Email Template Components

Tài liệu này mô tả các component mới được tạo để quản lý Email Template trong form tạo chiến dịch email.

## Tổng quan

Đã tách Email Template thành khối riêng với các tính năng:

1. **EmailTemplateSelector**: Component chọn template với hiển thị thông tin chi tiết
2. **TemplateVariableManager**: Quản lý biến trong template
3. **ExcelVariableImporter**: Import dữ liệu biến từ Excel

## Components

### 1. EmailTemplateSelector

Component chính để chọn email template với preview và quản lý biến.

```tsx
import { EmailTemplateSelector } from '@/modules/marketing';

<EmailTemplateSelector
  value={templateId}
  onChange={(templateId, templateData) => {
    // Handle template selection
  }}
  onVariablesChange={(variables) => {
    // Handle variables change
  }}
  disabled={false}
/>
```

**Props:**
- `value`: ID của template được chọn
- `onChange`: Callback khi chọn template
- `onVariablesChange`: Callback khi biến thay đổi
- `disabled`: Disable component

**Features:**
- Search và select template với pagination
- Hiển thị preview template (name, subject, content)
- Hiển thị danh sách biến trong template
- Button để mở Variable Manager

### 2. TemplateVariableManager

Component quản lý biến trong template với khả năng nhập tay và import Excel.

```tsx
import { TemplateVariableManager } from '@/modules/marketing';

<TemplateVariableManager
  template={selectedTemplate}
  variables={currentVariables}
  onChange={(variables) => {
    // Handle variables change
  }}
  onClose={() => {
    // Handle close
  }}
/>
```

**Props:**
- `template`: EmailTemplateDto object
- `variables`: Object chứa giá trị các biến
- `onChange`: Callback khi biến thay đổi
- `onClose`: Callback khi đóng manager

**Features:**
- Hiển thị danh sách tất cả biến trong template
- Input field cho từng biến với validation
- Export template Excel để điền dữ liệu
- Import dữ liệu từ Excel
- Validation cho biến bắt buộc

### 3. ExcelVariableImporter

Component import dữ liệu biến từ file Excel.

```tsx
import { ExcelVariableImporter } from '@/modules/marketing';

<ExcelVariableImporter
  template={selectedTemplate}
  onImport={(variables) => {
    // Handle imported variables
  }}
  onClose={() => {
    // Handle close
  }}
/>
```

**Props:**
- `template`: EmailTemplateDto object
- `onImport`: Callback khi import thành công
- `onClose`: Callback khi đóng importer

**Features:**
- Upload file Excel (.xlsx, .xls, .csv)
- Preview dữ liệu Excel
- Map cột Excel với biến template
- Auto-mapping dựa trên tên tương tự
- Download template Excel mẫu
- Validation mapping cho biến bắt buộc

## Cách sử dụng trong CreateEmailCampaignForm

```tsx
// Import component
import { EmailTemplateSelector } from './EmailTemplateSelector';

// State
const [selectedTemplateData, setSelectedTemplateData] = useState<EmailTemplateDto | null>(null);
const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});

// Handlers
const handleTemplateChange = useCallback((templateId: string, templateData?: EmailTemplateDto) => {
  setFormData(prev => ({ ...prev, templateId }));
  setSelectedTemplateData(templateData || null);
}, []);

const handleTemplateVariablesChange = useCallback((variables: Record<string, string>) => {
  setTemplateVariables(variables);
}, []);

// Trong JSX
<EmailTemplateSelector
  value={formData.templateId}
  onChange={handleTemplateChange}
  onVariablesChange={handleTemplateVariablesChange}
  disabled={createCampaign.isPending}
/>
```

## Cấu trúc dữ liệu

### EmailVariable
```typescript
interface EmailVariable {
  name: string;
  type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
  defaultValue?: string;
  required: boolean;
  description?: string;
}
```

### Template Variables
```typescript
// Object chứa giá trị các biến
const templateVariables: Record<string, string> = {
  'customer_name': 'John Doe',
  'product_name': 'iPhone 15',
  'discount_amount': '20'
};
```

## Excel Import Format

Template Excel sẽ có format:

| Variable Name | Current Value | Type | Required | Description |
|---------------|---------------|------|----------|-------------|
| customer_name |               | TEXT | Yes      | Tên khách hàng |
| product_name  |               | TEXT | Yes      | Tên sản phẩm |
| discount_amount | 10          | NUMBER | No     | Phần trăm giảm giá |

## Dependencies

- `xlsx`: Đã có sẵn trong project (version 0.18.5)
- `@types/xlsx`: Đã có sẵn trong devDependencies
- Các shared components: Card, Typography, Button, Input, Alert, Table, etc.

## Styling

Tất cả components tuân theo quy tắc RedAI:
- Sử dụng Typography thay vì HTML tags
- Responsive design
- Theme colors (bg-background, text-foreground)
- Consistent spacing và styling

## Testing

Để test các component:

1. Mở trang Email Campaigns
2. Click "Tạo chiến dịch mới"
3. Trong form, phần Email Template sẽ hiển thị component mới
4. Chọn một template để xem preview
5. Click "Quản lý biến" để test Variable Manager
6. Test import Excel với file mẫu

## Notes

- Component tự động load template detail khi chọn
- Variables được initialize với default values
- Auto-mapping trong Excel import dựa trên tên tương tự
- Validation cho biến bắt buộc
- Export template Excel để user có format chuẩn
