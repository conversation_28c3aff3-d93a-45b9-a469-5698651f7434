import React, { useState } from 'react';
import { SlideInForm } from '@/shared/components/common';
import GoogleCalendarList from '../components/GoogleCalendarList';
import GoogleCalendarForm from '../components/GoogleCalendarForm';

/**
 * Trang quản lý tích hợp Google Calendar
 */
const GoogleCalendarIntegrationPage: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
  };

  const handleCreateCancel = () => {
    setShowCreateForm(false);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <GoogleCalendarList onCreateNew={handleCreateNew} />

      {/* Create Form Slide-in */}
      <SlideInForm
        isVisible={showCreateForm}
      >
        <GoogleCalendarForm
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default GoogleCalendarIntegrationPage;
