/**
 * Enum cho các trường sắp xếp
 */
export enum TypeAgentSortBy {
  ID = 'id',
  NAME = 'name',
  ACTIVE = 'active',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  employeeId: string;
  name: string;
  avatar: string | null;
  date?: Date;
}

/**
 * Interface cho type agent trong danh sách
 */
export interface TypeAgentListItem {
  id: string;
  name: string;
  description: string | null;
  active: boolean;
  agentCount: number;
  createdAt: number;
}

/**
 * Interface cho thông tin chi tiết type agent
 */
export interface TypeAgentDetail {
  id: string;
  name: string;
  description: string | null;
  active: boolean;
  configuration: Record<string, unknown>;
  created?: EmployeeInfo;
  updated?: EmployeeInfo;
  deleted?: EmployeeInfo;
}

/**
 * Interface cho tham số tạo type agent
 */
export interface CreateTypeAgentParams {
  name: string;
  description?: string;
  configuration?: Record<string, unknown>;
  active?: boolean;
}

/**
 * Interface cho tham số cập nhật type agent
 */
export interface UpdateTypeAgentParams {
  name?: string;
  description?: string;
  configuration?: Record<string, unknown>;
  active?: boolean;
}

/**
 * Interface cho tham số query
 */
export interface TypeAgentQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  active?: boolean;
  sortBy?: TypeAgentSortBy;
  sortDirection?: SortDirection;
}
