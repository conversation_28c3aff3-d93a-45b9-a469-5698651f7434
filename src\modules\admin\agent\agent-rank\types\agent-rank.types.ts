/**
 * Enum cho các trường sắp xếp của agent rank
 */
export enum AgentRankSortBy {
  ID = 'id',
  NAME = 'name',
  MIN_EXP = 'minExp',
  MAX_EXP = 'maxExp',
  ACTIVE = 'active',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho agent rank trong danh sách
 */
export interface AgentRankListItem {
  /** ID của cấp bậc */
  id: number;
  /** Tên của cấp bậc */
  name: string;
  /** Mô tả chi tiết về cấp bậc */
  description: string | null;
  /** URL huy hiệu đại diện cho cấp bậc */
  badge: string;
  /** <PERSON>iể<PERSON> kinh nghiệm tối thiểu cần thiết để đạt cấp bậc */
  minExp: number;
  /** <PERSON>iể<PERSON> kinh nghiệm tối đa cho cấp bậc */
  maxExp: number;
  /** Trạng thái kích hoạt của cấp bậc */
  active: boolean;
}

/**
 * Interface cho thông tin chi tiết agent rank
 */
export interface AgentRankDetail {
  /** ID của cấp bậc */
  id: number;
  /** Tên của cấp bậc */
  name: string;
  /** Mô tả chi tiết về cấp bậc */
  description: string | null;
  /** URL huy hiệu đại diện cho cấp bậc */
  badge: string;
  /** Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc */
  minExp: number;
  /** Điểm kinh nghiệm tối đa cho cấp bậc */
  maxExp: number;
  /** Trạng thái kích hoạt của cấp bậc */
  active: boolean;
}

/**
 * Interface cho tham số tạo agent rank
 */
export interface CreateAgentRankParams {
  /** Tên của cấp bậc */
  name: string;
  /** Mô tả chi tiết về cấp bậc */
  description?: string;
  /** Tên file huy hiệu */
  fileName: string;
  /** Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc */
  minExp: number;
  /** Điểm kinh nghiệm tối đa cho cấp bậc */
  maxExp: number;
  /** Trạng thái kích hoạt của cấp bậc */
  active?: boolean;
}

/**
 * Interface cho tham số cập nhật agent rank
 */
export interface UpdateAgentRankParams {
  /** Tên của cấp bậc */
  name?: string;
  /** Mô tả chi tiết về cấp bậc */
  description?: string;
  /** Tên file huy hiệu */
  fileName?: string;
  /** Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc */
  minExp?: number;
  /** Điểm kinh nghiệm tối đa cho cấp bậc */
  maxExp?: number;
  /** Trạng thái kích hoạt của cấp bậc */
  active?: boolean;
}

/**
 * Interface cho tham số query agent rank
 */
export interface AgentRankQueryParams {
  /** Số trang */
  page?: number;
  /** Số lượng item trên mỗi trang */
  limit?: number;
  /** Từ khóa tìm kiếm */
  search?: string;
  /** Lọc theo trạng thái active */
  active?: boolean;
  /** Sắp xếp theo trường */
  sortBy?: AgentRankSortBy;
  /** Hướng sắp xếp */
  sortDirection?: SortDirection;
}

/**
 * Interface cho response tạo agent rank
 */
export interface CreateAgentRankResponse {
  /** URL để upload ảnh badge */
  uploadUrl: string;
  /** ID của rank đã tạo */
  id: number;
}
