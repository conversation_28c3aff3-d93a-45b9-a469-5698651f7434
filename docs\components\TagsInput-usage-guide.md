# TagsInput Component - H<PERSON>ớng dẫn sử dụng

## Mô tả

`TagsInput` là component cho phép người dùng nhập nhiều tag/từ khóa bằng cách gõ và nhấn Enter. Component này tích hợp với React Hook Form và hiển thị các tag dưới dạng Chip.

## Import

```typescript
import { TagsInput } from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
```

## Props

```typescript
interface TagsInputProps<T extends FieldValues = Record<string, unknown>> {
  fieldName: string;                    // Tên trường trong form
  placeholder?: string;                 // Placeholder cho input
  formRef: React.RefObject<FormRef<T>>; // Form ref để truy cập form methods
  initialValue?: string | string[];     // Giá trị ban đầu
  onChange?: (tags: string[]) => void;  // Callback khi tags thay đổi
  readOnly?: boolean;                   // Chế độ chỉ đọc
}
```

## Cách sử dụng cơ bản

### 1. Với React Hook Form

```typescript
import React from 'react';
import { useForm } from 'react-hook-form';
import { TagsInput, Form, FormItem } from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';

interface FormData {
  tags: string[];
}

const MyForm: React.FC = () => {
  const { formRef } = useFormErrors<FormData>();
  const { handleSubmit, setValue } = useForm<FormData>();

  const handleTagsChange = (tags: string[]) => {
    setValue('tags', tags);
  };

  const onSubmit = (data: FormData) => {
    console.log('Tags:', data.tags);
  };

  return (
    <Form ref={formRef} onSubmit={handleSubmit(onSubmit)}>
      <FormItem label="Tags" name="tags">
        <TagsInput
          fieldName="tags"
          formRef={formRef}
          placeholder="Nhập tag và nhấn Enter"
          onChange={handleTagsChange}
        />
      </FormItem>
    </Form>
  );
};
```

### 2. Với giá trị ban đầu

```typescript
<TagsInput
  fieldName="keywords"
  formRef={formRef}
  placeholder="Nhập từ khóa..."
  initialValue={['tag1', 'tag2', 'tag3']}
  onChange={handleTagsChange}
/>
```

### 3. Chế độ chỉ đọc

```typescript
<TagsInput
  fieldName="tags"
  formRef={formRef}
  initialValue="tag1, tag2, tag3"
  readOnly={true}
/>
```

## Tính năng

### 1. Nhập tag
- Gõ tag và nhấn **Enter** để thêm
- Tự động loại bỏ khoảng trắng thừa
- Không cho phép tag trùng lặp

### 2. Xóa tag
- Nhấn nút X trên Chip để xóa tag
- Chỉ hiển thị nút X khi không ở chế độ readOnly

### 3. Tích hợp Form
- Tự động cập nhật giá trị form khi tags thay đổi
- Hỗ trợ validation với React Hook Form
- Trigger validation khi có thay đổi

### 4. Linh hoạt với dữ liệu
- Hỗ trợ initialValue dạng string (phân cách bởi dấu phẩy)
- Hỗ trợ initialValue dạng array
- Trả về array trong form data

## Ví dụ thực tế

### Settings Keywords (như trong ChatKeywordSettings)

```typescript
const ChatKeywordSettings: React.FC = () => {
  const { formRef } = useFormErrors<KeywordFormData>();
  const { setValue } = useForm<KeywordFormData>();

  const handleTagsChange = (tags: string[]) => {
    setValue('keywords', tags);
  };

  return (
    <FormItem label="Từ khóa" name="keywords" required>
      <TagsInput
        fieldName="keywords"
        formRef={formRef}
        placeholder="Nhập từ khóa và nhấn Enter để thêm"
        onChange={handleTagsChange}
      />
      <Typography variant="caption" color="muted">
        Ví dụ: trang chủ, home, dashboard
      </Typography>
    </FormItem>
  );
};
```

### URL Tags (như trong UrlForm)

```typescript
<FormItem name="tags" label="Tags">
  <TagsInput
    fieldName="tags"
    formRef={formRef}
    placeholder="Nhập tag và nhấn Enter"
    initialValue={initialValues?.tags || ''}
    readOnly={readOnly}
  />
</FormItem>
```

## Styling

Component sử dụng:
- `Input` component cho input field
- `Chip` component cho hiển thị tags
- Tailwind CSS classes cho layout

```css
.space-y-2        /* Khoảng cách giữa input và tags */
.flex-wrap        /* Tags wrap xuống dòng */
.gap-1            /* Khoảng cách giữa các tags */
```

## Best Practices

1. **Luôn sử dụng với Form**: Component được thiết kế để hoạt động với React Hook Form
2. **Xử lý onChange**: Sử dụng onChange để cập nhật form state
3. **Validation**: Kết hợp với schema validation (Zod) cho validation
4. **Placeholder rõ ràng**: Hướng dẫn người dùng cách sử dụng
5. **Help text**: Thêm ví dụ hoặc hướng dẫn bên dưới component
