import React, { useState } from 'react';
import { SlideInForm } from '@/shared/components/common';
import CloudStorageProviderList from '../cloud-storage/components/CloudStorageProviderList';
import CloudStorageProviderForm from '../cloud-storage/components/CloudStorageProviderForm';

/**
 * Trang quản lý tích hợp cloud storage
 */
const CloudStorageIntegrationPage: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
  };

  const handleCreateCancel = () => {
    setShowCreateForm(false);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <CloudStorageProviderList onCreateNew={handleCreateNew} />

      {/* Create Form Slide-in */}
      <SlideInForm
        isVisible={showCreateForm}
      >
        <CloudStorageProviderForm
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default CloudStorageIntegrationPage;
