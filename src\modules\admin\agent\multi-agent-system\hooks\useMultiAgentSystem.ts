import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminMultiAgentSystemService } from '../services/multi-agent-system.service';
import {
  MultiAgentSystemDetail,
  CreateMultiAgentSystemParams,
  UpdateMultiAgentSystemParams,
  MultiAgentSystemQueryParams,
} from '../types/multi-agent-system.types';

// Query keys
export const ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS = {
  all: ['admin', 'multi-agent-system'] as const,
  lists: () => [...ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.all, 'list'] as const,
  list: (params: MultiAgentSystemQueryParams) =>
    [...ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.details(), id] as const,
};

export const useAdminMultiAgentSystems = (params: MultiAgentSystemQueryParams) => {
  return useQuery({
    queryKey: ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.list(params),
    queryFn: () => adminMultiAgentSystemService.getMultiAgentSystems(params),
    staleTime: 5 * 60 * 1000,
  });
};

export const useAdminMultiAgentSystemDetail = (id: string) => {
  return useQuery<MultiAgentSystemDetail>({
    queryKey: ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.detail(id),
    queryFn: () => adminMultiAgentSystemService.getMultiAgentSystemById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateAdminMultiAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateMultiAgentSystemParams) =>
      adminMultiAgentSystemService.createMultiAgentSystem(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

export const useUpdateAdminMultiAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMultiAgentSystemParams }) =>
      adminMultiAgentSystemService.updateMultiAgentSystem(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};

export const useDeleteAdminMultiAgentSystem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminMultiAgentSystemService.deleteMultiAgentSystem(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_MULTI_AGENT_SYSTEM_QUERY_KEYS.lists(),
      });
    },
  });
};
