import { apiClient } from '@/shared/api/axios';
import {
  MultiAgentSystemDetail,
  CreateMultiAgentSystemParams,
  UpdateMultiAgentSystemParams,
  MultiAgentSystemQueryParams,
} from '../types/multi-agent-system.types';

/**
 * Service để tương tác với API multi-agent system của admin
 */
export class AdminMultiAgentSystemService {
  private baseUrl = '/admin/multi-agent-systems';

  async getMultiAgentSystems(params: MultiAgentSystemQueryParams) {
    try {
      const response = await apiClient.get(this.baseUrl, { params, tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error('Error fetching multi-agent systems:', error);
      throw error;
    }
  }

  async getMultiAgentSystemById(id: string): Promise<MultiAgentSystemDetail> {
    try {
      const response = await apiClient.get<MultiAgentSystemDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching multi-agent system ${id}:`, error);
      throw error;
    }
  }

  async createMultiAgentSystem(data: CreateMultiAgentSystemParams): Promise<{ id: string }> {
    try {
      const response = await apiClient.post<{ id: string }>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating multi-agent system:', error);
      throw error;
    }
  }

  async updateMultiAgentSystem(id: string, data: UpdateMultiAgentSystemParams): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}`, data, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error updating multi-agent system ${id}:`, error);
      throw error;
    }
  }

  async deleteMultiAgentSystem(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting multi-agent system ${id}:`, error);
      throw error;
    }
  }
}

export const adminMultiAgentSystemService = new AdminMultiAgentSystemService();
