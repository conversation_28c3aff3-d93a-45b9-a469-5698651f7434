import { ReactNode, useImperative<PERSON><PERSON>le, forwardRef, useEffect, useState, useCallback, useRef } from 'react';
import {
  FieldValues,
  FormProvider,
  SubmitHandler,
  UseFormReturn,
  useForm,
  UseFormProps,
  Path,
  FieldErrors,
} from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { isEqual } from 'lodash';
// import { useTranslation } from 'react-i18next';

// Interface cho ref của Form
export interface FormRef<TFormValues extends FieldValues> {
  /**
   * Set lỗi cho các field từ bên ngoài (ví dụ: từ API)
   */
  setErrors: (errors: Record<string, string>) => void;

  /**
   * Reset form về giá trị mặc định
   */
  reset: (values?: TFormValues) => void;

  /**
   * Lấy form methods
   */
  getFormMethods: () => UseFormReturn<TFormValues>;

  /**
   * Submit form programmatically
   */
  submit: () => void;

  /**
   * Scroll to first error field
   */
  scrollToFirstError: () => void;

  /**
   * Get current form values
   */
  getValues: () => TFormValues;

  /**
   * Set form values
   */
  setValues: (values: Partial<TFormValues>) => void;

  /**
   * Check if form is dirty
   */
  isDirty: () => boolean;

  /**
   * Check if form is valid
   */
  isValid: () => boolean;
}

export interface FormProps<TFormValues extends FieldValues> {
  /**
   * Nội dung của form
   */
  children: ReactNode;

  /**
   * Schema validation của Zod
   */
  schema?: z.ZodType<TFormValues>;

  /**
   * Giá trị mặc định của form
   */
  defaultValues?: UseFormProps<TFormValues>['defaultValues'];

  /**
   * Callback khi submit form thành công
   */
  onSubmit: SubmitHandler<TFormValues>;

  /**
   * Callback khi form có lỗi
   */
  onError?: (errors: Record<string, { message: string }>) => void;

  /**
   * ID của form
   */
  id?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Mode validation của form
   * @default 'onSubmit'
   */
  mode?: UseFormProps<TFormValues>['mode'];

  /**
   * Tự động focus vào field lỗi đầu tiên
   * @default true
   */
  shouldFocusError?: boolean;

  /**
   * Chỉ sử dụng defaultValues lần đầu tiên, không cập nhật khi defaultValues thay đổi
   * @default false
   */
  useDefaultValuesOnce?: boolean;

  /**
   * Thuộc tính autoComplete của form
   */
  autoComplete?: string;

  /**
   * Validate khi blur field
   * @default false
   */
  validateOnBlur?: boolean;

  /**
   * Validate khi change field
   * @default false
   */
  validateOnChange?: boolean;

  /**
   * Reset form sau khi submit thành công
   * @default false
   */
  resetOnSubmitSuccess?: boolean;

  /**
   * Hiển thị confirm dialog khi user navigate away với unsaved changes
   * @default false
   */
  confirmOnDirty?: boolean;

  /**
   * Tự động scroll đến field lỗi đầu tiên
   * @default false
   */
  scrollToError?: boolean;

  /**
   * Tự động focus vào field lỗi đầu tiên
   * @default true
   */
  focusOnError?: boolean;

  /**
   * Submit form khi nhấn Enter
   * @default false
   */
  submitOnEnter?: boolean;

  /**
   * Disable toàn bộ form
   * @default false
   */
  disabled?: boolean;

  /**
   * Hiển thị loading state
   * @default false
   */
  loading?: boolean;

  /**
   * Thông báo thành công sau khi submit
   */
  successMessage?: string;

  /**
   * Thông báo lỗi sau khi submit
   */
  errorMessage?: string;

  /**
   * Callback khi form submit thành công
   */
  onSubmitSuccess?: (data: TFormValues) => void;

  /**
   * Callback khi form submit thất bại
   */
  onSubmitError?: (error: unknown) => void;

  /**
   * Tắt HTML5 validation
   * @default true
   */
  noValidate?: boolean;
}

/**
 * Form component quản lý form với validation sử dụng React Hook Form và Zod
 *
 * @example
 * // Form cơ bản
 * const schema = z.object({
 *   name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
 *   email: z.string().email('Email không hợp lệ'),
 * });
 *
 * const formRef = useRef<FormRef<FormValues>>(null);
 *
 * // Xử lý lỗi từ API
 * const handleApiError = (apiError) => {
 *   if (apiError.field) {
 *     formRef.current?.setErrors({
 *       [apiError.field]: apiError.message
 *     });
 *   }
 * };
 *
 * <Form ref={formRef} schema={schema} onSubmit={handleSubmit}>
 *   <FormItem name="name" label="Tên">
 *     <Input />
 *   </FormItem>
 *   <FormItem name="email" label="Email">
 *     <Input type="email" />
 *   </FormItem>
 *   <Button type="submit">Gửi</Button>
 * </Form>
 */
const Form = forwardRef(function Form<TFormValues extends FieldValues>(
  props: FormProps<TFormValues>,
  ref: React.Ref<FormRef<TFormValues>>
) {
  const {
    children,
    schema,
    defaultValues,
    onSubmit,
    onError,
    id,
    className = '',
    mode = 'onSubmit',

    autoComplete,
    validateOnBlur = false,
    validateOnChange = false,
    resetOnSubmitSuccess = false,
    confirmOnDirty = false,
    scrollToError = false,
    focusOnError = true,
    submitOnEnter = false,
    disabled = false,
    loading = false,
    successMessage,
    errorMessage,
    onSubmitSuccess,
    onSubmitError,
    noValidate = true,
    useDefaultValuesOnce = false,
  } = props;

  // State cho thông báo
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // State để theo dõi việc đã khởi tạo defaultValues chưa
  const [hasInitializedDefaults, setHasInitializedDefaults] = useState(false);

  // Ref để lưu trữ defaultValues trước đó để so sánh
  const previousDefaultValuesRef = useRef<UseFormProps<TFormValues>['defaultValues']>();

  // Tính toán mode dựa trên validateOnBlur và validateOnChange
  const formMode = validateOnChange ? 'onChange' : validateOnBlur ? 'onBlur' : mode;
  // Khởi tạo form với React Hook Form
  const methods = useForm<TFormValues>({
    resolver: schema ? zodResolver(schema) : undefined,
    defaultValues,
    mode: formMode,
    shouldFocusError: focusOnError,
  });

  const { formState } = methods;

  // Cập nhật giá trị form khi defaultValues thay đổi
  useEffect(() => {
    if (defaultValues) {
      // Nếu useDefaultValuesOnce = true và đã khởi tạo rồi thì không cập nhật nữa
      if (useDefaultValuesOnce && hasInitializedDefaults) {
        console.log('🚫 Skipping defaultValues update - useDefaultValuesOnce is true and already initialized');
        return;
      }

      // So sánh deep với defaultValues trước đó để tránh update không cần thiết
      const previousDefaultValues = previousDefaultValuesRef.current;
      const hasDefaultValuesChanged = !isEqual(defaultValues, previousDefaultValues);

      if (!hasDefaultValuesChanged) {
        console.log('🔄 defaultValues unchanged, skipping update');
        return;
      }

      console.log('📝 defaultValues changed, processing update', {
        previous: previousDefaultValues,
        current: defaultValues
      });

      // Cập nhật ref với defaultValues mới
      previousDefaultValuesRef.current = defaultValues;

      // Lấy giá trị hiện tại của form
      const currentValues = methods.getValues();

      // Kiểm tra xem form có đang dirty (user đã nhập dữ liệu) không
      const isDirty = formState.isDirty;

      // Nếu form đã dirty, chỉ cập nhật những field chưa được user thay đổi
      if (isDirty) {
        console.log('📝 Form is dirty, only updating unchanged fields', { currentValues, defaultValues });

        // Chỉ cập nhật những field có trong defaultValues nhưng chưa được user thay đổi
        Object.entries(defaultValues).forEach(([fieldName, defaultValue]) => {
          const currentValue = currentValues[fieldName as keyof TFormValues];

          // Nếu field hiện tại rỗng hoặc undefined, cập nhật với defaultValue
          if (currentValue === undefined || currentValue === null || currentValue === '') {
            methods.setValue(fieldName as Path<TFormValues>, defaultValue, {
              shouldValidate: false,
              shouldDirty: false,
              shouldTouch: false,
            });
          }
        });
      } else {
        // Form chưa dirty, có thể an toàn reset với defaultValues mới
        // So sánh giá trị hiện tại với defaultValues mới
        if (!isEqual(currentValues, defaultValues)) {
          console.log('🔄 Form not dirty, updating with defaultValues', { currentValues, defaultValues, useDefaultValuesOnce });

          // Sử dụng reset để đảm bảo form được khởi tạo lại hoàn toàn với dữ liệu mới
          methods.reset(defaultValues as TFormValues, {
            keepErrors: false,
            keepDirty: false,
            keepIsSubmitted: false,
            keepTouched: false,
            keepIsValid: false,
            keepSubmitCount: false,
          });
        }
      }

      // Đánh dấu đã khởi tạo defaultValues
      if (useDefaultValuesOnce && !hasInitializedDefaults) {
        console.log('✅ Marking defaultValues as initialized');
        setHasInitializedDefaults(true);
      }
    }
  }, [defaultValues, methods, useDefaultValuesOnce, hasInitializedDefaults, formState.isDirty]);



  // Xử lý scroll đến field lỗi đầu tiên
  const scrollToFirstError = useCallback(() => {
    if (scrollToError && Object.keys(formState.errors).length > 0) {
      const firstErrorField = Object.keys(formState.errors)[0];
      const element = document.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [scrollToError, formState.errors]);

  // Xử lý submit success
  const handleSubmitSuccess = useCallback(
    async (data: TFormValues) => {
      try {
        setSubmitStatus('idle');
        await onSubmit(data);
        setSubmitStatus('success');
        onSubmitSuccess?.(data);
        if (resetOnSubmitSuccess) {
          methods.reset();
        }
      } catch (error) {
        setSubmitStatus('error');
        onSubmitError?.(error);
      }
    },
    [onSubmit, onSubmitSuccess, onSubmitError, resetOnSubmitSuccess, methods]
  );

  // Xử lý submit error
  const handleSubmitError = useCallback(
    (errors: FieldErrors<TFormValues>) => {
      setSubmitStatus('error');
      scrollToFirstError();
      onError?.(errors as Record<string, { message: string }>);
    },
    [scrollToFirstError, onError]
  );

  // Xử lý submit form với các tính năng mới
  const handleSubmit = methods.handleSubmit(handleSubmitSuccess, handleSubmitError);

  // Xử lý confirm on dirty
  useEffect(() => {
    if (!confirmOnDirty || !formState.isDirty) return;

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      return 'You have unsaved changes. Are you sure you want to leave?';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [confirmOnDirty, formState.isDirty]);

  // Xử lý keyboard events
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLFormElement>) => {
      if (submitOnEnter && e.key === 'Enter' && !e.shiftKey) {
        // Chỉ submit khi không phải trong textarea
        const target = e.target as HTMLElement;
        if (target.tagName !== 'TEXTAREA') {
          e.preventDefault();
          // Trigger form submission
          const form = e.currentTarget;
          form.requestSubmit();
        }
      }
    },
    [submitOnEnter]
  );

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    setErrors: (errors: Record<string, string>) => {
      // Kiểm tra errors có tồn tại và không null/undefined
      if (errors && typeof errors === 'object') {
        // Chuyển đổi errors từ dạng Record<string, string> sang dạng mà React Hook Form cần
        Object.entries(errors).forEach(([field, message]) => {
          // Sử dụng type assertion để tránh lỗi TypeScript
          methods.setError(field as Path<TFormValues>, {
            type: 'manual',
            message,
          });
        });
      }
    },
    reset: (values?: TFormValues) => {
      methods.reset(values);
    },
    getFormMethods: () => methods,
    submit: () => {
      // Trigger submit directly without event
      handleSubmit();
    },
    scrollToFirstError: () => {
      scrollToFirstError();
    },
    getValues: () => {
      return methods.getValues();
    },
    setValues: (values: Partial<TFormValues>) => {
      Object.entries(values).forEach(([field, value]) => {
        if (value !== undefined) {
          methods.setValue(field as Path<TFormValues>, value, {
            shouldValidate: true,
            shouldDirty: true,
            shouldTouch: true,
          });
        }
      });
    },
    isDirty: () => {
      return formState.isDirty;
    },
    isValid: () => {
      return formState.isValid;
    },
  }), [methods, handleSubmit, scrollToFirstError, formState.isDirty, formState.isValid]);

  // Tính toán class cho form
  const formClassName = [
    className,
    disabled && 'pointer-events-none opacity-50',
    loading && 'relative',
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <FormProvider {...methods}>
      <form
        id={id}
        className={formClassName}
        onSubmit={handleSubmit}
        onKeyDown={handleKeyDown}
        noValidate={noValidate}
        autoComplete={autoComplete}
        method="post"
        action=""
      >
        {loading && (
          <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 flex items-center justify-center z-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}

        {children}

        {/* Hiển thị thông báo success/error */}
        {submitStatus === 'success' && successMessage && (
          <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/20 border border-green-300 dark:border-green-700 rounded-md">
            <p className="text-green-700 dark:text-green-300 text-sm">{successMessage}</p>
          </div>
        )}

        {submitStatus === 'error' && errorMessage && (
          <div className="mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md">
            <p className="text-red-700 dark:text-red-300 text-sm">{errorMessage}</p>
          </div>
        )}
      </form>
    </FormProvider>
  );
});

Form.displayName = 'Form';

export default Form;
