/**
 * Enum cho các trường sắp xếp của agent role
 */
export enum AgentRoleSortBy {
  ID = 'id',
  NAME = 'name',
  DESCRIPTION = 'description',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  /** ID nhân viên */
  employeeId: string;
  /** Tên nhân viên */
  name: string;
  /** Avatar nhân viên */
  avatar: string | null;
  /** Thời gian */
  date?: Date;
}

/**
 * Interface cho cấu hình reconnect trong MCP
 */
export interface ReconnectConfig {
  /** Bật/tắt tính năng reconnect */
  enable: boolean;
  /** Thời gian delay giữa các lần reconnect (ms) */
  delayMs: number;
  /** Số lần thử reconnect tối đa */
  maxAttempts: number;
}

/**
 * Interface cho cấu hình module MCP
 */
export interface ModuleMcpConfig {
  /** Tên server MCP */
  mcpNameServer: string;
  /** Port của server MCP */
  mcpPort: number;
  /** URL của server MCP */
  url: string;
  /** Sử dụng Node EventSource */
  useNodeEventSource: boolean;
  /** Header cho request */
  header: Record<string, string>;
  /** Cấu hình reconnect */
  reconnect: ReconnectConfig;
}

/**
 * Interface cho thông tin agent sử dụng vai trò
 */
export interface RoleAgentUse {
  /** ID của agent */
  id: string;
  /** Tên của agent */
  name: string;
  /** URL avatar của agent */
  avatar?: string | null;
}

/**
 * Interface cho agent role trong danh sách
 */
export interface AgentRoleListItem {
  /** ID của vai trò */
  id: string;
  /** Tên của vai trò */
  name: string;
  /** Mô tả của vai trò */
  description: string;
}

/**
 * Interface cho thông tin chi tiết agent role
 */
export interface AgentRoleDetail {
  /** ID của vai trò */
  id: string;
  /** Tên của vai trò */
  name: string;
  /** Mô tả của vai trò */
  description: string;
  /** Thông tin agent đang sử dụng vai trò */
  agentUse?: RoleAgentUse;
  /** Thông tin người tạo */
  created?: EmployeeInfo | null;
  /** Thông tin người cập nhật */
  updated?: EmployeeInfo | null;
  /** Thông tin người xóa */
  deleted?: EmployeeInfo | null;
  /** Cấu hình module MCP */
  moduleMcpConfig?: ModuleMcpConfig;
}

/**
 * Interface cho agent role đã xóa trong trash
 */
export interface AgentRoleTrashItem {
  /** ID của vai trò */
  id: string;
  /** Tên của vai trò */
  name: string;
  /** Mô tả của vai trò */
  description: string;
  /** Thông tin người xóa */
  deleted?: EmployeeInfo;
}

/**
 * Interface cho tham số tạo agent role
 */
export interface CreateAgentRoleParams {
  /** Tên của vai trò */
  name: string;
  /** Mô tả của vai trò */
  description?: string;
  /** Cấu hình module MCP */
  moduleMcpConfig?: ModuleMcpConfig;
}

/**
 * Interface cho tham số cập nhật agent role
 */
export interface UpdateAgentRoleParams {
  /** Tên của vai trò */
  name?: string;
  /** Mô tả của vai trò */
  description?: string;
  /** Cấu hình module MCP */
  moduleMcpConfig?: ModuleMcpConfig;
}

/**
 * Interface cho tham số query agent role
 */
export interface AgentRoleQueryParams {
  /** Số trang */
  page?: number;
  /** Số lượng item trên mỗi trang */
  limit?: number;
  /** Từ khóa tìm kiếm */
  search?: string;
  /** Sắp xếp theo trường */
  sortBy?: AgentRoleSortBy;
  /** Hướng sắp xếp */
  sortDirection?: SortDirection;
}

/**
 * Interface cho response tạo agent role
 */
export interface CreateAgentRoleResponse {
  /** ID của role đã tạo */
  id: string;
}
