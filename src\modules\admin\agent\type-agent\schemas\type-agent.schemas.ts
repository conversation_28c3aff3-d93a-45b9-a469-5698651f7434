import { z } from 'zod';
import { TypeAgentSortBy, SortDirection } from '../types/type-agent.types';

export const createTypeAgentSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên loại agent là bắt buộc')
    .max(255, 'Tên loại agent không được quá 255 ký tự'),
  description: z.string().optional(),
  configuration: z.record(z.unknown()).optional(),
  active: z.boolean().optional().default(true),
});

export const updateTypeAgentSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên loại agent là bắt buộc')
    .max(255, 'Tên loại agent không được quá 255 ký tự')
    .optional(),
  description: z.string().optional(),
  configuration: z.record(z.unknown()).optional(),
  active: z.boolean().optional(),
});

export const typeAgentQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  active: z.boolean().optional(),
  sortBy: z.nativeEnum(TypeAgentSortBy).optional().default(TypeAgentSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

export type CreateTypeAgentFormData = z.infer<typeof createTypeAgentSchema>;
export type UpdateTypeAgentFormData = z.infer<typeof updateTypeAgentSchema>;
export type TypeAgentQueryFormData = z.infer<typeof typeAgentQuerySchema>;
