import { apiClient } from '@/shared/api/axios';
import {
  AgentBaseDetail,
  CreateAgentBaseParams,
  UpdateAgentBaseParams,
  AgentBaseQueryParams,
  CreateAgentBaseResponse,
  UpdateAgentBaseResponse,
} from '../types/agent-base.types';

/**
 * Service để tương tác với API agent base của admin
 */
export class AdminAgentBaseService {
  private baseUrl = '/admin/agents/base';

  /**
   * L<PERSON>y danh sách agent base
   * @param params Tham số truy vấn
   * @returns Danh sách agent base
   */
  async getAgentBases(params: AgentBaseQueryParams) {
    try {
      const response = await apiClient.get(this.baseUrl, {
        params,
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching agent bases:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách agent base đã xóa
   * @param params Tham số truy vấn
   * @returns Danh sách agent base đã xóa
   */
  async getDeletedAgentBases(params: AgentBaseQueryParams) {
    try {
      const response = await apiClient.get(`${this.baseUrl}/trash`, {
        params,
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching deleted agent bases:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết agent base theo ID
   * @param id ID của agent base
   * @returns Thông tin chi tiết agent base
   */
  async getAgentBaseById(id: string): Promise<AgentBaseDetail> {
    try {
      const response = await apiClient.get<AgentBaseDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching agent base ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo agent base mới
   * @param data Dữ liệu tạo agent base
   * @returns Response tạo agent base
   */
  async createAgentBase(data: CreateAgentBaseParams): Promise<CreateAgentBaseResponse> {
    try {
      const response = await apiClient.post<CreateAgentBaseResponse>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating agent base:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin agent base
   * @param id ID của agent base
   * @param data Dữ liệu cập nhật
   * @returns Response cập nhật agent base
   */
  async updateAgentBase(id: string, data: UpdateAgentBaseParams): Promise<UpdateAgentBaseResponse> {
    try {
      const response = await apiClient.patch<UpdateAgentBaseResponse>(
        `${this.baseUrl}/${id}`,
        data,
        { tokenType: 'admin' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error updating agent base ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa agent base
   * @param id ID của agent base
   * @returns Kết quả xóa
   */
  async deleteAgentBase(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting agent base ${id}:`, error);
      throw error;
    }
  }

  /**
   * Đặt agent base thành active
   * @param id ID của agent base
   * @returns Kết quả đặt active
   */
  async setAgentBaseActive(id: string): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/active`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error setting agent base ${id} active:`, error);
      throw error;
    }
  }

  /**
   * Khôi phục agent base đã xóa
   * @param id ID của agent base
   * @returns Kết quả khôi phục
   */
  async restoreAgentBase(id: string): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/restore`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error restoring agent base ${id}:`, error);
      throw error;
    }
  }
}

// Export instance
export const adminAgentBaseService = new AdminAgentBaseService();
