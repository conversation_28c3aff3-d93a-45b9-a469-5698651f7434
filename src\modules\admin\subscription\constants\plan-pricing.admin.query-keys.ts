import { GetPlanPricingsQueryDto } from '../types/plan-pricing.admin.types';

/**
 * Query keys cho Plan Pricing Admin
 */
export const PLAN_PRICING_ADMIN_QUERY_KEYS = {
  /**
   * Key gốc cho tất cả queries liên quan đến plan pricing admin
   */
  all: ['admin', 'plan-pricing'] as const,

  /**
   * Key cho danh sách plan pricing
   */
  lists: () => [...PLAN_PRICING_ADMIN_QUERY_KEYS.all, 'list'] as const,

  /**
   * Key cho danh sách plan pricing với params
   */
  list: (params: GetPlanPricingsQueryDto) =>
    [...PLAN_PRICING_ADMIN_QUERY_KEYS.lists(), params] as const,

  /**
   * Key cho chi tiết plan pricing
   */
  details: () => [...PLAN_PRICING_ADMIN_QUERY_KEYS.all, 'detail'] as const,

  /**
   * Key cho chi tiết plan pricing theo ID
   */
  detail: (id: number) =>
    [...PLAN_PRICING_ADMIN_QUERY_KEYS.details(), id] as const,

  /**
   * Key cho plan pricing theo plan ID
   */
  byPlan: (planId: number) =>
    [...PLAN_PRICING_ADMIN_QUERY_KEYS.all, 'by-plan', planId] as const,

  /**
   * Key cho plan pricing theo billing cycle
   */
  byBillingCycle: (billingCycle: string) =>
    [...PLAN_PRICING_ADMIN_QUERY_KEYS.all, 'by-billing-cycle', billingCycle] as const,

  /**
   * Key cho plan pricing active/inactive
   */
  byStatus: (isActive: boolean) =>
    [...PLAN_PRICING_ADMIN_QUERY_KEYS.all, 'by-status', isActive] as const,
} as const;
