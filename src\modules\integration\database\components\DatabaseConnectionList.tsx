import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Table } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';
import { DatabaseConnectionConfig } from '../types';

interface DatabaseConnectionListProps {
  connections: DatabaseConnectionConfig[];
  loading?: boolean;
  onEdit?: (connection: DatabaseConnectionConfig) => void;
  onView?: (connection: DatabaseConnectionConfig) => void;
  onDelete?: (connection: DatabaseConnectionConfig) => void;
  onTest?: (connection: DatabaseConnectionConfig) => void;
  onStatusChange?: (connection: DatabaseConnectionConfig, status: string) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
    showSizeChanger?: boolean;
    pageSizeOptions?: number[];
    showFirstLastButtons?: boolean;
    showPageInfo?: boolean;
  };
  sortable?: boolean;
  onSortChange?: (column: string | null, order: 'asc' | 'desc' | null) => void;
}

/**
 * Database Connection List Component
 * Displays database connections in a table format
 */
const DatabaseConnectionList: React.FC<DatabaseConnectionListProps> = ({
  connections,
  loading = false,
  onEdit,
  onView,
  onDelete,
  onTest,
  onStatusChange,
  pagination,
  sortable = true,
  onSortChange,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // Define table columns
  const columns = useMemo<TableColumn<DatabaseConnectionConfig>[]>(() => {
    const allColumns: TableColumn<DatabaseConnectionConfig>[] = [
      {
        key: 'name',
        title: t('admin:integration.database.list.columns.name'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
        render: (value: unknown, record: DatabaseConnectionConfig) => (
          <div className="flex flex-col">
            <span className="font-medium text-foreground">{String(value)}</span>
            <span className="text-sm text-muted-foreground">{record.displayName}</span>
          </div>
        ),
      },
      {
        key: 'type',
        title: t('admin:integration.database.list.columns.type'),
        dataIndex: 'type',
        width: '15%',
        sortable: true,
        render: (value: unknown) => (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {String(value).toUpperCase()}
          </span>
        ),
      },
      {
        key: 'host',
        title: t('admin:integration.database.list.columns.host'),
        dataIndex: 'credentials',
        width: '20%',
        render: (credentials: unknown) => {
          const creds = credentials as Record<string, unknown>;
          if (creds?.host) {
            const port = creds.port as string | number | undefined;
            return (
              <div className="flex flex-col">
                <span className="text-foreground">{String(creds.host)}</span>
                {port && (
                  <span className="text-sm text-muted-foreground">Port: {String(port)}</span>
                )}
              </div>
            );
          }
          if (creds?.connectionString) {
            return (
              <span className="text-foreground font-mono text-sm">
                {String(creds.connectionString).substring(0, 30)}...
              </span>
            );
          }
          if (creds?.filePath) {
            return (
              <span className="text-foreground font-mono text-sm">
                {String(creds.filePath)}
              </span>
            );
          }
          return <span className="text-muted-foreground">-</span>;
        },
      },
      {
        key: 'database',
        title: t('admin:integration.database.list.columns.database'),
        dataIndex: 'credentials',
        width: '15%',
        render: (credentials: unknown) => {
          const creds = credentials as Record<string, unknown>;
          const dbName = creds?.database || creds?.collection;
          return dbName ? (
            <span className="text-foreground">{String(dbName)}</span>
          ) : (
            <span className="text-muted-foreground">-</span>
          );
        },
      },
      {
        key: 'status',
        title: t('admin:integration.database.list.columns.status'),
        dataIndex: 'status',
        width: '12%',
        render: (value: unknown) => {
          const status = String(value);
          const statusColors = {
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-gray-100 text-gray-800',
            error: 'bg-red-100 text-red-800',
            testing: 'bg-yellow-100 text-yellow-800',
            pending: 'bg-blue-100 text-blue-800',
          };
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
            }`}>
              {t(`admin:integration.database.status.${status}`, status)}
            </span>
          );
        },
      },
      {
        key: 'isDefault',
        title: t('admin:integration.database.list.columns.default'),
        dataIndex: 'isDefault',
        width: '8%',
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {value ? t('common:yes') : t('common:no')}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('admin:integration.database.list.columns.actions'),
        width: '10%',
        render: (_: unknown, record: DatabaseConnectionConfig) => {
          const menuItems = [
            {
              label: t('admin:integration.database.actions.edit'),
              icon: 'edit',
              onClick: () => onEdit?.(record),
            },
            {
              label: t('common:view'),
              icon: 'eye',
              onClick: () => onView?.(record),
            },
            {
              label: t('admin:integration.database.actions.test'),
              icon: 'link',
              onClick: () => onTest?.(record),
            },
            {
              type: 'divider' as const,
            },
            {
              label: record.status === 'active' 
                ? t('admin:integration.database.actions.deactivate')
                : t('admin:integration.database.actions.activate'),
              icon: record.status === 'active' ? 'pause' : 'play',
              onClick: () => onStatusChange?.(record, record.status === 'active' ? 'inactive' : 'active'),
            },
            {
              label: t('admin:integration.database.actions.delete'),
              icon: 'trash',
              onClick: () => onDelete?.(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [t, onEdit, onView, onDelete, onTest, onStatusChange]);

  return (
    <Table<DatabaseConnectionConfig>
      columns={columns}
      data={connections}
      rowKey="id"
      loading={loading}
      sortable={sortable}
      onSortChange={onSortChange}
      pagination={pagination}

    />
  );
};

export default DatabaseConnectionList;
