/**
 * Responsive utility classes
 * These classes provide additional responsive functionality beyond Tailwind's defaults
 */

/* ResponsiveGrid component styles */
.grid[class*='grid-cols-'] {
  display: grid !important;
  width: 100% !important;
}

/* Responsive grid columns for different breakpoints */
/* Base grid columns (all screen sizes) */
.grid.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
}

.grid.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
}

.grid.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
}

.grid.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
}

/* Ensure grid columns work properly on larger screens */
@media (min-width: 1024px) {
  .grid.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .grid.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .grid.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
  }
}

/* Responsive text alignment */
.text-center-xs {
  @apply text-center;
}

.text-left-xs {
  @apply text-left;
}

.text-right-xs {
  @apply text-right;
}

@media (min-width: 640px) {
  .text-center-sm {
    @apply text-center;
  }

  .text-left-sm {
    @apply text-left;
  }

  .text-right-sm {
    @apply text-right;
  }
}

@media (min-width: 768px) {
  .text-center-md {
    @apply text-center;
  }

  .text-left-md {
    @apply text-left;
  }

  .text-right-md {
    @apply text-right;
  }
}

@media (min-width: 1024px) {
  .text-center-lg {
    @apply text-center;
  }

  .text-left-lg {
    @apply text-left;
  }

  .text-right-lg {
    @apply text-right;
  }
}

@media (min-width: 1280px) {
  .text-center-xl {
    @apply text-center;
  }

  .text-left-xl {
    @apply text-left;
  }

  .text-right-xl {
    @apply text-right;
  }
}

/* Responsive order */
.order-first-xs {
  @apply order-first;
}

.order-last-xs {
  @apply order-last;
}

@media (min-width: 640px) {
  .order-first-sm {
    @apply order-first;
  }

  .order-last-sm {
    @apply order-last;
  }
}

@media (min-width: 768px) {
  .order-first-md {
    @apply order-first;
  }

  .order-last-md {
    @apply order-last;
  }
}

@media (min-width: 1024px) {
  .order-first-lg {
    @apply order-first;
  }

  .order-last-lg {
    @apply order-last;
  }
}

@media (min-width: 1280px) {
  .order-first-xl {
    @apply order-first;
  }

  .order-last-xl {
    @apply order-last;
  }
}

/* Responsive spacing */
.m-auto-xs {
  @apply m-auto;
}

.mx-auto-xs {
  @apply mx-auto;
}

.my-auto-xs {
  @apply my-auto;
}

@media (min-width: 640px) {
  .m-auto-sm {
    @apply m-auto;
  }

  .mx-auto-sm {
    @apply mx-auto;
  }

  .my-auto-sm {
    @apply my-auto;
  }
}

@media (min-width: 768px) {
  .m-auto-md {
    @apply m-auto;
  }

  .mx-auto-md {
    @apply mx-auto;
  }

  .my-auto-md {
    @apply my-auto;
  }
}

@media (min-width: 1024px) {
  .m-auto-lg {
    @apply m-auto;
  }

  .mx-auto-lg {
    @apply mx-auto;
  }

  .my-auto-lg {
    @apply my-auto;
  }
}

@media (min-width: 1280px) {
  .m-auto-xl {
    @apply m-auto;
  }

  .mx-auto-xl {
    @apply mx-auto;
  }

  .my-auto-xl {
    @apply my-auto;
  }
}

/* Responsive display */
.block-xs {
  @apply block;
}

.inline-xs {
  @apply inline;
}

.inline-block-xs {
  @apply inline-block;
}

.flex-xs {
  @apply flex;
}

.inline-flex-xs {
  @apply inline-flex;
}

.hidden-xs {
  @apply hidden;
}

@media (min-width: 640px) {
  .block-sm {
    @apply block;
  }

  .inline-sm {
    @apply inline;
  }

  .inline-block-sm {
    @apply inline-block;
  }

  .flex-sm {
    @apply flex;
  }

  .inline-flex-sm {
    @apply inline-flex;
  }

  .hidden-sm {
    @apply hidden;
  }
}

@media (min-width: 768px) {
  .block-md {
    @apply block;
  }

  .inline-md {
    @apply inline;
  }

  .inline-block-md {
    @apply inline-block;
  }

  .flex-md {
    @apply flex;
  }

  .inline-flex-md {
    @apply inline-flex;
  }

  .hidden-md {
    @apply hidden;
  }
}

@media (min-width: 1024px) {
  .block-lg {
    @apply block;
  }

  .inline-lg {
    @apply inline;
  }

  .inline-block-lg {
    @apply inline-block;
  }

  .flex-lg {
    @apply flex;
  }

  .inline-flex-lg {
    @apply inline-flex;
  }

  .hidden-lg {
    @apply hidden;
  }
}

@media (min-width: 1280px) {
  .block-xl {
    @apply block;
  }

  .inline-xl {
    @apply inline;
  }

  .inline-block-xl {
    @apply inline-block;
  }

  .flex-xl {
    @apply flex;
  }

  .inline-flex-xl {
    @apply inline-flex;
  }

  .hidden-xl {
    @apply hidden;
  }
}

/* Responsive flex direction */
.flex-row-xs {
  @apply flex-row;
}

.flex-col-xs {
  @apply flex-col;
}

@media (min-width: 640px) {
  .flex-row-sm {
    @apply flex-row;
  }

  .flex-col-sm {
    @apply flex-col;
  }
}

@media (min-width: 768px) {
  .flex-row-md {
    @apply flex-row;
  }

  .flex-col-md {
    @apply flex-col;
  }
}

@media (min-width: 1024px) {
  .flex-row-lg {
    @apply flex-row;
  }

  .flex-col-lg {
    @apply flex-col;
  }
}

@media (min-width: 1280px) {
  .flex-row-xl {
    @apply flex-row;
  }

  .flex-col-xl {
    @apply flex-col;
  }
}

/* Responsive width */
.w-full-xs {
  @apply w-full;
}

.w-auto-xs {
  @apply w-auto;
}

@media (min-width: 640px) {
  .w-full-sm {
    @apply w-full;
  }

  .w-auto-sm {
    @apply w-auto;
  }
}

@media (min-width: 768px) {
  .w-full-md {
    @apply w-full;
  }

  .w-auto-md {
    @apply w-auto;
  }
}

@media (min-width: 1024px) {
  .w-full-lg {
    @apply w-full;
  }

  .w-auto-lg {
    @apply w-auto;
  }
}

@media (min-width: 1280px) {
  .w-full-xl {
    @apply w-full;
  }

  .w-auto-xl {
    @apply w-auto;
  }
}

/* Responsive height */
.h-full-xs {
  @apply h-full;
}

.h-auto-xs {
  @apply h-auto;
}

@media (min-width: 640px) {
  .h-full-sm {
    @apply h-full;
  }

  .h-auto-sm {
    @apply h-auto;
  }
}

@media (min-width: 768px) {
  .h-full-md {
    @apply h-full;
  }

  .h-auto-md {
    @apply h-auto;
  }
}

@media (min-width: 1024px) {
  .h-full-lg {
    @apply h-full;
  }

  .h-auto-lg {
    @apply h-auto;
  }
}

@media (min-width: 1280px) {
  .h-full-xl {
    @apply h-full;
  }

  .h-auto-xl {
    @apply h-auto;
  }
}

/* Responsive image blur-up effect */
.blur-up {
  filter: blur(5px);
  transition: filter 0.3s ease-in-out;
}

.blur-up.loaded {
  filter: blur(0);
}
