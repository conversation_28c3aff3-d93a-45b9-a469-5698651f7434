import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum cho chu kỳ thanh toán
 */
export enum BillingCycle {
  MONTH = 'month',
  YEAR = 'year',
  SIX_MONTHS = '6_months',
}

/**
 * Enum cho đơn vị sử dụng
 */
export enum UsageUnit {
  BYTES = 'bytes',
  REQUEST = 'REQUEST',
}

/**
 * Interface cho Plan Pricing
 */
export interface PlanPricing {
  id: number;
  planId: number;
  billingCycle: BillingCycle;
  price: string;
  createdAt: string;
  updatedAt: string;
  usageLimit: string;
  usageUnit: UsageUnit;
  isActive: boolean;
}

/**
 * DTO cho query danh sách Plan Pricing
 */
export interface GetPlanPricingsQueryDto extends QueryDto {
  planId?: number;
  billingCycle?: BillingCycle;
  isActive?: boolean;
}

/**
 * DTO cho tạo Plan Pricing mới
 */
export interface CreatePlanPricingDto {
  planId: number;
  billingCycle: BillingCycle;
  price: number;
  usageLimit: number;
  usageUnit: UsageUnit;
  isActive: boolean;
}

/**
 * DTO cho cập nhật Plan Pricing
 */
export interface UpdatePlanPricingDto {
  planId?: number;
  billingCycle?: BillingCycle;
  price?: number;
  usageLimit?: number;
  usageUnit?: UsageUnit;
  isActive?: boolean;
}
