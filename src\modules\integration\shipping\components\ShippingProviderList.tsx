import React from 'react';
import { Card, Typography, Button, Icon, EmptyState } from '@/shared/components/common';

interface ShippingProviderListProps {
  onCreateNew?: () => void;
}

/**
 * Placeholder component for Shipping Provider List
 */
const ShippingProviderList: React.FC<ShippingProviderListProps> = ({ onCreateNew }) => {
  return (
    <div className="w-full space-y-6">
      <Card>
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <Typography variant="h3" className="mb-2">
                Danh sách nhà vận chuyển
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                Quản lý các tích hợp nhà vận chuyển
              </Typography>
            </div>

            <Button
              variant="primary"
              onClick={onCreateNew}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              Thêm nhà vận chuyển
            </Button>
          </div>
        </div>
      </Card>

      <Card>
        <div className="p-8">
          <EmptyState
            icon="truck"
            title="Chưa có nhà vận chuyển nào"
            description="Bạn chưa thêm nhà vận chuyển nào. Hãy thêm nhà vận chuyển đầu tiên."
            actions={
              <Button
                variant="primary"
                onClick={onCreateNew}
                leftIcon={<Icon name="plus" size="sm" />}
              >
                Thêm nhà vận chuyển đầu tiên
              </Button>
            }
          />
        </div>
      </Card>
    </div>
  );
};

export default ShippingProviderList;
