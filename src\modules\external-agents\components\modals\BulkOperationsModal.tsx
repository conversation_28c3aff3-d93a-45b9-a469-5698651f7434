import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Button,
  Select,
  Typography,
  Checkbox,
  ProgressBar
} from '@/shared/components/common';
import { ExternalAgent, ExternalAgentStatus } from '../../types';
import { useBulkUpdateStatus, useBulkDeleteAgents } from '../../hooks';

interface BulkOperationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedAgents: ExternalAgent[];
  onComplete?: () => void;
}

type BulkOperation = 'updateStatus' | 'delete' | 'test';

const BulkOperationsModal: React.FC<BulkOperationsModalProps> = ({
  isOpen,
  onClose,
  selectedAgents,
  onComplete,
}) => {
  const { t } = useTranslation(['external-agents']);
  const [operation, setOperation] = useState<BulkOperation>('updateStatus');
  const [newStatus, setNewStatus] = useState<ExternalAgentStatus>(ExternalAgentStatus.ACTIVE);
  const [confirmed, setConfirmed] = useState(false);

  const bulkUpdateMutation = useBulkUpdateStatus();
  const bulkDeleteMutation = useBulkDeleteAgents();

  const operationOptions = [
    { value: 'updateStatus', label: t('external-agents:bulk.updateStatus') },
    { value: 'delete', label: t('external-agents:bulk.delete') },
    { value: 'test', label: t('external-agents:bulk.testConnections') },
  ];

  const statusOptions = Object.values(ExternalAgentStatus).map(status => ({
    value: status,
    label: t(`external-agents:status.${status}`),
  }));

  const isLoading = bulkUpdateMutation.isPending || bulkDeleteMutation.isPending;

  const handleExecute = async () => {
    if (!confirmed || selectedAgents.length === 0) return;

    const agentIds = selectedAgents.map(agent => agent.id);

    try {
      switch (operation) {
        case 'updateStatus':
          await bulkUpdateMutation.mutateAsync({ ids: agentIds, status: newStatus });
          break;
        case 'delete':
          await bulkDeleteMutation.mutateAsync(agentIds);
          break;
        case 'test':
          // TODO: Implement bulk connection testing
          console.log('Bulk testing not implemented yet');
          break;
      }

      onComplete?.();
      handleClose();
    } catch (error) {
      console.error('Bulk operation failed:', error);
    }
  };

  const handleClose = () => {
    setOperation('updateStatus');
    setNewStatus(ExternalAgentStatus.ACTIVE);
    setConfirmed(false);
    onClose();
  };

  const getOperationDescription = () => {
    switch (operation) {
      case 'updateStatus':
        return t('external-agents:bulk.updateStatusDescription', { 
          count: selectedAgents.length,
          status: t(`external-agents:status.${newStatus}`)
        });
      case 'delete':
        return t('external-agents:bulk.deleteDescription', { count: selectedAgents.length });
      case 'test':
        return t('external-agents:bulk.testDescription', { count: selectedAgents.length });
      default:
        return '';
    }
  };

  const isDestructive = operation === 'delete';

  const footer = (
    <div className="flex justify-end gap-3">
      <Button variant="outline" onClick={handleClose} disabled={isLoading}>
        {t('external-agents:actions.cancel')}
      </Button>
      <Button
        variant={isDestructive ? 'danger' : 'primary'}
        onClick={handleExecute}
        disabled={!confirmed || selectedAgents.length === 0 || isLoading}
      >
        {isLoading
          ? t('external-agents:bulk.processing')
          : t('external-agents:actions.execute')
        }
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={t('external-agents:bulk.title')}
      size="md"
      footer={footer}
    >

        <div className="space-y-6">
          {/* Selected Agents Summary */}
          <div className="p-4 bg-muted rounded-lg">
            <Typography variant="body2" className="font-medium mb-2">
              {t('external-agents:bulk.selectedAgents')}:
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {selectedAgents.length} {t('external-agents:bulk.agentsSelected')}
            </Typography>
            <div className="mt-2 max-h-32 overflow-y-auto">
              {selectedAgents.slice(0, 5).map(agent => (
                <div key={agent.id} className="text-sm py-1">
                  • {agent.name}
                </div>
              ))}
              {selectedAgents.length > 5 && (
                <div className="text-sm text-muted-foreground">
                  {t('external-agents:bulk.andMore', { count: selectedAgents.length - 5 })}
                </div>
              )}
            </div>
          </div>

          {/* Operation Selection */}
          <div className="space-y-4">
            <div>
              <Typography variant="body2" className="font-medium mb-2">
                {t('external-agents:bulk.selectOperation')}:
              </Typography>
              <Select
                value={operation}
                onChange={(value) => setOperation(value as BulkOperation)}
                options={operationOptions}

              />
            </div>

            {/* Status Selection for Update Operation */}
            {operation === 'updateStatus' && (
              <div>
                <Typography variant="body2" className="font-medium mb-2">
                  {t('external-agents:bulk.newStatus')}:
                </Typography>
                <Select
                  value={newStatus}
                  onChange={(value) => setNewStatus(value as ExternalAgentStatus)}
                  options={statusOptions}

                />
              </div>
            )}
          </div>

          {/* Operation Description */}
          <div className={`p-4 rounded-lg ${isDestructive ? 'bg-red-50 border border-red-200' : 'bg-blue-50 border border-blue-200'}`}>
            <Typography variant="body2" className={isDestructive ? 'text-red-700' : 'text-blue-700'}>
              {getOperationDescription()}
            </Typography>
          </div>

          {/* Confirmation */}
          <div className="flex items-center gap-2">
            <Checkbox
              checked={confirmed}
              onChange={setConfirmed}
              id="confirm-bulk-operation"
            />
            <label htmlFor="confirm-bulk-operation" className="text-sm cursor-pointer">
              {isDestructive 
                ? t('external-agents:bulk.confirmDestructive')
                : t('external-agents:bulk.confirmOperation')
              }
            </label>
          </div>

          {/* Progress */}
          {isLoading && (
            <div className="space-y-2">
              <ProgressBar value={0} className="w-full" />
              <Typography variant="caption" className="text-muted-foreground">
                {t('external-agents:bulk.processing')}
              </Typography>
            </div>
          )}
        </div>
    </Modal>
  );
};

export default BulkOperationsModal;
