import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import { Card, Table, IconCard, Tooltip, Loading } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useFormErrors } from '@/shared/hooks/';
import CustomerForm from '../components/forms/CustomerForm';
import CustomerDetailView from '../components/forms/CustomerDetailView';
import CustomerImport from '../components/import/CustomerImport';
import { useConvertCustomers } from '../hooks/useCustomerQuery';
import {
  UserConvertCustomerListItemDto,
  QueryUserConvertCustomerDto,
  UserConvertCustomerSortField,
  SortDirection,
} from '../types/customer.types';
import { CustomerDetailData } from '../components/forms/sections/types';

/**
 * Utility function để map UserConvertCustomerListItemDto sang CustomerDetailData
 */
const mapToCustomerDetailData = (customer: UserConvertCustomerListItemDto): CustomerDetailData => {
  // Xử lý an toàn cho createdAt để tránh lỗi Invalid time value
  const getCustomerSince = (createdAt: unknown): string => {
    try {
      // Kiểm tra nếu createdAt là null, undefined hoặc không hợp lệ
      if (!createdAt) {
        return new Date().toISOString().split('T')[0]; // Fallback về ngày hiện tại
      }

      let date: Date;

      // Nếu là số (timestamp)
      if (typeof createdAt === 'number') {
        date = new Date(createdAt);
      }
      // Nếu là string
      else if (typeof createdAt === 'string') {
        // Kiểm tra nếu là timestamp dạng string
        if (/^\d+$/.test(createdAt)) {
          date = new Date(Number(createdAt));
        } else {
          date = new Date(createdAt);
        }
      }
      // Nếu đã là Date object
      else if (createdAt instanceof Date) {
        date = createdAt;
      }
      // Fallback
      else {
        date = new Date();
      }

      // Kiểm tra xem date có hợp lệ không
      if (isNaN(date.getTime())) {
        return new Date().toISOString().split('T')[0]; // Fallback về ngày hiện tại
      }

      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error parsing createdAt:', error, 'Value:', createdAt);
      return new Date().toISOString().split('T')[0]; // Fallback về ngày hiện tại
    }
  };

  return {
    id: customer.id.toString(),
    name: customer.name || 'N/A',
    email:
      typeof customer.email === 'object'
        ? customer.email?.primary || 'N/A'
        : customer.email || 'N/A',
    phone: customer.phone || 'N/A',
    avatar: customer.avatar || undefined,
    status: 'active', // Default status since API doesn't provide this
    totalOrders: 0, // Default values since API doesn't provide these
    totalSpent: 0,
    averageOrderValue: 0,
    customerSince: getCustomerSince(customer.createdAt),
    // Additional fields from API
    address: customer.platform || undefined,
    tags: customer.metadata ? [customer.platform || 'Unknown'] : [],
    // Empty arrays for sections that need data
    interactions: [],
    orders: [],
    activities: [],
  };
};

/**
 * Trang quản lý khách hàng
 */
const CustomerPage: React.FC = () => {
  const { t } = useTranslation('business');

  // State cho filter và pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [platform, setPlatform] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State cho detail view
  const [selectedCustomer, setSelectedCustomer] = useState<UserConvertCustomerListItemDto | null>(
    null
  );

  // Query parameters
  const queryParams: QueryUserConvertCustomerDto = useMemo(
    () => ({
      page: currentPage,
      limit: pageSize,
      search: searchTerm || undefined,
      platform: platform || undefined,
      sortBy: UserConvertCustomerSortField.CREATED_AT,
      sortDirection: SortDirection.DESC,
    }),
    [currentPage, pageSize, searchTerm, platform]
  );

  // Fetch data using API
  const { data: customerData, isLoading, error, refetch } = useConvertCustomers(queryParams);

  // Hook để xử lý lỗi form
  const { formRef, setFormErrors } = useFormErrors<FieldValues>();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho detail view
  const { isVisible: isDetailVisible, showForm: showDetail, hideForm: hideDetail } = useSlideForm();

  // Sử dụng hook animation cho import modal
  const { isVisible: isImportVisible, showForm: showImport, hideForm: hideImport } = useSlideForm();

  // Định nghĩa cột cho bảng
  const columns: TableColumn<UserConvertCustomerListItemDto>[] = [
    {
      key: 'avatar',
      title: t('customer.form.avatar'),
      dataIndex: 'avatar',
      render: (value: unknown) => {
        const avatarUrl = value as string;
        return avatarUrl ? (
          <img src={avatarUrl} alt="Avatar" className="w-8 h-8 rounded-full object-cover" />
        ) : (
          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500 text-xs">N/A</span>
          </div>
        );
      },
    },
    {
      key: 'name',
      title: t('customer.form.name'),
      dataIndex: 'name',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'email',
      title: t('customer.form.email'),
      dataIndex: 'email',
      sortable: true,
      render: (value: unknown) => {
        const email = value as { primary?: string } | string | null;
        if (typeof email === 'object' && email !== null) {
          return email.primary || 'N/A';
        }
        return email || 'N/A';
      },
    },
    {
      key: 'phone',
      title: t('customer.form.phone'),
      dataIndex: 'phone',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'platform',
      title: t('customer.platform'),
      dataIndex: 'platform',
      sortable: true,
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'timezone',
      title: t('customer.timezone'),
      dataIndex: 'timezone',
      render: (value: unknown) => (value as string) || 'N/A',
    },
    {
      key: 'createdAt',
      title: t('common.createdAt'),
      dataIndex: 'createdAt',
      sortable: true,
      render: (value: unknown) => {
        try {
          if (!value) return 'N/A';

          let date: Date;

          if (typeof value === 'number') {
            date = new Date(value);
          } else if (typeof value === 'string') {
            if (/^\d+$/.test(value)) {
              date = new Date(Number(value));
            } else {
              date = new Date(value);
            }
          } else if (value instanceof Date) {
            date = value;
          } else {
            return 'N/A';
          }

          if (isNaN(date.getTime())) {
            return 'N/A';
          }

          return date.toLocaleDateString('vi-VN');
        } catch (error) {
          console.error('Error formatting date:', error, 'Value:', value);
          return 'N/A';
        }
      },
    },
    {
      key: 'actions',
      title: t('common.actions'),
      render: (_, record) => {
        return (
          <div className="flex space-x-2">
            <Tooltip content={t('common.view')}>
              <IconCard
                icon="eye"
                variant="ghost"
                size="sm"
                onClick={() => handleViewCustomer(record)}
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // Xử lý thêm mới
  const handleAdd = () => {
    // Clear form errors khi mở form mới
    setFormErrors({});
    showAddForm();
  };

  // Xử lý xem chi tiết khách hàng
  const handleViewCustomer = (customer: UserConvertCustomerListItemDto) => {
    setSelectedCustomer(customer);
    showDetail();
  };

  // Xử lý thay đổi platform filter
  const handlePlatformFilter = (selectedPlatform: string) => {
    setPlatform(selectedPlatform === 'all' ? '' : selectedPlatform);
    setCurrentPage(1); // Reset về trang đầu khi filter
  };

  // Xử lý khi tạo khách hàng thành công
  const handleCustomerSuccess = () => {
    // Clear form errors và đóng form khi thành công
    setFormErrors({});
    hideAddForm();
  };

  // Xử lý hủy form thêm khách hàng
  const handleCancelAdd = () => {
    // Clear form errors khi hủy form
    setFormErrors({});
    hideAddForm();
  };

  // Xử lý đóng detail view
  const handleCloseDetail = () => {
    setSelectedCustomer(null);
    hideDetail();
  };

  // Xử lý import khách hàng
  const handleImport = () => {
    showImport();
  };

  // Xử lý khi import hoàn thành
  const handleImportComplete = (importedCount: number) => {
    hideImport();
    // Refresh data
    refetch();
    // Show success message
    alert(`Đã import thành công ${importedCount} khách hàng!`);
  };

  // Hiển thị loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }

  // Hiển thị error state
  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-4">Có lỗi xảy ra khi tải dữ liệu</p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={setSearchTerm}
        onAdd={handleAdd}
        items={[
          {
            id: 'all',
            label: t('common.all'),
            icon: 'list',
            onClick: () => handlePlatformFilter('all'),
          },
          {
            id: 'facebook',
            label: 'Facebook',
            icon: 'facebook',
            onClick: () => handlePlatformFilter('Facebook'),
          },
          {
            id: 'zalo',
            label: 'Zalo',
            icon: 'message-circle',
            onClick: () => handlePlatformFilter('Zalo'),
          },
          {
            id: 'website',
            label: 'Website',
            icon: 'globe',
            onClick: () => handlePlatformFilter('Website'),
          },
        ]}
        additionalIcons={[
          {
            tooltip: t('customer.import.title'),
            icon: 'upload',
            onClick: handleImport,
          },
        ]}
      />

      {/* Form thêm khách hàng */}
      <SlideInForm isVisible={isAddFormVisible}>
          <CustomerForm
            formRef={formRef}
            onSuccess={handleCustomerSuccess}
            onCancel={handleCancelAdd}
            title={t('customer.addForm')}
          />
      </SlideInForm>

      {/* Detail view khách hàng */}
      <SlideInForm isVisible={isDetailVisible}>
        {selectedCustomer && (
          <CustomerDetailView
            customer={mapToCustomerDetailData(selectedCustomer)}
            onClose={handleCloseDetail}
          />
        )}
      </SlideInForm>

      {/* Import form */}
      <SlideInForm isVisible={isImportVisible}>
        <CustomerImport
          onClose={hideImport}
          onImportComplete={handleImportComplete}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={customerData?.items || []}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: customerData?.meta?.totalItems || 0,
            showSizeChanger: true,
            onChange: (page: number, size?: number) => {
              setCurrentPage(page);
              if (size && size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
        />
      </Card>
    </div>
  );
};

export default CustomerPage;
