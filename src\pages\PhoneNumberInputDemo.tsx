import React, { useState } from 'react';

import {
  Typography,
  Card,
  PhoneNumberInput,
  CountryFlag,
  Button,
  Form,
  FormItem,
  Input,
  ResponsiveGrid,
  CodeBlock,
  Divider,
  Badge,
  Alert,
} from '@/shared/components/common';
import { Country } from '@/shared/data/countries';

const PhoneNumberInputDemo: React.FC = () => {
  const [phoneValue, setPhoneValue] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);

  const handlePhoneChange = (value: string, country: Country) => {
    setPhoneValue(value);
    setSelectedCountry(country);
    console.log('Phone changed:', { value, country });
  };

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    console.log('Country changed:', country);
  };

  const phoneVariants = [
    {
      title: 'Default Phone Input',
      description: 'Standard phone input with country selector',
      component: (
        <PhoneNumberInput
          value={phoneValue}
          onChange={handlePhoneChange}
          onCountryChange={handleCountryChange}
          placeholder="Enter phone number"
        />
      ),
      code: `<PhoneNumberInput
  value={phoneValue}
  onChange={handlePhoneChange}
  onCountryChange={handleCountryChange}
  placeholder="Enter phone number"
/>`,
    },
    {
      title: 'Small Size',
      description: 'Compact phone input for tight spaces',
      component: (
        <PhoneNumberInput
          value=""
          onChange={handlePhoneChange}
          placeholder="Phone number"
          size="small"
        />
      ),
      code: `<PhoneNumberInput
  size="small"
  placeholder="Phone number"
/>`,
    },
    {
      title: 'Large Size',
      description: 'Large phone input for better visibility',
      component: (
        <PhoneNumberInput
          value=""
          onChange={handlePhoneChange}
          placeholder="Phone number"
          size="large"
        />
      ),
      code: `<PhoneNumberInput
  size="large"
  placeholder="Phone number"
/>`,
    },
    {
      title: 'Filled Variant',
      description: 'Phone input with filled background',
      component: (
        <PhoneNumberInput
          value=""
          onChange={handlePhoneChange}
          placeholder="Phone number"
          variant="filled"
        />
      ),
      code: `<PhoneNumberInput
  variant="filled"
  placeholder="Phone number"
/>`,
    },
    {
      title: 'Standard Variant',
      description: 'Phone input with bottom border only',
      component: (
        <PhoneNumberInput
          value=""
          onChange={handlePhoneChange}
          placeholder="Phone number"
          variant="standard"
        />
      ),
      code: `<PhoneNumberInput
  variant="standard"
  placeholder="Phone number"
/>`,
    },
    {
      title: 'Error State',
      description: 'Phone input showing error state',
      component: (
        <PhoneNumberInput
          value=""
          onChange={handlePhoneChange}
          placeholder="Phone number"
          error={true}
        />
      ),
      code: `<PhoneNumberInput
  error={true}
  placeholder="Phone number"
/>`,
    },
    {
      title: 'Disabled State',
      description: 'Disabled phone input',
      component: (
        <PhoneNumberInput
          value="+84 123 456 789"
          onChange={handlePhoneChange}
          placeholder="Phone number"
          disabled={true}
        />
      ),
      code: `<PhoneNumberInput
  value="+84 123 456 789"
  disabled={true}
  placeholder="Phone number"
/>`,
    },
    {
      title: 'Without Dial Code Display',
      description: 'Phone input that only shows local number',
      component: (
        <PhoneNumberInput
          value=""
          onChange={handlePhoneChange}
          placeholder="Phone number"
          showDialCode={false}
        />
      ),
      code: `<PhoneNumberInput
  showDialCode={false}
  placeholder="Phone number"
/>`,
    },
    {
      title: 'US Default Country',
      description: 'Phone input with US as default country',
      component: (
        <PhoneNumberInput
          value=""
          onChange={handlePhoneChange}
          placeholder="Phone number"
          defaultCountry="US"
        />
      ),
      code: `<PhoneNumberInput
  defaultCountry="US"
  placeholder="Phone number"
/>`,
    },
    {
      title: 'Different Countries',
      description: 'Phone inputs with different default countries',
      component: (
        <div className="space-y-3">
          <div>
            <Typography variant="body2" className="mb-1 text-muted-foreground">Japan (+81)</Typography>
            <PhoneNumberInput
              onChange={handlePhoneChange}
              placeholder="Phone number"
              defaultCountry="JP"
            />
          </div>
          <div>
            <Typography variant="body2" className="mb-1 text-muted-foreground">Singapore (+65)</Typography>
            <PhoneNumberInput
              onChange={handlePhoneChange}
              placeholder="Phone number"
              defaultCountry="SG"
            />
          </div>
          <div>
            <Typography variant="body2" className="mb-1 text-muted-foreground">Germany (+49)</Typography>
            <PhoneNumberInput
              onChange={handlePhoneChange}
              placeholder="Phone number"
              defaultCountry="DE"
            />
          </div>
        </div>
      ),
      code: `<PhoneNumberInput defaultCountry="JP" />
<PhoneNumberInput defaultCountry="SG" />
<PhoneNumberInput defaultCountry="DE" />`,
    },
  ];

  const basicUsageCode = `import { PhoneNumberInput } from '@/shared/components/common';
import { Country } from '@/shared/data/countries';

const [phoneValue, setPhoneValue] = useState('');
const [selectedCountry, setSelectedCountry] = useState<Country | null>(null);

const handlePhoneChange = (value: string, country: Country) => {
  setPhoneValue(value);
  setSelectedCountry(country);
};

const handleCountryChange = (country: Country) => {
  setSelectedCountry(country);
};

<PhoneNumberInput
  value={phoneValue}
  onChange={handlePhoneChange}
  onCountryChange={handleCountryChange}
  placeholder="Enter phone number"
  defaultCountry="VN"
/>`;

  const formUsageCode = `<Form>
  <FormItem label="Phone Number" name="phone" required>
    <PhoneNumberInput
      placeholder="Enter your phone number"
      defaultCountry="VN"
    />
  </FormItem>
  <FormItem label="Email" name="email">
    <Input type="email" placeholder="Enter your email" />
  </FormItem>
  <Button type="submit" variant="primary">
    Submit
  </Button>
</Form>`;

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-8">
        {/* Header */}
        <div>
          <Typography variant="h1" className="mb-4">
            PhoneNumberInput Component
          </Typography>
          <Typography variant="body1" className="text-muted-foreground mb-6">
            International phone number input with country selection, flag display, and validation.
            Features include country search, multiple variants, and form integration.
          </Typography>
          
          <Alert type="info" message="This component supports 38+ countries with their respective flags and dial codes." />
        </div>

        {/* Basic Usage */}
        <Card className="p-6">
          <Typography variant="h2" className="mb-4">Basic Usage</Typography>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            Import and use the PhoneNumberInput component with country change handlers.
          </Typography>

          <div className="border border-border rounded-lg p-4 bg-muted mb-4">
            <PhoneNumberInput
              value={phoneValue}
              onChange={handlePhoneChange}
              onCountryChange={handleCountryChange}
              placeholder="Enter phone number"
            />
          </div>
          
          <CodeBlock language="tsx" code={basicUsageCode} />
        </Card>

        {/* Variants Grid */}
        <div>
          <Typography variant="h2" className="mb-6">Component Variants</Typography>
          <ResponsiveGrid maxColumns={{ xs: 1, md: 2 }}>
            {phoneVariants.map((variant, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-start justify-between mb-2">
                  <Typography variant="h3" className="flex-1">
                    {variant.title}
                  </Typography>
                  <Badge variant="secondary" className="ml-2">
                    Demo
                  </Badge>
                </div>
                <Typography variant="body2" className="text-muted-foreground mb-4">
                  {variant.description}
                </Typography>
                <div className="border border-border rounded-lg p-4 bg-muted mb-4">
                  {variant.component}
                </div>
                <CodeBlock language="tsx" code={variant.code} />
              </Card>
            ))}
          </ResponsiveGrid>
        </div>

        <Divider />

        {/* Form Integration */}
        <Card className="p-6">
          <Typography variant="h2" className="mb-4">Form Integration</Typography>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            Use PhoneNumberInput within forms for user registration, contact forms, etc.
          </Typography>

          <div className="border border-border rounded-lg p-4 bg-muted mb-4">
            <Form onSubmit={() => console.log('Form submitted')}>
              <FormItem label="Phone Number" name="phone" required>
                <PhoneNumberInput
                  placeholder="Enter your phone number"
                  defaultCountry="VN"
                />
              </FormItem>
              <FormItem label="Email" name="email">
                <Input type="email" placeholder="Enter your email" />
              </FormItem>
              <Button type="submit" variant="primary">
                Submit
              </Button>
            </Form>
          </div>
          
          <CodeBlock language="tsx" code={formUsageCode} />
        </Card>

        {/* Selected Country Info */}
        {selectedCountry && (
          <Card className="p-6 bg-primary/5 border-primary/20">
            <Typography variant="h2" className="mb-4 text-primary">
              Selected Country Information
            </Typography>
            <ResponsiveGrid maxColumns={{ xs: 2, md: 4 }} gap={4}>
              <div>
                <Typography variant="body2" className="font-medium text-primary mb-1">
                  Flag
                </Typography>
                <div className="flex items-center space-x-2">
                  <CountryFlag country={selectedCountry} size="lg" />
                  <Typography variant="body1">{selectedCountry.flag}</Typography>
                </div>
              </div>
              <div>
                <Typography variant="body2" className="font-medium text-primary mb-1">
                  Country
                </Typography>
                <Typography variant="body1">{selectedCountry.name}</Typography>
              </div>
              <div>
                <Typography variant="body2" className="font-medium text-primary mb-1">
                  Code
                </Typography>
                <Typography variant="body1">{selectedCountry.code}</Typography>
              </div>
              <div>
                <Typography variant="body2" className="font-medium text-primary mb-1">
                  Dial Code
                </Typography>
                <Typography variant="body1">{selectedCountry.dialCode}</Typography>
              </div>
            </ResponsiveGrid>
            {phoneValue && (
              <div className="mt-4">
                <Typography variant="body2" className="font-medium text-primary mb-1">
                  Full Phone Number
                </Typography>
                <Typography variant="body1" className="font-mono bg-background px-3 py-2 rounded border border-border">
                  {phoneValue}
                </Typography>
              </div>
            )}
          </Card>
        )}
      </div>
    </div>
  );
};

export default PhoneNumberInputDemo;
