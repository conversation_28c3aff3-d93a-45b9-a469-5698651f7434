import { useQuery } from '@tanstack/react-query';
import { REPORT_QUERY_KEYS } from '../constants/report-query-keys';
import { ReportService } from '../services/report.service';
import {
  ReportOverviewQueryDto,
  SalesChartQueryDto,
  OrdersChartQueryDto,
  CustomersChartQueryDto,
  ProductsChartQueryDto,
  TopSellingProductsQueryDto,
  PotentialCustomersQueryDto,
} from '../types/report.types';

/**
 * Hook để lấy dữ liệu tổng quan báo cáo
 */
export const useReportOverview = (params?: ReportOverviewQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.overviewWithParams(params || {}),
    queryFn: () => ReportService.getReportOverview(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy dữ liệu biểu đồ doanh thu
 */
export const useSalesChart = (params?: SalesChartQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.salesChartWithParams(params || {}),
    queryFn: () => ReportService.getSalesChart(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy dữ liệu biểu đồ đơn hàng
 */
export const useOrdersChart = (params?: OrdersChartQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.ordersChartWithParams(params || {}),
    queryFn: () => ReportService.getOrdersChart(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy dữ liệu biểu đồ khách hàng
 */
export const useCustomersChart = (params?: CustomersChartQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.customersChartWithParams(params || {}),
    queryFn: () => ReportService.getCustomersChart(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy dữ liệu biểu đồ sản phẩm
 */
export const useProductsChart = (params?: ProductsChartQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.productsChartWithParams(params || {}),
    queryFn: () => ReportService.getProductsChart(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy danh sách sản phẩm bán chạy
 */
export const useTopSellingProducts = (params?: TopSellingProductsQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.topSellingProductsWithParams(params || {}),
    queryFn: () => ReportService.getTopSellingProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.result,
  });
};

/**
 * Hook để lấy danh sách khách hàng tiềm năng
 */
export const usePotentialCustomers = (params?: PotentialCustomersQueryDto) => {
  return useQuery({
    queryKey: REPORT_QUERY_KEYS.potentialCustomersWithParams(params || {}),
    queryFn: () => ReportService.getPotentialCustomers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => data.result,
  });
};

/**
 * Hook tổng hợp cho quản lý dữ liệu báo cáo
 */
export const useReportData = () => {
  return {
    useReportOverview,
    useSalesChart,
    useOrdersChart,
    useCustomersChart,
    useProductsChart,
    useTopSellingProducts,
    usePotentialCustomers,
  };
};

export default useReportData;
