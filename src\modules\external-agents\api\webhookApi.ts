import { apiClient } from '@/shared/api';
import { EXTERNAL_AGENT_ENDPOINTS } from '../constants';
import {
  WebhookConfig,
  WebhookDelivery,
  PaginatedResponse,
} from '../types';

export const webhookApi = {
  // Get all webhooks
  getWebhooks: async (params?: { page?: number; limit?: number }): Promise<PaginatedResponse<WebhookConfig>> => {
    const response = await apiClient.get<PaginatedResponse<WebhookConfig>>(
      EXTERNAL_AGENT_ENDPOINTS.WEBHOOKS,
      { params }
    );
    return response.result;
  },

  // Get webhook by ID
  getWebhook: async (id: string): Promise<WebhookConfig> => {
    const response = await apiClient.get<WebhookConfig>(
      EXTERNAL_AGENT_ENDPOINTS.WEBHOOK_DETAIL(id)
    );
    return response.result;
  },

  // Create webhook
  createWebhook: async (data: Omit<WebhookConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<WebhookConfig> => {
    const response = await apiClient.post<WebhookConfig>(
      EXTERNAL_AGENT_ENDPOINTS.WEBHOOKS,
      data
    );
    return response.result;
  },

  // Update webhook
  updateWebhook: async (id: string, data: Partial<WebhookConfig>): Promise<WebhookConfig> => {
    const response = await apiClient.put<WebhookConfig>(
      EXTERNAL_AGENT_ENDPOINTS.WEBHOOK_DETAIL(id),
      data
    );
    return response.result;
  },

  // Delete webhook
  deleteWebhook: async (id: string): Promise<void> => {
    await apiClient.delete(EXTERNAL_AGENT_ENDPOINTS.WEBHOOK_DETAIL(id));
  },

  // Get webhook deliveries
  getWebhookDeliveries: async (webhookId: string, params?: { page?: number; limit?: number }): Promise<PaginatedResponse<WebhookDelivery>> => {
    const response = await apiClient.get<PaginatedResponse<WebhookDelivery>>(
      EXTERNAL_AGENT_ENDPOINTS.WEBHOOK_DELIVERIES(webhookId),
      { params }
    );
    return response.result;
  },

  // Retry webhook delivery
  retryWebhookDelivery: async (deliveryId: string): Promise<void> => {
    await apiClient.post(`${EXTERNAL_AGENT_ENDPOINTS.WEBHOOKS}/deliveries/${deliveryId}/retry`);
  },

  // Test webhook
  testWebhook: async (id: string): Promise<{ success: boolean; error?: string }> => {
    const response = await apiClient.post<{ success: boolean; error?: string }>(
      `${EXTERNAL_AGENT_ENDPOINTS.WEBHOOK_DETAIL(id)}/test`
    );
    return response.result;
  },
};
