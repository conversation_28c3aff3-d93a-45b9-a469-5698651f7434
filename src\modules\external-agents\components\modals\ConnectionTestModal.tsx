import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Button
} from '@/shared/components/common';
import ConnectionTester from '../common/ConnectionTester';
import { ConnectionTestResult } from '../../types';

interface ConnectionTestModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId?: string;
  agentName?: string;
  endpoint?: string;
  onTestComplete?: (result: ConnectionTestResult) => void;
}

const ConnectionTestModal: React.FC<ConnectionTestModalProps> = ({
  isOpen,
  onClose,
  agentId,
  agentName,
  endpoint,
  onTestComplete,
}) => {
  const { t } = useTranslation(['external-agents']);

  const handleTestComplete = (result: ConnectionTestResult) => {
    onTestComplete?.(result);
  };

  const footer = (
    <div className="flex justify-end">
      <Button variant="outline" onClick={onClose}>
        {t('external-agents:actions.close')}
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={agentName
        ? t('external-agents:connection.testAgent', { name: agentName })
        : t('external-agents:connection.testConnection')
      }
      size="lg"
      footer={footer}
    >
      <div className="py-4">
        <ConnectionTester
          agentId={agentId}
          endpoint={endpoint}
          onTestComplete={handleTestComplete}
          showHistory={true}
        />
      </div>
    </Modal>
  );
};

export default ConnectionTestModal;
