import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Icon,
  Loading,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';

interface FacebookAuthCallbackProps {
  /**
   * Redirect path sau khi authentication thành công
   */
  successRedirect?: string;
  
  /**
   * Redirect path khi có lỗi
   */
  errorRedirect?: string;
  
  /**
   * Callback khi authentication thành công
   */
  onSuccess?: () => void;
  
  /**
   * Callback khi có lỗi
   */
  onError?: (error: Error) => void;
}

/**
 * Facebook OAuth Callback Handler Component
 * Xử lý callback từ Facebook OAuth và hiển thị trạng thái
 */
const FacebookAuthCallback: React.FC<FacebookAuthCallbackProps> = ({
  successRedirect = '/marketing/facebook-ads',
  errorRedirect = '/marketing/facebook-ads',
  onSuccess,
  onError,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');
  
  const { handleCallback } = useFacebookAuth();

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Get parameters from URL
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // Check for OAuth errors
        if (error) {
          throw new Error(errorDescription || error);
        }

        // Validate required parameters
        if (!code || !state) {
          throw new Error(t('marketing:facebookAds.auth.errors.missingParams', 'Thiếu thông số xác thực'));
        }

        // Get current URL without parameters for redirect URI
        const redirectUri = window.location.origin + window.location.pathname;

        // Handle the callback
        await handleCallback(code, state, redirectUri);

        setStatus('success');
        onSuccess?.();

        // Redirect after a short delay
        setTimeout(() => {
          navigate(successRedirect);
        }, 2000);

      } catch (err) {
        console.error('Facebook auth callback error:', err);
        
        const error = err instanceof Error ? err : new Error('Unknown error');
        setErrorMessage(error.message);
        setStatus('error');
        onError?.(error);

        // Redirect to error page after delay
        setTimeout(() => {
          navigate(errorRedirect);
        }, 5000);
      }
    };

    processCallback();
  }, [searchParams, handleCallback, navigate, successRedirect, errorRedirect, onSuccess, onError, t]);

  const handleRetry = () => {
    navigate(errorRedirect);
  };

  const handleContinue = () => {
    navigate(successRedirect);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md p-8 text-center">
        {status === 'loading' && (
          <>
            <div className="mb-6">
              <Loading size="lg" />
            </div>
            <Typography variant="h5" className="mb-2">
              {t('marketing:facebookAds.auth.callback.processing', 'Đang xử lý xác thực...')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('marketing:facebookAds.auth.callback.wait', 'Vui lòng đợi trong giây lát')}
            </Typography>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <Icon name="check" size="xl" className="text-green-600" />
              </div>
            </div>
            <Typography variant="h5" className="mb-2 text-green-600">
              {t('marketing:facebookAds.auth.callback.success', 'Kết nối thành công!')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mb-6">
              {t('marketing:facebookAds.auth.callback.successDesc', 'Tài khoản Facebook đã được kết nối thành công. Bạn sẽ được chuyển hướng...')}
            </Typography>
            <Button
              variant="primary"
              onClick={handleContinue}
              className="w-full"
            >
              <Icon name="arrow-right" className="mr-2" />
              {t('marketing:facebookAds.auth.callback.continue', 'Tiếp tục')}
            </Button>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                <Icon name="x" size="xl" className="text-red-600" />
              </div>
            </div>
            <Typography variant="h5" className="mb-2 text-red-600">
              {t('marketing:facebookAds.auth.callback.error', 'Kết nối thất bại')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mb-4">
              {errorMessage || t('marketing:facebookAds.auth.callback.errorDesc', 'Có lỗi xảy ra trong quá trình kết nối với Facebook.')}
            </Typography>
            
            {/* Common error messages */}
            <div className="text-left bg-muted p-3 rounded-md mb-6">
              <Typography variant="caption" className="font-medium mb-2 block">
                {t('marketing:facebookAds.auth.callback.commonErrors', 'Nguyên nhân có thể:')}
              </Typography>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• {t('marketing:facebookAds.auth.errors.userDenied', 'Người dùng từ chối cấp quyền')}</li>
                <li>• {t('marketing:facebookAds.auth.errors.invalidState', 'Phiên xác thực không hợp lệ')}</li>
                <li>• {t('marketing:facebookAds.auth.errors.networkError', 'Lỗi kết nối mạng')}</li>
                <li>• {t('marketing:facebookAds.auth.errors.serverError', 'Lỗi máy chủ')}</li>
              </ul>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handleRetry}
                className="flex-1"
              >
                <Icon name="arrow-left" className="mr-2" />
                {t('common:button.back', 'Quay lại')}
              </Button>
              <Button
                variant="primary"
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                <Icon name="refresh-cw" className="mr-2" />
                {t('common:button.retry', 'Thử lại')}
              </Button>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default FacebookAuthCallback;
