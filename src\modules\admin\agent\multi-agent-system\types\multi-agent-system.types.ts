/**
 * Enum cho trạng thái multi-agent system
 */
export enum MultiAgentSystemStatusEnum {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DEPRECATED = 'DEPRECATED',
}

/**
 * Enum cho các trường sắp xếp
 */
export enum MultiAgentSystemSortBy {
  ID = 'id',
  NAME = 'name',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  employeeId: string;
  name: string;
  avatar: string | null;
  date?: Date;
}

/**
 * Interface cho multi-agent system trong danh sách
 */
export interface MultiAgentSystemListItem {
  id: string;
  name: string;
  description: string | null;
  status: MultiAgentSystemStatusEnum;
  agentCount: number;
  createdAt: number;
}

/**
 * Interface cho thông tin chi tiết multi-agent system
 */
export interface MultiAgentSystemDetail {
  id: string;
  name: string;
  description: string | null;
  status: MultiAgentSystemStatusEnum;
  configuration: Record<string, unknown>;
  agents: Array<{
    id: string;
    name: string;
    role: string;
  }>;
  created?: EmployeeInfo;
  updated?: EmployeeInfo;
  deleted?: EmployeeInfo;
}

/**
 * Interface cho tham số tạo multi-agent system
 */
export interface CreateMultiAgentSystemParams {
  name: string;
  description?: string;
  configuration: Record<string, unknown>;
  status?: MultiAgentSystemStatusEnum;
}

/**
 * Interface cho tham số cập nhật multi-agent system
 */
export interface UpdateMultiAgentSystemParams {
  name?: string;
  description?: string;
  configuration?: Record<string, unknown>;
  status?: MultiAgentSystemStatusEnum;
}

/**
 * Interface cho tham số query
 */
export interface MultiAgentSystemQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: MultiAgentSystemStatusEnum;
  sortBy?: MultiAgentSystemSortBy;
  sortDirection?: SortDirection;
}
