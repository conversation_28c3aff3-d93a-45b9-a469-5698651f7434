import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Input,
  Textarea,
  Select,
  FormItem,
  Card,
} from '@/shared/components/common';
import { PackageType } from '../../types/subscription-plans.admin.types';

interface SubscriptionPlanFormProps {
  onSubmit: (data: Record<string, unknown>) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form tạo gói dịch vụ subscription mới
 */
const SubscriptionPlanForm: React.FC<SubscriptionPlanFormProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho form fields
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    packageType: PackageType.TIME_ONLY,
  });

  // State cho errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Xử lý thay đổi name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, name: value }));
    if (errors.name) {
      setErrors(prev => ({ ...prev, name: '' }));
    }
  };

  // Xử lý thay đổi description
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, description: value }));
    if (errors.description) {
      setErrors(prev => ({ ...prev, description: '' }));
    }
  };

  // Xử lý thay đổi packageType
  const handlePackageTypeChange = (value: string | number | string[] | number[]) => {
    setFormData(prev => ({ ...prev, packageType: value as PackageType }));
    if (errors.packageType) {
      setErrors(prev => ({ ...prev, packageType: '' }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên gói dịch vụ là bắt buộc';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Mô tả là bắt buộc';
    }

    if (!formData.packageType) {
      newErrors.packageType = 'Loại gói dịch vụ là bắt buộc';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Xử lý submit form
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData as Record<string, unknown>);
      // Reset form sau khi submit thành công
      setFormData({
        name: '',
        description: '',
        packageType: PackageType.TIME_ONLY,
      });
      setErrors({});
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Xử lý hủy
  const handleCancel = () => {
    setFormData({
      name: '',
      description: '',
      packageType: PackageType.TIME_ONLY,
    });
    setErrors({});
    onCancel();
  };

  // Options cho package type
  const packageTypeOptions = [
    {
      value: PackageType.TIME_ONLY,
      label: t('admin:subscription.packageType.timeOnly'),
    },
    {
      value: PackageType.HYBRID,
      label: t('admin:subscription.packageType.hybrid'),
    },
  ];

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h3" className="mb-6">
          {t('admin:subscription.plan.form.create.title')}
        </Typography>

        <form onSubmit={handleFormSubmit} className="space-y-6">
          {/* Tên gói dịch vụ */}
          <FormItem
            label={t('admin:subscription.plan.form.name.label')}
            required
          >
            <Input
              value={formData.name}
              onChange={handleNameChange}
              placeholder={t('admin:subscription.plan.form.name.placeholder')}
              error={errors.name}
              fullWidth
            />
          </FormItem>

          {/* Mô tả */}
          <FormItem
            label={t('admin:subscription.plan.form.description.label')}
            required
          >
            <Textarea
              value={formData.description}
              onChange={handleDescriptionChange}
              placeholder={t('admin:subscription.plan.form.description.placeholder')}
              rows={4}
              status={errors.description ? 'error' : 'default'}
              fullWidth
            />
          </FormItem>

          {/* Loại gói dịch vụ */}
          <FormItem
            label={t('admin:subscription.plan.form.packageType.label')}
            required
          >
            <Select
              value={formData.packageType}
              onChange={handlePackageTypeChange}
              options={packageTypeOptions}
              placeholder={t('admin:subscription.plan.form.packageType.placeholder')}
              error={errors.packageType}
              fullWidth
            />
          </FormItem>

          {/* Buttons */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              {t('admin:subscription.plan.form.create.submit')}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default SubscriptionPlanForm;
