import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Upload, Download, FileSpreadsheet, Type, AlertCircle } from 'lucide-react';
import {
  Card,
  Typography,
  Button,
  Input,
  FormItem,
  Alert,
  Chip,
} from '@/shared/components/common';
import type { EmailTemplateDto, EmailVariable } from '../../types/email.types';
import { ExcelVariableImporter } from './ExcelVariableImporter';

interface TemplateVariableManagerProps {
  template: EmailTemplateDto;
  variables: Record<string, string>;
  onChange: (variables: Record<string, string>) => void;
  onClose: () => void;
}

/**
 * Component quản lý biến trong email template
 */
export function TemplateVariableManager({
  template,
  variables,
  onChange,
  onClose,
}: TemplateVariableManagerProps) {
  const { t } = useTranslation(['marketing', 'common']);
  
  // State
  const [showExcelImporter, setShowExcelImporter] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Handle individual variable change
  const handleVariableChange = useCallback((variableName: string, value: string) => {
    const newVariables = { ...variables, [variableName]: value };
    
    // Clear validation error for this field
    if (validationErrors[variableName]) {
      const newErrors = { ...validationErrors };
      delete newErrors[variableName];
      setValidationErrors(newErrors);
    }
    
    onChange(newVariables);
  }, [variables, onChange, validationErrors]);

  // Validate variables
  const validateVariables = useCallback(() => {
    const errors: Record<string, string> = {};
    
    template.variables.forEach(variable => {
      if (variable.required && (!variables[variable.name] || variables[variable.name].trim() === '')) {
        errors[variable.name] = t('marketing:email.template.variables.required', 'Trường này là bắt buộc');
      }
    });
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [template.variables, variables, t]);

  // Handle Excel import
  const handleExcelImport = useCallback((importedVariables: Record<string, string>) => {
    // Merge with existing variables, prioritizing imported values
    const mergedVariables = { ...variables, ...importedVariables };
    onChange(mergedVariables);
    setShowExcelImporter(false);
  }, [variables, onChange]);

  // Export variables to Excel format
  const handleExportTemplate = useCallback(() => {
    const csvContent = [
      // Header row
      ['Variable Name', 'Current Value', 'Type', 'Required', 'Description'],
      // Data rows
      ...template.variables.map(variable => [
        variable.name,
        variables[variable.name] || '',
        variable.type,
        variable.required ? 'Yes' : 'No',
        variable.description || ''
      ])
    ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${template.name}_variables.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [template, variables]);

  // Get variable type icon
  const getVariableTypeIcon = (type: EmailVariable['type']) => {
    switch (type) {
      case 'TEXT':
        return <Type className="h-4 w-4" />;
      case 'NUMBER':
        return <span className="text-xs font-mono">#</span>;
      case 'DATE':
        return <span className="text-xs font-mono">📅</span>;
      case 'URL':
        return <span className="text-xs font-mono">🔗</span>;
      case 'IMAGE':
        return <span className="text-xs font-mono">🖼️</span>;
      default:
        return <Type className="h-4 w-4" />;
    }
  };

  return (
    <Card
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5 text-primary" />
            <Typography variant="h6">
              {t('marketing:email.template.variables.manager.title', 'Quản lý biến Template')}
            </Typography>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      }
      subtitle={t('marketing:email.template.variables.manager.description', 'Nhập giá trị cho các biến trong template hoặc import từ Excel')}
    >
      <div className="space-y-6">
        {/* Action Buttons */}
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExcelImporter(true)}
          >
            <Upload className="h-4 w-4 mr-1" />
            {t('marketing:email.template.variables.importExcel', 'Import Excel')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportTemplate}
          >
            <Download className="h-4 w-4 mr-1" />
            {t('marketing:email.template.variables.exportTemplate', 'Export Template')}
          </Button>
        </div>

        {/* Variables List */}
        <div className="space-y-4">
          <Typography variant="h6" className="flex items-center gap-2">
            {t('marketing:email.template.variables.list', 'Danh sách biến')}
            <Chip variant="info" size="sm">
              {template.variables.length}
            </Chip>
          </Typography>

          {template.variables.length === 0 ? (
            <Alert
              type="info"
              message={t('marketing:email.template.variables.noVariables', 'Template này không có biến nào')}
            />
          ) : (
            <div className="space-y-3">
              {template.variables.map((variable) => (
                <div key={variable.name} className="p-4 border rounded-lg bg-muted/20">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getVariableTypeIcon(variable.type)}
                      <Typography variant="body2" weight="medium">
                        {variable.name}
                      </Typography>
                      {variable.required && (
                        <span className="text-red-500 text-sm">*</span>
                      )}
                      <Chip variant="default" size="sm">
                        {variable.type}
                      </Chip>
                    </div>
                  </div>

                  {variable.description && (
                    <Typography variant="caption" color="muted" className="mb-2 block">
                      {variable.description}
                    </Typography>
                  )}

                  <FormItem name={`variable_${variable.name}`}>
                    <Input
                      value={variables[variable.name] || ''}
                      onChange={(e) => handleVariableChange(variable.name, e.target.value)}
                      placeholder={variable.defaultValue || t('marketing:email.template.variables.placeholder', 'Nhập giá trị...')}
                      type={variable.type === 'NUMBER' ? 'number' : variable.type === 'URL' ? 'url' : 'text'}
                      error={validationErrors[variable.name]}
                      fullWidth
                    />
                  </FormItem>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Validation Errors */}
        {Object.keys(validationErrors).length > 0 && (
          <Alert
            type="error"
            message={t('marketing:email.template.variables.validationError', 'Có lỗi trong dữ liệu biến')}
            description={
              <ul className="mt-2 space-y-1">
                {Object.entries(validationErrors).map(([field, error]) => (
                  <li key={field} className="flex items-center gap-2">
                    <AlertCircle className="h-3 w-3" />
                    <span className="font-medium">{field}:</span> {error}
                  </li>
                ))}
              </ul>
            }
          />
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            {t('common:close', 'Đóng')}
          </Button>
          <Button onClick={validateVariables}>
            {t('marketing:email.template.variables.validate', 'Kiểm tra')}
          </Button>
        </div>
      </div>

      {/* Excel Importer Modal */}
      {showExcelImporter && (
        <ExcelVariableImporter
          template={template}
          onImport={handleExcelImport}
          onClose={() => setShowExcelImporter(false)}
        />
      )}
    </Card>
  );
}

export default TemplateVariableManager;
