import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon } from '@/shared/components/common';
import { AgentCapability } from '../../types';

interface CapabilityMatrixProps {
  capabilities: AgentCapability[];
  allCapabilities?: AgentCapability[];
  size?: 'sm' | 'md' | 'lg';
  layout?: 'grid' | 'list' | 'compact';
  showUnsupported?: boolean;
  className?: string;
}

const CapabilityMatrix: React.FC<CapabilityMatrixProps> = ({
  capabilities,
  allCapabilities = Object.values(AgentCapability),
  size = 'md',
  layout = 'grid',
  showUnsupported = true,
  className,
}) => {
  const { t } = useTranslation(['external-agents']);

  const getCapabilityIcon = (capability: AgentCapability) => {
    switch (capability) {
      case AgentCapability.TEXT_PROCESSING:
        return 'type';
      case AgentCapability.IMAGE_ANALYSIS:
        return 'image';
      case AgentCapability.DATA_RETRIEVAL:
        return 'database';
      case AgentCapability.FILE_OPERATIONS:
        return 'file';
      case AgentCapability.API_CALLS:
        return 'globe';
      case AgentCapability.REAL_TIME_COMMUNICATION:
        return 'zap';
      case AgentCapability.CUSTOM:
        return 'settings';
      default:
        return 'help-circle';
    }
  };

  const getCapabilityLabel = (capability: AgentCapability) => {
    return t(`external-agents:capabilities.${capability}`);
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          icon: 'w-4 h-4',
          text: 'text-xs',
          spacing: 'gap-1',
          padding: 'p-2',
        };
      case 'lg':
        return {
          icon: 'w-6 h-6',
          text: 'text-base',
          spacing: 'gap-3',
          padding: 'p-4',
        };
      default:
        return {
          icon: 'w-5 h-5',
          text: 'text-sm',
          spacing: 'gap-2',
          padding: 'p-3',
        };
    }
  };

  const sizeClasses = getSizeClasses(size);

  const renderCapabilityItem = (capability: AgentCapability, isSupported: boolean) => {
    const iconName = getCapabilityIcon(capability);
    const label = getCapabilityLabel(capability);

    const itemClasses = isSupported
      ? 'text-green-700 bg-green-50 border-green-200'
      : 'text-gray-400 bg-gray-50 border-gray-200';

    if (layout === 'compact') {
      return (
        <div
          key={capability}
          className={`
            inline-flex items-center justify-center rounded-md border
            ${sizeClasses.padding}
            ${itemClasses}
          `}
          title={label}
        >
          <Icon 
            name={iconName} 
            className={`${sizeClasses.icon} ${!isSupported ? 'opacity-50' : ''}`}
          />
        </div>
      );
    }

    return (
      <div
        key={capability}
        className={`
          flex items-center rounded-md border
          ${sizeClasses.spacing}
          ${sizeClasses.padding}
          ${itemClasses}
        `}
      >
        <Icon 
          name={iconName} 
          className={`${sizeClasses.icon} ${!isSupported ? 'opacity-50' : ''}`}
        />
        <Typography 
          variant="caption"
          className={`${sizeClasses.text} font-medium ${!isSupported ? 'opacity-50' : ''}`}
        >
          {label}
        </Typography>
        {isSupported && (
          <Icon name="check" className="w-3 h-3 text-green-600 ml-auto" />
        )}
      </div>
    );
  };

  const getLayoutClasses = () => {
    switch (layout) {
      case 'list':
        return 'flex flex-col gap-2';
      case 'compact':
        return 'flex flex-wrap gap-2';
      default:
        return 'grid grid-cols-2 md:grid-cols-3 gap-2';
    }
  };

  const displayCapabilities = showUnsupported ? allCapabilities : capabilities;

  return (
    <div className={`w-full ${className}`}>
      <div className={getLayoutClasses()}>
        {displayCapabilities.map((capability) => {
          const isSupported = capabilities.includes(capability);
          
          if (!showUnsupported && !isSupported) {
            return null;
          }

          return renderCapabilityItem(capability, isSupported);
        })}
      </div>

      {/* Summary */}
      {showUnsupported && (
        <div className="mt-4 pt-3 border-t">
          <Typography variant="caption" className="text-muted-foreground">
            {t('external-agents:capabilities.supported')}: {capabilities.length} / {allCapabilities.length}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default CapabilityMatrix;
