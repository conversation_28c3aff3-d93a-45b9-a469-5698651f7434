import { useTranslation } from 'react-i18next';
import { Mail, Send, BarChart3, Calendar } from 'lucide-react';
import { StatsGrid, type StatsItem } from '@/shared/components/common';
import type { EmailCampaignDto, EmailCampaignOverviewDto } from '../../types/email.types';
import type { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Props cho EmailCampaignStatsGrid
 */
interface EmailCampaignStatsGridProps {
  /**
   * Dữ liệu overview từ API
   */
  overviewData?: EmailCampaignOverviewDto;

  /**
   * Dữ liệu campaigns để fallback
   */
  campaignsData?: PaginatedResult<EmailCampaignDto>;

  /**
   * Trạng thái loading của overview API
   */
  isOverviewLoading?: boolean;

  /**
   * Số cột tối đa cho mỗi breakpoint khi chatpanel đóng
   * @default { xs: 1, sm: 2, md: 4, lg: 4 }
   */
  maxColumns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Số cột tối đa cho mỗi breakpoint khi chatpanel mở
   * @default { xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }
   */
  maxColumnsWithChatPanel?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
}

/**
 * Component grid hiển thị thống kê Email Campaign theo quy tắc RedAI
 */
export function EmailCampaignStatsGrid({
  overviewData,
  campaignsData,
  isOverviewLoading = false,
  maxColumns = { xs: 1, sm: 2, md: 4, lg: 4 },
  maxColumnsWithChatPanel = { xs: 1, sm: 1, md: 2, lg: 3, xl: 3 },
}: EmailCampaignStatsGridProps) {
  const { t } = useTranslation(['marketing']);

  // Tính toán giá trị hiển thị với fallback
  const totalCampaigns = isOverviewLoading 
    ? '...' 
    : (overviewData?.totalCampaigns || campaignsData?.meta.totalItems || 0);

  const sendingCampaigns = isOverviewLoading 
    ? '...' 
    : (overviewData?.sendingCampaigns || 
       campaignsData?.items.filter((campaign: EmailCampaignDto) => campaign.status === 'SENDING').length || 0);

  const sentCampaigns = isOverviewLoading 
    ? '...' 
    : (overviewData?.sentCampaigns || 
       campaignsData?.items.filter((campaign: EmailCampaignDto) => campaign.status === 'SENT').length || 0);

  const scheduledCampaigns = isOverviewLoading 
    ? '...' 
    : (overviewData?.scheduledCampaigns || 
       campaignsData?.items.filter((campaign: EmailCampaignDto) => campaign.status === 'SCHEDULED').length || 0);

  // Cấu hình các cards
  const statsItems: StatsItem[] = [
    {
      title: t('marketing:email.campaigns.stats.totalCampaigns', 'Tổng chiến dịch'),
      value: totalCampaigns,
      subtitle: t('marketing:email.campaigns.stats.fromOverviewApi', 'Từ API Overview'),
      icon: Mail,
      color: 'blue',
    },
    {
      title: t('marketing:email.campaigns.stats.sending', 'Đang gửi'),
      value: sendingCampaigns,
      subtitle: t('marketing:email.campaigns.stats.active', 'Đang hoạt động'),
      icon: Send,
      color: 'orange',
    },
    {
      title: t('marketing:email.campaigns.stats.sent', 'Đã gửi'),
      value: sentCampaigns,
      subtitle: t('marketing:email.campaigns.stats.completed', 'Hoàn thành'),
      icon: BarChart3,
      color: 'green',
    },
    {
      title: t('marketing:email.campaigns.stats.scheduled', 'Đã lên lịch'),
      value: scheduledCampaigns,
      subtitle: t('marketing:email.campaigns.stats.upcoming', 'Sắp tới'),
      icon: Calendar,
      color: 'purple',
    },
  ];

  return (
    <StatsGrid
      items={statsItems}
      isLoading={isOverviewLoading}
      maxColumns={maxColumns}
      maxColumnsWithChatPanel={maxColumnsWithChatPanel}
    />
  );
}

export default EmailCampaignStatsGrid;
