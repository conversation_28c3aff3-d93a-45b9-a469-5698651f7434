/**
 * Component chọ<PERSON> lo<PERSON><PERSON> hợ<PERSON> (<PERSON><PERSON>h nghiệp/<PERSON><PERSON> nhân)
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon, ResponsiveGrid } from '@/shared/components/common';
import { ContractType, ContractStepProps } from '../types';

const ContractTypeSelector: React.FC<ContractStepProps> = ({ data, onNext }) => {
  const { t } = useTranslation('contract');

  const handleSelectType = (type: ContractType) => {
    // Tự động chuyển sang bước tiếp theo khi chọn loại
    onNext({ type });
  };

  return (
    <div className="w-full">
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }} maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}>
        {/* <PERSON><PERSON><PERSON> nghi<PERSON> */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
            data.type === ContractType.BUSINESS
              ? 'bg-primary/5 shadow-lg border-primary'
              : 'hover:shadow-md hover:border-primary/50'
          }`}
          onClick={() => handleSelectType(ContractType.BUSINESS)}
        >
          {/* Doanh nghiệp */}
          <Card
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
              data.type === ContractType.BUSINESS
                ? 'bg-primary/5 shadow-lg border-primary'
                : 'hover:shadow-md hover:border-primary/50'
            }`}
            onClick={() => handleSelectType(ContractType.BUSINESS)}
          >
            <div className="text-center p-3">
              <div className="mb-2">
                <Icon
                  name="building"
                  size="md"
                  className={`mx-auto ${
                    data.type === ContractType.BUSINESS ? 'text-primary' : 'text-muted'
                  }`}
                />
              </div>
              <Typography variant="h6" className="mb-1">
                {t('contract:types.business')}
              </Typography>
              <Typography variant="body2" className="text-muted text-xs">
                Dành cho các doanh nghiệp, công ty
              </Typography>
            </div>
          </Card>

          {/* Cá nhân */}
          <Card
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 ${
              data.type === ContractType.PERSONAL
                ? 'bg-primary/5 shadow-lg border-primary'
                : 'hover:shadow-md hover:border-primary/50'
            }`}
            onClick={() => handleSelectType(ContractType.PERSONAL)}
          >
            <div className="text-center p-3">
              <div className="mb-2">
                <Icon
                  name="user"
                  size="md"
                  className={`mx-auto ${
                    data.type === ContractType.PERSONAL ? 'text-primary' : 'text-muted'
                  }`}
                />
              </div>
              <Typography variant="h6" className="mb-1">
                {t('contract:types.personal')}
              </Typography>
              <Typography variant="body2" className="text-muted text-xs">
                Dành cho cá nhân
              </Typography>
            </div>
            <Typography variant="h4" className="mb-2">
              {t('contract:types.personal')}
            </Typography>
            <Typography variant="body2" className="text-muted">
              Dành cho cá nhân
            </Typography>
          </Card>
        </Card>
      </ResponsiveGrid>
    </div>
  );
};

export default ContractTypeSelector;
