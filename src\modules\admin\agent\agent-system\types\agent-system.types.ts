/**
 * Enum cho trạng thái agent
 */
export enum AgentStatusEnum {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DEPRECATED = 'DEPRECATED',
}

/**
 * Enum cho các trường sắp xếp của agent system
 */
export enum AgentSystemSortBy {
  ID = 'id',
  NAME = 'name',
  NAME_CODE = 'nameCode',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  /** ID nhân viên */
  employeeId: string;
  /** Tên nhân viên */
  name: string;
  /** Avatar nhân viên */
  avatar: string | null;
  /** Thời gian */
  date?: Date;
}

/**
 * Interface cho cấu hình model AI
 */
export interface ModelConfig {
  /** Temperature cho model (0-2) */
  temperature?: number;
  /** Top P (0-1) */
  top_p?: number;
  /** Top K */
  top_k?: number;
  /** Max tokens */
  max_tokens?: number;
}

/**
 * Interface cho thông tin vector store
 */
export interface VectorStore {
  /** ID của vector store */
  vectorStoreId: string;
  /** Tên của vector store */
  vectorStoreName: string;
}

/**
 * Interface cho thông tin vai trò đơn giản
 */
export interface SimpleRole {
  /** ID của vai trò */
  id: string;
  /** Tên của vai trò */
  name: string;
}

/**
 * Interface cho thông tin vai trò đầy đủ
 */
export interface Role extends SimpleRole {
  /** Mô tả của vai trò */
  description: string | null;
  /** Số lượng quyền trong vai trò */
  permissionCount?: number;
}

/**
 * Interface cho agent system trong danh sách
 */
export interface AgentSystemListItem {
  /** ID của agent */
  id: string;
  /** Tên hiển thị của agent */
  name: string;
  /** Mã định danh của agent system */
  nameCode: string;
  /** URL avatar của agent */
  avatar: string | null;
  /** Tên model sử dụng */
  model: string;
  /** Trạng thái của agent */
  status: AgentStatusEnum;
  /** Model ID từ provider */
  model_id?: string | null;
  /** Loại provider */
  type_provider?: string | null;
  /** Thông tin vai trò (nếu có) */
  roles?: SimpleRole;
}

/**
 * Interface cho thông tin chi tiết agent system
 */
export interface AgentSystemDetail {
  /** ID của agent */
  id: string;
  /** Tên hiển thị của agent */
  name: string;
  /** Mã định danh của agent system */
  nameCode: string;
  /** URL avatar của agent */
  avatar: string | null;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent */
  instruction: string | null;
  /** Thông tin vector store */
  vector?: VectorStore;
  /** Trạng thái của agent */
  status: AgentStatusEnum;
  /** Model ID từ provider */
  model_id?: string | null;
  /** Loại provider */
  type_provider?: string | null;
  /** Thông tin người tạo */
  created?: EmployeeInfo;
  /** Thông tin người cập nhật */
  updated?: EmployeeInfo;
  /** Thông tin người xóa */
  deleted?: EmployeeInfo;
  /** Thông tin vai trò (nếu có) */
  roles?: SimpleRole;
}

/**
 * Interface cho agent system đã xóa trong trash
 */
export interface AgentSystemTrashItem {
  /** ID của agent */
  id: string;
  /** Tên hiển thị của agent */
  name: string;
  /** Mã định danh của agent system */
  nameCode: string;
  /** URL avatar của agent */
  avatar?: string | null;
  /** Tên model sử dụng */
  model: string;
  /** Model ID từ provider */
  model_id?: string | null;
  /** Loại provider */
  type_provider?: string | null;
  /** Trạng thái của agent */
  status: AgentStatusEnum;
  /** Thông tin người xóa */
  deleted?: EmployeeInfo;
  /** Thông tin vai trò (nếu có) */
  roles?: SimpleRole;
}

/**
 * Interface cho tham số tạo agent system
 */
export interface CreateAgentSystemParams {
  /** Tên hiển thị của agent */
  name: string;
  /** Mã định danh của agent system */
  nameCode: string;
  /** MIME type của avatar */
  avatarMimeType?: string;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent */
  instruction?: string | null;
  /** ID của vector store */
  vectorStoreId?: string;
  /** Trạng thái của agent */
  status?: AgentStatusEnum;
  /** ID của vai trò */
  roleId?: string;
  /** ID của base model */
  modelBaseId?: string;
  /** ID của finetuning model */
  modelFinetuningId?: string;
}

/**
 * Interface cho tham số cập nhật agent system
 */
export interface UpdateAgentSystemParams {
  /** Tên hiển thị của agent */
  name?: string;
  /** Mã định danh của agent system */
  nameCode?: string;
  /** MIME type của avatar */
  avatarMimeType?: string;
  /** Cấu hình model AI */
  modelConfig?: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent */
  instruction?: string | null;
  /** ID của vector store */
  vectorStoreId?: string;
  /** Trạng thái của agent */
  status?: AgentStatusEnum;
  /** ID của vai trò */
  roleId?: string;
  /** ID của base model */
  modelBaseId?: string;
  /** ID của finetuning model */
  modelFinetuningId?: string;
}

/**
 * Interface cho tham số cập nhật trạng thái agent system
 */
export interface UpdateAgentSystemStatusParams {
  /** Trạng thái mới của agent */
  status: AgentStatusEnum;
}

/**
 * Interface cho tham số query agent system
 */
export interface AgentSystemQueryParams {
  /** Số trang */
  page?: number;
  /** Số lượng item trên mỗi trang */
  limit?: number;
  /** Từ khóa tìm kiếm */
  search?: string;
  /** Lọc theo trạng thái */
  status?: AgentStatusEnum;
  /** Sắp xếp theo trường */
  sortBy?: AgentSystemSortBy;
  /** Hướng sắp xếp */
  sortDirection?: SortDirection;
}

/**
 * Interface cho response tạo agent system
 */
export interface CreateAgentSystemResponse {
  /** ID của agent system đã tạo */
  id: string;
  /** URL tải lên avatar (nếu có) */
  avatarUrlUpload?: string;
}

/**
 * Interface cho response cập nhật agent system
 */
export interface UpdateAgentSystemResponse {
  /** URL tải lên avatar (nếu có) */
  avatarUrlUpload?: string;
}
