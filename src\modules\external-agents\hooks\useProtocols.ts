import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { protocolService } from '../services';
import { EXTERNAL_AGENT_QUERY_KEYS } from '../constants';
import { ProtocolConfig, ProtocolTemplate } from '../types';

// Get available protocols
export const useProtocols = () => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.PROTOCOLS,
    queryFn: () => protocolService.getProtocols(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Detect protocol mutation
export const useProtocolDetection = () => {
  return useMutation({
    mutationFn: (endpoint: string) => protocolService.detectProtocol(endpoint),
  });
};

// Validate protocol mutation
export const useProtocolValidation = () => {
  return useMutation({
    mutationFn: (config: ProtocolConfig) => protocolService.validateProtocol(config),
  });
};

// Protocol templates
export const useProtocolTemplates = (params?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.PROTOCOL_TEMPLATES,
    queryFn: () => protocolService.getProtocolTemplates(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get single protocol template
export const useProtocolTemplate = (id: string, enabled = true) => {
  return useQuery({
    queryKey: [...EXTERNAL_AGENT_QUERY_KEYS.PROTOCOL_TEMPLATES, id],
    queryFn: () => protocolService.getProtocolTemplate(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Create protocol template mutation
export const useCreateProtocolTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<ProtocolTemplate, 'id' | 'createdAt' | 'updatedAt'>) =>
      protocolService.createProtocolTemplate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.PROTOCOL_TEMPLATES,
      });
    },
  });
};

// Update protocol template mutation
export const useUpdateProtocolTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ProtocolTemplate> }) =>
      protocolService.updateProtocolTemplate(id, data),
    onSuccess: (updatedTemplate) => {
      queryClient.setQueryData(
        [...EXTERNAL_AGENT_QUERY_KEYS.PROTOCOL_TEMPLATES, updatedTemplate.id],
        updatedTemplate
      );
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.PROTOCOL_TEMPLATES,
      });
    },
  });
};

// Delete protocol template mutation
export const useDeleteProtocolTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => protocolService.deleteProtocolTemplate(id),
    onSuccess: (_, deletedId) => {
      queryClient.removeQueries({
        queryKey: [...EXTERNAL_AGENT_QUERY_KEYS.PROTOCOL_TEMPLATES, deletedId],
      });
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.PROTOCOL_TEMPLATES,
      });
    },
  });
};

// Custom hook for protocol detection workflow
export const useProtocolDetectionWorkflow = () => {
  const detectMutation = useProtocolDetection();
  const validateMutation = useProtocolValidation();

  const detectAndValidate = async (endpoint: string) => {
    const detection = await detectMutation.mutateAsync(endpoint);

    if (detection.detectedProtocol) {
      const protocols = await protocolService.getProtocols();
      const protocolConfig = protocols.find(p => p.type === detection.detectedProtocol);

      if (protocolConfig) {
        const validation = await validateMutation.mutateAsync(protocolConfig);
        return { detection, validation, protocolConfig };
      }
    }

    return { detection };
  };

  return {
    detectAndValidate,
    isDetecting: detectMutation.isPending,
    isValidating: validateMutation.isPending,
    isWorking: detectMutation.isPending || validateMutation.isPending,
    error: detectMutation.error || validateMutation.error,
    reset: () => {
      detectMutation.reset();
      validateMutation.reset();
    },
  };
};
