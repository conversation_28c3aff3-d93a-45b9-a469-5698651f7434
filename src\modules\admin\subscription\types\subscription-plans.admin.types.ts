import { QueryDto } from '@/shared/dto/request/query.dto';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Enum cho loại gói dịch vụ
 */
export enum PackageType {
  TIME_ONLY = 'TIME_ONLY',
  HYBRID = 'HYBRID',
}

/**
 * Interface cho thông tin gói dịch vụ
 */
export interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  packageType: PackageType;
}

/**
 * DTO cho query danh sách gói dịch vụ
 */
export interface GetSubscriptionPlansQueryDto extends QueryDto {
  /**
   * Tìm kiếm theo tên gói dịch vụ
   */
  name?: string;
  
  /**
   * Lọc theo loại gói dịch vụ
   */
  packageType?: PackageType;
}

/**
 * DTO cho tạo gói dịch vụ mới
 */
export interface CreateSubscriptionPlanDto {
  name: string;
  description: string;
  packageType: PackageType;
}

/**
 * DTO cho cập nhật gói dịch vụ
 */
export interface UpdateSubscriptionPlanDto {
  name: string;
  description: string;
  packageType: PackageType;
}

/**
 * Response cho danh sách gói dịch vụ
 */
export type SubscriptionPlansListApiResponse = ApiResponseDto<PaginatedResult<SubscriptionPlan>>;

/**
 * Response cho chi tiết gói dịch vụ
 */
export type SubscriptionPlanDetailApiResponse = ApiResponseDto<SubscriptionPlan>;

/**
 * Response cho tạo gói dịch vụ
 */
export type CreateSubscriptionPlanApiResponse = ApiResponseDto<SubscriptionPlan>;

/**
 * Response cho cập nhật gói dịch vụ
 */
export type UpdateSubscriptionPlanApiResponse = ApiResponseDto<SubscriptionPlan>;

/**
 * Response cho xóa gói dịch vụ
 */
export type DeleteSubscriptionPlanApiResponse = ApiResponseDto<null>;
