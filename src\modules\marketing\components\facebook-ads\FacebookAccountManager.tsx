import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  ResponsiveGrid,
  EmptyState,
  SearchBar,
  Select,
  Loading,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';
import FacebookAccountCard from './FacebookAccountCard';
import FacebookAuthButton from './FacebookAuthButton';

interface FacebookAccountManagerProps {
  /**
   * Show search and filters
   */
  showFilters?: boolean;
  
  /**
   * Show add account button
   */
  showAddButton?: boolean;
  
  /**
   * Grid layout columns
   */
  gridColumns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  
  /**
   * Callback khi sync account
   */
  onSyncAccount?: (accountId: string) => void;
  
  /**
   * Callback khi edit account
   */
  onEditAccount?: (accountId: string) => void;
  
  /**
   * Callback khi delete account
   */
  onDeleteAccount?: (accountId: string) => void;
  
  /**
   * Callback khi add account
   */
  onAddAccount?: () => void;
}

/**
 * Facebook Account Manager Component
 * Component tổng hợp để quản lý tài khoản Facebook Ads
 */
const FacebookAccountManager: React.FC<FacebookAccountManagerProps> = ({
  showFilters = true,
  showAddButton = true,
  gridColumns = { xs: 1, sm: 1, md: 2, lg: 2, xl: 3 },
  onSyncAccount,
  onEditAccount,
  onDeleteAccount,
  onAddAccount,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    isAuthenticated,
    adAccounts,
    isLoading: authLoading,
    refetchAdAccounts,
  } = useFacebookAuth();

  // Convert Facebook ad accounts to display format
  const displayAccounts = useMemo(() => {
    if (!isAuthenticated || !adAccounts.length) return [];
    
    return adAccounts.map(account => ({
      id: account.id,
      accountId: account.accountId,
      name: account.name,
      businessName: account.businessName,
      status: account.accountStatus === 1 ? 'active' as const : 'inactive' as const,
      currency: account.currency,
      timezone: account.timezone || 'N/A',
      balance: 0, // TODO: Get from API
      spendCap: 0, // TODO: Get from API
      accountStatus: account.accountStatus,
    }));
  }, [isAuthenticated, adAccounts]);

  // Filter accounts
  const filteredAccounts = useMemo(() => {
    return displayAccounts.filter(account => {
      const matchesSearch = account.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           account.accountId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (account.businessName && account.businessName.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesStatus = statusFilter === 'all' || account.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [displayAccounts, searchQuery, statusFilter]);

  const statusOptions = [
    { value: 'all', label: t('common:filter.all', 'Tất cả') },
    { value: 'active', label: t('common:status.active', 'Hoạt động') },
    { value: 'inactive', label: t('common:status.inactive', 'Không hoạt động') },
    { value: 'error', label: t('common:status.error', 'Lỗi') },
  ];

  const handleSyncAccount = async (accountId: string) => {
    setIsLoading(true);
    try {
      await refetchAdAccounts();
      onSyncAccount?.(accountId);
    } catch (error) {
      console.error('Failed to sync account:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditAccount = (accountId: string) => {
    onEditAccount?.(accountId);
  };

  const handleDeleteAccount = (accountId: string) => {
    onDeleteAccount?.(accountId);
  };

  const handleAddAccount = () => {
    onAddAccount?.();
  };

  const handleRefreshAll = async () => {
    setIsLoading(true);
    try {
      await refetchAdAccounts();
    } catch (error) {
      console.error('Failed to refresh accounts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state
  if (authLoading) {
    return (
      <Card className="p-8 text-center">
        <Loading size="lg" />
        <Typography variant="body1" className="mt-4">
          {t('marketing:facebookAds.accounts.loading', 'Đang tải tài khoản...')}
        </Typography>
      </Card>
    );
  }

  // Show not authenticated state
  if (!isAuthenticated) {
    return (
      <Card className="p-8 text-center">
        <EmptyState
          icon="facebook"
          title={t('marketing:facebookAds.accounts.notConnected.title', 'Chưa kết nối Facebook')}
          description={t('marketing:facebookAds.accounts.notConnected.description', 'Kết nối tài khoản Facebook để quản lý tài khoản quảng cáo')}
          actions={
            <FacebookAuthButton
              variant="primary"
              size="lg"
              onAuthSuccess={() => {
                console.log('Facebook connected successfully');
              }}
            />
          }
        />
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Typography variant="h6">
            {t('marketing:facebookAds.accounts.manager.title', 'Tài khoản Facebook Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('marketing:facebookAds.accounts.manager.description', 'Quản lý các tài khoản quảng cáo Facebook đã kết nối')}
          </Typography>
        </div>
        
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleRefreshAll}
            isLoading={isLoading}
          >
            <Icon name="refresh-cw" className="mr-2" />
            {t('common:button.refresh', 'Làm mới')}
          </Button>
          
          {showAddButton && (
            <Button
              variant="primary"
              onClick={handleAddAccount}
            >
              <Icon name="plus" className="mr-2" />
              {t('marketing:facebookAds.accounts.add', 'Thêm tài khoản')}
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && filteredAccounts.length > 0 && (
        <Card className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder={t('marketing:facebookAds.accounts.search', 'Tìm kiếm tài khoản...')}
              />
            </div>
            <div className="w-full sm:w-48">
              <Select
                value={statusFilter}
                onChange={(value) => setStatusFilter(value as string)}
                options={statusOptions}
                placeholder={t('marketing:facebookAds.accounts.filterStatus', 'Lọc theo trạng thái')}
              />
            </div>
          </div>
        </Card>
      )}

      {/* Accounts Grid */}
      {filteredAccounts.length === 0 ? (
        <Card className="p-8 text-center">
          <EmptyState
            icon="facebook"
            title={
              searchQuery || statusFilter !== 'all'
                ? t('marketing:facebookAds.accounts.noResults.title', 'Không tìm thấy tài khoản')
                : t('marketing:facebookAds.accounts.empty.title', 'Chưa có tài khoản nào')
            }
            description={
              searchQuery || statusFilter !== 'all'
                ? t('marketing:facebookAds.accounts.noResults.description', 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm')
                : t('marketing:facebookAds.accounts.empty.description', 'Tài khoản Facebook của bạn chưa có tài khoản quảng cáo nào')
            }
            actions={
              searchQuery || statusFilter !== 'all' ? (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('all');
                  }}
                >
                  <Icon name="x" className="mr-2" />
                  {t('common:button.clearFilters', 'Xóa bộ lọc')}
                </Button>
              ) : showAddButton ? (
                <Button
                  variant="primary"
                  onClick={handleAddAccount}
                >
                  <Icon name="plus" className="mr-2" />
                  {t('marketing:facebookAds.accounts.addFirst', 'Thêm tài khoản đầu tiên')}
                </Button>
              ) : undefined
            }
          />
        </Card>
      ) : (
        <ResponsiveGrid maxColumns={gridColumns}>
          {filteredAccounts.map((account) => (
            <FacebookAccountCard
              key={account.id}
              account={account}
              showDetails
              onSync={handleSyncAccount}
              onEdit={handleEditAccount}
              onDelete={handleDeleteAccount}
              isLoading={isLoading}
            />
          ))}
        </ResponsiveGrid>
      )}

      {/* Summary */}
      {filteredAccounts.length > 0 && (
        <Card className="p-4">
          <div className="flex justify-between items-center text-sm">
            <Typography variant="body2" className="text-muted-foreground">
              {t('marketing:facebookAds.accounts.summary.total', 'Tổng cộng')}: {filteredAccounts.length} {t('marketing:facebookAds.accounts.summary.accounts', 'tài khoản')}
            </Typography>
            
            <div className="flex space-x-4">
              <Typography variant="body2" className="text-green-600">
                {t('common:status.active', 'Hoạt động')}: {filteredAccounts.filter(a => a.status === 'active').length}
              </Typography>
              <Typography variant="body2" className="text-gray-600">
                {t('common:status.inactive', 'Không hoạt động')}: {filteredAccounts.filter(a => a.status === 'inactive').length}
              </Typography>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default FacebookAccountManager;
