import { apiClient } from '@/shared/api';
import {
  DatabaseConnectionConfig,
  DatabaseConnectionFormData,
  DatabaseConnectionQueryParams,
  DatabaseConnectionResponse,
  DatabaseTestRequest,
  DatabaseTestResult,
} from '../types';
import { DATABASE_INTEGRATION_ENDPOINTS } from '../constants';
import { validateCredentialsByType } from '../schemas';

/**
 * Database Integration API Service
 */
export class DatabaseIntegrationService {
  /**
   * Get list of database connections
   */
  static async getConnections(params?: DatabaseConnectionQueryParams): Promise<DatabaseConnectionResponse> {
    const response = await apiClient.get<DatabaseConnectionResponse>(DATABASE_INTEGRATION_ENDPOINTS.CONNECTIONS, {
      params,
    });
    return response.result;
  }

  /**
   * Get database connection by ID
   */
  static async getConnection(id: string): Promise<DatabaseConnectionConfig> {
    const response = await apiClient.get<DatabaseConnectionConfig>(DATABASE_INTEGRATION_ENDPOINTS.CONNECTION_DETAIL(id));
    return response.result;
  }

  /**
   * Create new database connection
   */
  static async createConnection(data: DatabaseConnectionFormData): Promise<DatabaseConnectionConfig> {
    const response = await apiClient.post<DatabaseConnectionConfig>(DATABASE_INTEGRATION_ENDPOINTS.CONNECTIONS, data);
    return response.result;
  }

  /**
   * Update database connection
   */
  static async updateConnection(id: string, data: Partial<DatabaseConnectionFormData>): Promise<DatabaseConnectionConfig> {
    const response = await apiClient.put<DatabaseConnectionConfig>(DATABASE_INTEGRATION_ENDPOINTS.CONNECTION_DETAIL(id), data);
    return response.result;
  }

  /**
   * Delete database connection
   */
  static async deleteConnection(id: string): Promise<void> {
    await apiClient.delete(DATABASE_INTEGRATION_ENDPOINTS.CONNECTION_DETAIL(id));
  }

  /**
   * Test database connection
   */
  static async testConnection(id: string): Promise<DatabaseTestResult> {
    const response = await apiClient.post<DatabaseTestResult>(DATABASE_INTEGRATION_ENDPOINTS.TEST_CONNECTION(id));
    return response.result;
  }

  /**
   * Update database connection status
   */
  static async updateConnectionStatus(id: string, status: string): Promise<DatabaseConnectionConfig> {
    const response = await apiClient.patch<DatabaseConnectionConfig>(DATABASE_INTEGRATION_ENDPOINTS.CONNECTION_STATUS(id), {
      status,
    });
    return response.result;
  }

  /**
   * Test database connection with custom query
   */
  static async testConnectionWithQuery(data: DatabaseTestRequest): Promise<DatabaseTestResult> {
    const response = await apiClient.post<DatabaseTestResult>(DATABASE_INTEGRATION_ENDPOINTS.TEST_CONNECTION(data.connectionId), {
      testQuery: data.testQuery,
    });
    return response.result;
  }
}

/**
 * Business Logic Service for Database Integration
 */
export class DatabaseIntegrationBusinessService {
  /**
   * Get connections with business logic
   */
  static async getConnectionsWithBusinessLogic(params?: DatabaseConnectionQueryParams): Promise<DatabaseConnectionResponse> {
    // Apply default parameters
    const defaultParams: DatabaseConnectionQueryParams = {
      page: 1,
      limit: 12,
      sortBy: 'createdAt',
      sortOrder: 'desc',
      ...params,
    };

    // Validate parameters
    if (defaultParams.limit && defaultParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    if (defaultParams.page && defaultParams.page < 1) {
      throw new Error('Page must be greater than 0');
    }

    return DatabaseIntegrationService.getConnections(defaultParams);
  }

  /**
   * Create connection with business logic
   */
  static async createConnectionWithBusinessLogic(data: DatabaseConnectionFormData): Promise<DatabaseConnectionConfig> {
    // Validate business rules
    await this.validateConnectionBusinessRules(data);

    // Validate credentials based on database type
    validateCredentialsByType(data.type, data.credentials);

    // If setting as default, ensure only one default exists
    if (data.isDefault) {
      await this.ensureSingleDefaultConnection();
    }

    return DatabaseIntegrationService.createConnection(data);
  }

  /**
   * Update connection with business logic
   */
  static async updateConnectionWithBusinessLogic(
    id: string,
    data: Partial<DatabaseConnectionFormData>
  ): Promise<DatabaseConnectionConfig> {
    // Get current connection
    const currentConnection = await DatabaseIntegrationService.getConnection(id);

    // Validate business rules
    if (data.name || data.type || data.credentials) {
      await this.validateConnectionBusinessRules({
        ...currentConnection,
        ...data,
      } as DatabaseConnectionFormData);
    }

    // Validate credentials if provided
    if (data.credentials && data.type) {
      validateCredentialsByType(data.type, data.credentials);
    }

    // If setting as default, ensure only one default exists
    if (data.isDefault && !currentConnection.isDefault) {
      await this.ensureSingleDefaultConnection();
    }

    return DatabaseIntegrationService.updateConnection(id, data);
  }

  /**
   * Delete connection with business logic
   */
  static async deleteConnectionWithBusinessLogic(id: string): Promise<void> {
    // Get current connection
    const connection = await DatabaseIntegrationService.getConnection(id);

    // Prevent deletion of default connection if it's the only one
    if (connection.isDefault) {
      const allConnections = await DatabaseIntegrationService.getConnections({ limit: 100 });
      if (allConnections.items.length === 1) {
        throw new Error('Cannot delete the only database connection');
      }
    }

    return DatabaseIntegrationService.deleteConnection(id);
  }

  /**
   * Test connection with enhanced error handling
   */
  static async testConnectionWithBusinessLogic(id: string): Promise<DatabaseTestResult> {
    try {
      const result = await DatabaseIntegrationService.testConnection(id);
      
      // Update connection's last tested time and result
      await DatabaseIntegrationService.updateConnection(id, {
        settings: {
          // This would be merged with existing settings
        },
      });

      return result;
    } catch (error: unknown) {
      // Return failed test result
      const errorMessage = error instanceof Error ? error.message : 'Test failed';
      return {
        success: false,
        message: errorMessage,
        responseTime: 0,
        timestamp: new Date().toISOString(),
        details: {
          error: errorMessage,
        },
      };
    }
  }

  /**
   * Validate connection business rules
   */
  private static async validateConnectionBusinessRules(data: DatabaseConnectionFormData): Promise<void> {
    // Check for duplicate names
    const existingConnections = await DatabaseIntegrationService.getConnections({
      search: data.name,
      limit: 100,
    });

    const duplicateName = existingConnections.items.find(
      connection => connection.name.toLowerCase() === data.name.toLowerCase()
    );

    if (duplicateName) {
      throw new Error(`Connection with name "${data.name}" already exists`);
    }
  }

  /**
   * Ensure only one default connection exists
   */
  private static async ensureSingleDefaultConnection(): Promise<void> {
    const allConnections = await DatabaseIntegrationService.getConnections({ limit: 100 });
    const defaultConnections = allConnections.items.filter(c => c.isDefault);

    // Update all current default connections to non-default
    for (const connection of defaultConnections) {
      await DatabaseIntegrationService.updateConnection(connection.id, {
        isDefault: false,
      });
    }
  }
}

// Export both services
export { DatabaseIntegrationService as databaseIntegrationApi };
export { DatabaseIntegrationBusinessService as databaseIntegrationService };
