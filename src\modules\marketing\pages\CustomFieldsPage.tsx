import React, { useMemo, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Table,
  Card,
  ConfirmDeleteModal,
  ActionMenu,
  ActionMenuItem,
} from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import CustomFieldForm from '../components/forms/CustomFieldForm';
import EditCustomFieldForm from '../components/forms/EditCustomFieldForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  CustomField,
  CustomFieldType,
  CustomFieldQueryParams,
  CreateCustomFieldRequest,
} from '../types/custom-field.types';
import { useCustomFields, useCreateCustomField, useDeleteCustomField } from '../hooks';
import { CustomFieldService } from '../services/custom-field.service';
import { useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { AxiosError } from 'axios';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';

/**
 * Trang quản lý Custom Fields theo phong cách MediaPage
 *
 * Features:
 * - CRUD operations cho custom fields
 * - Search và filter với pagination
 * - Slide-in forms cho add/edit
 * - ConfirmDeleteModal cho xóa
 * - Responsive table với sorting
 */
const CustomFieldsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // Mutation hooks
  const createCustomFieldMutation = useCreateCustomField();
  const deleteCustomFieldMutation = useDeleteCustomField();

  // State management
  const [editingFieldId, setEditingFieldId] = useState<number | null>(null);

  // Không sử dụng hook useUpdateCustomField nữa vì chúng ta gọi API trực tiếp
  // Tạo một đối tượng giả để tương thích với useEffect
  const updateCustomFieldMutation = useMemo(
    () => ({
      isSuccess: false,
      reset: () => {},
    }),
    []
  );

  // State quản lý xóa item
  const [itemToDelete, setItemToDelete] = useState<{
    id: number;
    name: string;
  } | null>(null);

  // Form visibility hooks
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleEdit = useCallback(
    (id: number | string) => {
      const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
      setEditingFieldId(numericId);
      showEditForm();
    },
    [showEditForm]
  );

  const handleDelete = useCallback((id: number | string, name: string) => {
    setItemToDelete({
      id: typeof id === 'string' ? parseInt(id, 10) : id,
      name,
    });
  }, []);

  const confirmDelete = useCallback(() => {
    if (itemToDelete) {
      deleteCustomFieldMutation.mutate(itemToDelete.id);
      setItemToDelete(null);
    }
  }, [itemToDelete, deleteCustomFieldMutation]);

  const cancelDelete = useCallback(() => {
    setItemToDelete(null);
  }, []);

  // ============================================================================
  // Table Configuration
  // ============================================================================

  const columns = useMemo<TableColumn<CustomField>[]>(
    () => [
      { key: 'id', title: t('common:id'), dataIndex: 'id', width: '10%', sortable: true },
      {
        key: 'displayName',
        title: t('marketing:customFields.displayName'),
        dataIndex: 'displayName',
        width: '20%',
        sortable: true,
      },
      {
        key: 'fieldKey',
        title: t('marketing:customFields.fieldKey'),
        dataIndex: 'fieldKey',
        width: '15%',
        sortable: true,
      },
      {
        key: 'dataType',
        title: t('marketing:customFields.dataType'),
        dataIndex: 'dataType',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const dataType = value as string;
          let displayText = dataType;

          // Hiển thị tên thân thiện cho kiểu dữ liệu
          switch (dataType) {
            case CustomFieldType.TEXT:
              displayText = t('marketing:customFields.types.text');
              break;
            case CustomFieldType.NUMBER:
              displayText = t('marketing:customFields.types.number');
              break;
            case CustomFieldType.DATE:
              displayText = t('marketing:customFields.types.date');
              break;
            case CustomFieldType.BOOLEAN:
              displayText = t('marketing:customFields.types.boolean');
              break;
          }

          return <span>{displayText}</span>;
        },
      },
      {
        key: 'description',
        title: t('marketing:customFields.description'),
        dataIndex: 'description',
        width: '20%',
        sortable: true,
      },

      {
        key: 'actions',
        title: t('marketing:customFields.action'),
        width: '10%',
        render: (_: unknown, record: CustomField) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record.id),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => handleDelete(record.id, record.displayName),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleEdit, handleDelete]
  );

  // ============================================================================
  // Data Table Setup
  // ============================================================================

  const filterOptions = useMemo(
    () => [{ id: 'all', label: t('common:all'), icon: 'list', value: 'all' }],
    [t]
  );

  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): CustomFieldQueryParams => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };
  };

  const dataTable = useDataTable(
    useDataTableConfig<CustomField, CustomFieldQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // ============================================================================
  // Data Fetching
  // ============================================================================

  const { data: customFieldData, isLoading } = useCustomFields(dataTable.queryParams);

  const tableData = customFieldData?.items || [];
  const meta = customFieldData?.meta || { page: 1, limit: 10, total: 0 };

  // Theo dõi kết quả mutation để refetch dữ liệu
  useEffect(() => {
    if (
      createCustomFieldMutation.isSuccess ||
      updateCustomFieldMutation.isSuccess ||
      deleteCustomFieldMutation.isSuccess
    ) {
      // Reset các trạng thái mutation để tránh refetch liên tục
      if (createCustomFieldMutation.isSuccess) createCustomFieldMutation.reset();
      if (updateCustomFieldMutation.isSuccess) updateCustomFieldMutation.reset();
      if (deleteCustomFieldMutation.isSuccess) deleteCustomFieldMutation.reset();
    }
  }, [createCustomFieldMutation, updateCustomFieldMutation, deleteCustomFieldMutation]);

  // Chỉ log khi cần debug
  // useEffect(() => {
  //   console.log('API Response - customFieldData:', customFieldData);
  // }, [customFieldData]);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,

    t,
  });

  // ============================================================================
  // Form Handlers
  // ============================================================================

  const handleAdd = useCallback(() => {
    showAddForm();
  }, [showAddForm]);

  const handleSubmit = useCallback((values: Record<string, unknown>) => {
    const customFieldData = {
      fieldKey: values.fieldKey as string,
      displayName: values.displayName as string,
      dataType: values.dataType as CustomFieldType,
      description: values.description as string | undefined,
    };

    createCustomFieldMutation.mutate(customFieldData as CreateCustomFieldRequest);
    hideAddForm();
  }, [createCustomFieldMutation, hideAddForm]);

  const queryClient = useQueryClient();

  const handleEditSubmit = useCallback((id: number, values: Record<string, unknown>) => {
    if (!id || id <= 0) {
      console.error('Invalid ID for update:', id);
      return;
    }

    const updateData = {
      displayName: values.displayName as string,
      dataType: values.dataType as CustomFieldType,
      description: values.description as string | undefined,
    };

    CustomFieldService.updateCustomField(id, updateData)
      .then(data => {
        NotificationUtil.success({
          message: data.message || 'Cập nhật trường tùy chỉnh thành công',
        });

        queryClient.invalidateQueries({
          queryKey: ['marketing', 'custom-fields'],
          exact: false,
          refetchType: 'active',
        });
      })
      .catch((error: AxiosError<{ message: string }>) => {
        NotificationUtil.error({
          message: error.response?.data?.message || 'Cập nhật trường tùy chỉnh thất bại',
        });
        console.error('Error updating custom field:', error);
      });

    hideEditForm();
    setEditingFieldId(null);
  }, [queryClient, hideEditForm]);

  const handleCancel = useCallback(() => {
    hideAddForm();
  }, [hideAddForm]);

  const handleEditCancel = useCallback(() => {
    hideEditForm();
    setEditingFieldId(null);
  }, [hideEditForm]);

  return (
    <div className="w-full bg-background text-foreground space-y-4">
      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={!!itemToDelete}
        onClose={cancelDelete}
        onConfirm={confirmDelete}
        title={t('common:confirmDelete')}
        message={t('marketing:customFields.deleteConfirmation', {
          name: itemToDelete?.name,
        })}
        itemName={itemToDelete?.name || ''}
        isSubmitting={deleteCustomFieldMutation.isPending}
      />

      {/* Menu và Controls */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Active Filters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Forms */}
      <SlideInForm isVisible={isAddFormVisible}>
        <CustomFieldForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <SlideInForm isVisible={isEditFormVisible}>
        {editingFieldId && (
          <EditCustomFieldForm
            id={editingFieldId}
            onSubmit={handleEditSubmit}
            onCancel={handleEditCancel}
          />
        )}
      </SlideInForm>

      {/* Main Table */}
      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={tableData}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: meta.page,
            pageSize: meta.limit,
            total: meta.total,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default CustomFieldsPage;
