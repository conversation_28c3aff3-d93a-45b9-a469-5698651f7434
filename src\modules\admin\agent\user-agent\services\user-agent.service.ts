import { apiClient } from '@/shared/api/axios';
import {
  UserAgentDetail,
  CreateUserAgentParams,
  UpdateUserAgentParams,
  UserAgentQueryParams,
} from '../types/user-agent.types';

/**
 * Service để tương tác với API user agent của admin
 */
export class AdminUserAgentService {
  private baseUrl = '/admin/user-agents';

  async getUserAgents(params: UserAgentQueryParams) {
    try {
      const response = await apiClient.get(this.baseUrl, { params, tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error('Error fetching user agents:', error);
      throw error;
    }
  }

  async getUserAgentById(id: string): Promise<UserAgentDetail> {
    try {
      const response = await apiClient.get<UserAgentDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching user agent ${id}:`, error);
      throw error;
    }
  }

  async createUserAgent(
    data: CreateUserAgentParams
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      const response = await apiClient.post<{ id: string; avatarUrlUpload?: string }>(
        this.baseUrl,
        data,
        { tokenType: 'admin' }
      );
      return response.result;
    } catch (error) {
      console.error('Error creating user agent:', error);
      throw error;
    }
  }

  async updateUserAgent(
    id: string,
    data: UpdateUserAgentParams
  ): Promise<{ avatarUrlUpload?: string }> {
    try {
      const response = await apiClient.patch<{ avatarUrlUpload?: string }>(
        `${this.baseUrl}/${id}`,
        data,
        { tokenType: 'admin' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error updating user agent ${id}:`, error);
      throw error;
    }
  }

  async deleteUserAgent(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting user agent ${id}:`, error);
      throw error;
    }
  }
}

export const adminUserAgentService = new AdminUserAgentService();
