import { apiClient } from '@/shared/api';
import { EXTERNAL_AGENT_ENDPOINTS } from '../constants';
import {
  AgentMessage,
  MessageHistory,
  MessageQueryDto,
  MessageStats,
} from '../types';

export const messageApi = {
  // Get messages for all agents
  getMessages: async (params?: MessageQueryDto): Promise<MessageHistory> => {
    const response = await apiClient.get<MessageHistory>(
      EXTERNAL_AGENT_ENDPOINTS.MESSAGES,
      { params }
    );
    return response.result;
  },

  // Get message history for specific agent
  getMessageHistory: async (agentId: string, params?: MessageQueryDto): Promise<MessageHistory> => {
    const response = await apiClient.get<MessageHistory>(
      EXTERNAL_AGENT_ENDPOINTS.MESSAGE_HISTORY(agentId),
      { params }
    );
    return response.result;
  },

  // Get message statistics for agent
  getMessageStats: async (agentId: string, period?: { start: string; end: string }): Promise<MessageStats> => {
    const response = await apiClient.get<MessageStats>(
      EXTERNAL_AGENT_ENDPOINTS.MESSAGE_STATS(agentId),
      { params: period }
    );
    return response.result;
  },

  // Send message to agent
  sendMessage: async (agentId: string, content: Record<string, unknown>): Promise<AgentMessage> => {
    const response = await apiClient.post<AgentMessage>(
      EXTERNAL_AGENT_ENDPOINTS.MESSAGE_HISTORY(agentId),
      { content }
    );
    return response.result;
  },

  // Delete message
  deleteMessage: async (messageId: string): Promise<void> => {
    await apiClient.delete(`${EXTERNAL_AGENT_ENDPOINTS.MESSAGES}/${messageId}`);
  },

  // Bulk delete messages
  bulkDeleteMessages: async (messageIds: string[]): Promise<void> => {
    await apiClient.delete(`${EXTERNAL_AGENT_ENDPOINTS.MESSAGES}/bulk`, {
      data: { ids: messageIds },
    });
  },
};
