# External Agents Module

<PERSON><PERSON> lý và tích hợp với các external agents thông qua nhiều giao thức khác nhau như MCP (Model Context Protocol), Google Agent Communication, REST API, WebSocket, gRPC và các giao thức tùy chỉnh.

## 🚀 Tính năng chính

### 1. Quản lý External Agents
- ✅ Tạ<PERSON>, sửa, xóa agents
- ✅ Bulk operations (cập nhật trạng thái, xóa hàng loạt)
- ✅ Real-time status monitoring
- ✅ Search và filter agents

### 2. Protocol Support
- ✅ **MCP (Model Context Protocol)**: Gia<PERSON> thức chuẩn cho AI agents
- ✅ **Google Agent Communication**: Tích hợp với Google agents
- ✅ **REST API**: HTTP/HTTPS endpoints
- ✅ **WebSocket**: Real-time communication
- ✅ **gRPC**: High-performance RPC
- ✅ **Custom Protocol**: Giao thức tùy chỉnh
- ✅ Auto-detection protocols từ endpoint

### 3. Authentication Support
- ✅ **API Key**: X-API-Key header
- ✅ **Bearer Token**: Authorization header
- ✅ **OAuth 2.0**: Client credentials flow
- ✅ **Basic Auth**: Username/password
- ✅ **Custom Auth**: Cấu hình tùy chỉnh
- ✅ **No Auth**: Không yêu cầu xác thực

### 4. Connection Testing
- ✅ Real-time connection tests
- ✅ Performance monitoring (response time, uptime)
- ✅ Test history và logging
- ✅ Timeout configuration

### 5. Real-time Features
- ✅ WebSocket integration với auto-reconnect
- ✅ Live status updates
- ✅ Real-time message notifications
- ✅ Performance metrics updates

### 6. Integration Dashboard
- ✅ Overview statistics
- ✅ Protocol distribution
- ✅ Quick actions
- ✅ Getting started guide

## 📁 Cấu trúc Module

```
src/modules/external-agents/
├── types/                    # TypeScript interfaces
│   ├── externalAgent.ts     # Agent types
│   ├── protocol.ts          # Protocol types
│   ├── message.ts           # Message types
│   ├── api.ts               # API response types
│   └── enums.ts             # Enums
├── constants/               # Constants và query keys
│   ├── queryKeys.ts         # TanStack Query keys
│   ├── endpoints.ts         # API endpoints
│   └── defaults.ts          # Default values
├── api/                     # Raw API calls
│   ├── externalAgentApi.ts  # Agent CRUD operations
│   ├── protocolApi.ts       # Protocol operations
│   ├── messageApi.ts        # Message operations
│   └── webhookApi.ts        # Webhook operations
├── services/               # Business logic layer
│   ├── externalAgentService.ts
│   ├── protocolService.ts
│   ├── messageService.ts
│   ├── webhookService.ts
│   └── websocketService.ts
├── hooks/                  # TanStack Query hooks
│   ├── useExternalAgents.ts
│   ├── useExternalAgent.ts
│   ├── useConnectionTest.ts
│   ├── useProtocols.ts
│   ├── useMessages.ts
│   ├── useWebhooks.ts
│   ├── useRealTimeStatus.ts
│   └── useWebSocket.ts
├── utils/                  # Helper functions
│   ├── protocolHelpers.ts   # Protocol utilities
│   ├── validationSchemas.ts # Zod validation
│   └── formatters.ts        # Data formatters
├── components/
│   ├── cards/              # Card components
│   │   ├── ExternalAgentCard.tsx
│   │   └── AgentStatusCard.tsx
│   ├── forms/              # Form components
│   │   ├── ExternalAgentForm.tsx
│   │   ├── AuthenticationForm.tsx
│   │   └── ProtocolConfigForm.tsx
│   ├── indicators/         # Status indicators
│   │   ├── StatusIndicator.tsx
│   │   ├── ProtocolBadge.tsx
│   │   └── CapabilityMatrix.tsx
│   ├── common/             # Common components
│   │   ├── ProtocolSelector.tsx
│   │   ├── ConnectionTester.tsx
│   │   ├── PerformanceChart.tsx
│   │   └── MessageLogTable.tsx
│   └── modals/             # Modal components
│       ├── ProtocolDetectionModal.tsx
│       ├── ConnectionTestModal.tsx
│       └── BulkOperationsModal.tsx
├── pages/                  # Page components
│   ├── ExternalAgentsPage.tsx
│   ├── AgentDetailPage.tsx
│   ├── AgentTestingPage.tsx
│   ├── AgentAnalyticsPage.tsx
│   └── MessageHistoryPage.tsx
├── routers/               # Route configuration
│   └── externalAgentRoutes.tsx
└── locales/              # i18n translations
    ├── vi.json
    ├── en.json
    └── zh.json
```

## 🔧 Sử dụng

### 1. Import Module
```typescript
import { 
  useExternalAgents, 
  useCreateExternalAgent,
  ExternalAgentCard,
  ProtocolSelector 
} from '@/modules/external-agents';
```

### 2. Sử dụng Hooks
```typescript
// Get danh sách agents
const { data: agents, isLoading } = useExternalAgents({
  status: 'active',
  protocol: 'rest_api'
});

// Tạo agent mới
const createMutation = useCreateExternalAgent();
const handleCreate = (data) => {
  createMutation.mutate(data);
};

// Test connection
const { testConnection, isTestingConnection } = useConnectionTest();
const handleTest = (agentId) => {
  testConnection(agentId);
};
```

### 3. Sử dụng Components
```typescript
// Agent card
<ExternalAgentCard
  agent={agent}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onTest={handleTest}
  onView={handleView}
/>

// Protocol selector
<ProtocolSelector
  value={selectedProtocol}
  onSelect={handleProtocolSelect}
  showAutoDetect={true}
/>

// Connection tester
<ConnectionTester
  agentId={agentId}
  onTestComplete={handleTestComplete}
  showHistory={true}
/>
```

## 🌐 Internationalization

Module hỗ trợ đa ngôn ngữ với namespace `external-agents`:

```typescript
const { t } = useTranslation(['external-agents']);

// Sử dụng
t('external-agents:agent.name')
t('external-agents:status.active')
t('external-agents:protocol.mcp')
```

## 🔗 API Integration

Module sử dụng 3-layer pattern:

1. **API Layer**: Raw API calls
2. **Services Layer**: Business logic và validation
3. **Hooks Layer**: TanStack Query integration

### API Endpoints
```
GET    /api/external-agents              # Danh sách agents
POST   /api/external-agents              # Tạo agent
GET    /api/external-agents/:id          # Chi tiết agent
PUT    /api/external-agents/:id          # Cập nhật agent
DELETE /api/external-agents/:id          # Xóa agent
POST   /api/external-agents/:id/test     # Test connection
GET    /api/external-agents/:id/performance # Performance metrics
```

## 🚀 Real-time Features

WebSocket connection cho real-time updates:

```typescript
// Subscribe to status updates
const { isConnected, lastUpdate } = useRealTimeStatus(agentId);

// Subscribe to messages
const { messageCount } = useRealTimeMessages(agentId);

// Send commands
const { sendCommand } = useRealTimeUpdates(agentId);
sendCommand('restart', { timeout: 30000 });
```

## 🧪 Testing

Module bao gồm comprehensive testing utilities:

- Connection testing với timeout
- Performance monitoring
- Protocol validation
- Bulk operations testing

## 📝 Notes

- Tất cả components tuân thủ design system của RedAI
- Sử dụng TypeScript strict mode
- Validation với Zod schemas
- Responsive design với ResponsiveGrid
- Error handling và loading states
- Accessibility support
