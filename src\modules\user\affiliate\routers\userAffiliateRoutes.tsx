import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';

const AffiliateOverviewPage = lazy(() => import('../pages/AffiliateOverviewPage'));

/**
 * Routes cho module user affiliate
 */
export const userAffiliateRoutes: RouteObject[] = [
  {
    path: '/user/affiliate',
    element: (
      <MainLayout title="Affiliate Overview">
        <Suspense fallback={<Loading />}>
          <AffiliateOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/user/affiliate/overview',
    element: (
      <MainLayout title="Affiliate Overview">
        <Suspense fallback={<Loading />}>
          <AffiliateOverviewPage />
        </Suspense>
      </MainLayout>
    ),
  },
];