import { z } from 'zod';
import { BillingCycle, UsageUnit } from '../types/plan-pricing.admin.types';

/**
 * Schema validation cho tạo Plan Pricing mới
 */
export const CreatePlanPricingSchema = z.object({
  planId: z
    .number({
      required_error: 'ID gói dịch vụ là bắt buộc',
      invalid_type_error: 'ID gói dịch vụ phải là số',
    })
    .min(1, 'ID gói dịch vụ phải lớn hơn 0'),

  billingCycle: z
    .nativeEnum(BillingCycle, {
      required_error: '<PERSON> kỳ thanh toán là bắt buộc',
      invalid_type_error: 'Chu kỳ thanh toán không hợp lệ',
    }),

  price: z
    .number({
      required_error: 'Gi<PERSON> là bắt buộc',
      invalid_type_error: '<PERSON>i<PERSON> phải là số',
    })
    .min(0, '<PERSON><PERSON><PERSON> phải lớn hơn hoặc bằng 0'),

  usageLimit: z
    .number({
      required_error: 'Giới hạn sử dụng là bắt buộc',
      invalid_type_error: 'Giới hạn sử dụng phải là số',
    })
    .min(0, 'Giới hạn sử dụng phải lớn hơn hoặc bằng 0'),

  usageUnit: z
    .nativeEnum(UsageUnit, {
      required_error: 'Đơn vị sử dụng là bắt buộc',
      invalid_type_error: 'Đơn vị sử dụng không hợp lệ',
    }),

  isActive: z
    .boolean({
      required_error: 'Trạng thái kích hoạt là bắt buộc',
      invalid_type_error: 'Trạng thái kích hoạt phải là boolean',
    }),
});

/**
 * Schema validation cho cập nhật Plan Pricing
 */
export const UpdatePlanPricingSchema = z.object({
  planId: z
    .number({
      invalid_type_error: 'ID gói dịch vụ phải là số',
    })
    .min(1, 'ID gói dịch vụ phải lớn hơn 0')
    .optional(),

  billingCycle: z
    .nativeEnum(BillingCycle, {
      invalid_type_error: 'Chu kỳ thanh toán không hợp lệ',
    })
    .optional(),

  price: z
    .number({
      invalid_type_error: 'Giá phải là số',
    })
    .min(0, 'Giá phải lớn hơn hoặc bằng 0')
    .optional(),

  usageLimit: z
    .number({
      invalid_type_error: 'Giới hạn sử dụng phải là số',
    })
    .min(0, 'Giới hạn sử dụng phải lớn hơn hoặc bằng 0')
    .optional(),

  usageUnit: z
    .nativeEnum(UsageUnit, {
      invalid_type_error: 'Đơn vị sử dụng không hợp lệ',
    })
    .optional(),

  isActive: z
    .boolean({
      invalid_type_error: 'Trạng thái kích hoạt phải là boolean',
    })
    .optional(),
});

/**
 * Schema validation cho query parameters
 */
export const GetPlanPricingsQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  planId: z.number().min(1).optional(),
  billingCycle: z.nativeEnum(BillingCycle).optional(),
  isActive: z.boolean().optional(),
});
