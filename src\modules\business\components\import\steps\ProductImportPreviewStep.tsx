import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Table,
  Icon,
  Checkbox,
} from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { TableColumn } from '@/shared/components/common/Table/types';
import {
  ProductExcelData,
  ProductColumnMapping,
  ProductImportData,
  ProductValidationResult,
  ProductImportOptions,
} from '../../../types/product-import.types';

interface ProductImportPreviewStepProps {
  excelData: ProductExcelData;
  mappings: ProductColumnMapping[];
  onStartImport: (options: ProductImportOptions) => void;
  onGoBack: () => void;
}

/**
 * Component cho bước preview dữ liệu sản phẩm trước khi import
 */
const ProductImportPreviewStep: React.FC<ProductImportPreviewStepProps> = ({
  excelData,
  mappings,
  onStartImport,
  onGoBack,
}) => {
  const { t } = useTranslation(['common', 'business']);

  // State cho import options
  const [skipInvalidRows, setSkipInvalidRows] = useState(true);
  const [updateExisting, setUpdateExisting] = useState(false);
  const [sendNotification, setSendNotification] = useState(false);

  // Transform data theo mappings
  const transformedData = useMemo(() => {
    const activeMappings = mappings.filter(m => m.productField);

    return excelData.rows.map((row, index) => {
      const productData: ProductImportData = {
        name: '',
        typePrice: 'HAS_PRICE',
        price: null,
      };

      let listPrice = 0;
      let salePrice = 0;
      const tags: string[] = [];

      activeMappings.forEach(mapping => {
        const columnIndex = excelData.headers.indexOf(mapping.excelColumn);
        const value = row[columnIndex];

        switch (mapping.productField) {
          case 'name':
            productData.name = value ? String(value).trim() : '';
            break;
          case 'listPrice':
            listPrice = value ? Number(value) || 0 : 0;
            break;
          case 'salePrice':
            salePrice = value ? Number(value) || 0 : 0;
            break;
          case 'description':
            productData.description = value ? String(value).trim() : undefined;
            break;
          case 'tags': {
            const tagValue = value ? String(value).trim() : '';
            if (tagValue) {
              tags.push(...tagValue.split(',').map(tag => tag.trim()).filter(tag => tag));
            }
            break;
          }
          case 'widthCm':
          case 'heightCm':
          case 'lengthCm':
          case 'weightGram': {
            if (!productData.shipmentConfig) {
              productData.shipmentConfig = {};
            }
            const numValue = value ? Number(value) || undefined : undefined;
            if (numValue !== undefined) {
              productData.shipmentConfig[mapping.productField] = numValue;
            }
            break;
          }
          default:
            // Custom fields - check if it's a custom field
            if (mapping.productField.startsWith('custom_')) {
              if (!productData.customFields) {
                productData.customFields = [];
              }
              // Extract custom field ID from mapping (assuming format: custom_123)
              const customFieldId = parseInt(mapping.productField.replace('custom_', ''));
              if (!isNaN(customFieldId)) {
                productData.customFields.push({
                  customFieldId,
                  value: {
                    value: value ? String(value) : '',
                  },
                });
              }
            }
            break;
        }
      });

      // Set price based on listPrice and salePrice
      if (listPrice > 0 || salePrice > 0) {
        productData.price = {
          listPrice: listPrice || salePrice,
          salePrice: salePrice || listPrice,
          currency: 'VND',
        };
        productData.typePrice = 'HAS_PRICE';
      } else {
        productData.price = null;
        productData.typePrice = 'NO_PRICE';
      }

      // Set tags if any
      if (tags.length > 0) {
        productData.tags = tags;
      }

      return { ...productData, rowIndex: index + 2 }; // +2 vì bắt đầu từ dòng 2 (sau header)
    });
  }, [excelData, mappings]);

  // Validation data
  const validationResult = useMemo((): ProductValidationResult => {
    const validData: ProductImportData[] = [];
    const errors: Array<{ row: number; field: string; message: string; value: unknown }> = [];

    transformedData.forEach((data, index) => {
      const rowErrors: Array<{ field: string; message: string; value: unknown }> = [];

      // Validate required fields
      if (!data.name || data.name.trim() === '') {
        rowErrors.push({
          field: 'name',
          message: t('business:product.import.validation.nameRequired'),
          value: data.name
        });
      }

      // Validate price
      if (data.typePrice === 'HAS_PRICE') {
        if (!data.price ||
            (typeof data.price === 'object' && 'listPrice' in data.price && data.price.listPrice <= 0)) {
          rowErrors.push({
            field: 'price',
            message: t('business:product.import.validation.priceRequired'),
            value: data.price
          });
        }
      }

      // Validate shipment config if present
      if (data.shipmentConfig) {
        const { widthCm, heightCm, lengthCm, weightGram } = data.shipmentConfig;

        if (widthCm !== undefined && (isNaN(widthCm) || widthCm <= 0)) {
          rowErrors.push({
            field: 'widthCm',
            message: t('business:product.import.validation.invalidDimensions'),
            value: widthCm
          });
        }

        if (heightCm !== undefined && (isNaN(heightCm) || heightCm <= 0)) {
          rowErrors.push({
            field: 'heightCm',
            message: t('business:product.import.validation.invalidDimensions'),
            value: heightCm
          });
        }

        if (lengthCm !== undefined && (isNaN(lengthCm) || lengthCm <= 0)) {
          rowErrors.push({
            field: 'lengthCm',
            message: t('business:product.import.validation.invalidDimensions'),
            value: lengthCm
          });
        }

        if (weightGram !== undefined && (isNaN(weightGram) || weightGram <= 0)) {
          rowErrors.push({
            field: 'weightGram',
            message: t('business:product.import.validation.invalidWeight'),
            value: weightGram
          });
        }
      }

      if (rowErrors.length > 0) {
        rowErrors.forEach(error => {
          errors.push({
            row: index + 2,
            field: error.field,
            message: error.message,
            value: error.value
          });
        });
      } else {
        validData.push(data);
      }
    });

    return {
      totalRows: transformedData.length,
      validRows: validData.length,
      invalidRows: transformedData.length - validData.length,
      errors,
      validData,
    };
  }, [transformedData, t]);

  // Columns cho preview table
  const previewColumns: TableColumn<ProductImportData & { rowIndex: number }>[] = [
    {
      key: 'rowIndex',
      title: t('business:product.import.preview.row'),
      dataIndex: 'rowIndex',
      width: 80,
    },
    {
      key: 'name',
      title: t('business:product.form.name'),
      dataIndex: 'name',
      render: (value: unknown) => value ? String(value) : <span className="text-muted">-</span>,
    },
    {
      key: 'price',
      title: t('business:product.form.price'),
      dataIndex: 'price',
      render: (value: unknown) => {
        if (!value) return <span className="text-muted">-</span>;
        if (typeof value === 'object' && value !== null && 'listPrice' in value) {
          const priceObj = value as { listPrice: number; salePrice: number; currency: string };
          return `${priceObj.listPrice.toLocaleString()} VND`;
        }
        return <span className="text-muted">-</span>;
      },
    },
    {
      key: 'description',
      title: t('business:product.form.description'),
      dataIndex: 'description',
      render: (value: unknown) => {
        if (!value) return <span className="text-muted">-</span>;
        const str = String(value);
        return str.length > 50 ? `${str.substring(0, 50)}...` : str;
      },
    },
    {
      key: 'tags',
      title: 'Tags',
      dataIndex: 'tags',
      render: (value: unknown) => {
        if (!value || !Array.isArray(value)) return <span className="text-muted">-</span>;
        return value.join(', ');
      },
    },
  ];

  const handleStartImport = () => {
    const options: ProductImportOptions = {
      skipInvalidRows,
      updateExisting,
      sendNotification,
    };
    onStartImport(options);
  };

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="w-full text-center">
        <Typography variant="h5" className="mb-2">
          {t('business:product.import.preview.title')}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('business:product.import.preview.description')}
        </Typography>
      </div>

      {/* Summary */}
      <ListOverviewCard
        items={[
          {
            title: t('business:product.import.preview.totalRows'),
            value: validationResult.totalRows,
            color: 'blue',
            description: 'Tổng số dòng'
          },
          {
            title: t('business:product.import.preview.validRows'),
            value: validationResult.validRows,
            color: 'green',
            description: 'Dòng hợp lệ'
          },
          {
            title: t('business:product.import.preview.invalidRows'),
            value: validationResult.invalidRows,
            color: 'red',
            description: 'Dòng lỗi'
          }
        ]}
        maxColumns={{ xs: 1, sm: 3, md: 3, lg: 3, xl: 3 }}
        gap={4}
      />

      {/* Validation Errors */}
      {validationResult.errors.length > 0 && (
        <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Icon name="alert-triangle" size="sm" className="text-orange-500 mt-0.5" />
            <div className="flex-1">
              <Typography variant="body2" className="font-medium text-orange-700 mb-2">
                {t('business:product.import.preview.validationWarnings')}
              </Typography>
              <div className="max-h-32 overflow-y-auto">
                <ul className="space-y-1">
                  {validationResult.errors.slice(0, 10).map((error, index) => (
                    <li key={index} className="text-sm text-orange-600">
                      • Dòng {error.row}: {error.message}
                    </li>
                  ))}
                  {validationResult.errors.length > 10 && (
                    <li className="text-sm text-orange-600">
                      ... và {validationResult.errors.length - 10} lỗi khác
                    </li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Table */}
      <div className="p-6 bg-card rounded-lg">
        <Typography variant="h6" className="mb-4">
          {t('business:product.import.preview.dataPreview')}
        </Typography>
        <Table
          columns={previewColumns}
          data={validationResult.validData.slice(0, 10).map((item, index) => ({ ...item, rowIndex: index + 2 }))}
          rowKey="rowIndex"
          pagination={false}
          size="sm"
        />
        {validationResult.validData.length > 10 && (
          <Typography variant="body2" className="text-muted text-center mt-2">
            {t('business:product.import.preview.showingFirst10')}
          </Typography>
        )}
      </div>

      {/* Import Options */}
      <div className="p-4 bg-card rounded-lg">
        <Typography variant="body2" className="font-medium mb-4">
          {t('business:product.import.preview.importOptions')}
        </Typography>
        <div className="space-y-4">
          <div>
            <Checkbox
              label={t('business:product.import.preview.skipInvalidRows')}
              checked={skipInvalidRows}
              onChange={setSkipInvalidRows}
            />
          </div>
          <div>
            <Checkbox
              label={t('business:product.import.preview.updateExisting')}
              checked={updateExisting}
              onChange={setUpdateExisting}
            />
          </div>
          <div>
            <Checkbox
              label={t('business:product.import.preview.sendNotification')}
              checked={sendNotification}
              onChange={setSendNotification}
            />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onGoBack}>
          {t('common:back')}
        </Button>

        <Button
          onClick={handleStartImport}
          disabled={validationResult.validRows === 0}
          variant="primary"
        >
          <Icon name="upload" size="sm" className="mr-2" />
          {t('business:product.import.preview.startImport')} ({validationResult.validRows})
        </Button>
      </div>
    </div>
  );
};

export default ProductImportPreviewStep;
