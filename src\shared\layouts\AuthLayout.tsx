import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { ThemeToggle } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import LanguageSwitcher from '../../modules/auth/components/LanguageSwitcher';

interface AuthLayoutProps {
  children: ReactNode;
  title: string;
}

/**
 * Layout component for authentication pages
 */
const AuthLayout: React.FC<AuthLayoutProps> = ({ children, title }) => {
  const { t } = useTranslation();
  const { toggleTheme, currentTheme } = useTheme();

  return (
    <>
      <Helmet>
        <title>{title} | RedAI</title>
      </Helmet>
      <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-100 to-slate-200 dark:from-dark dark:to-dark-light">
        {/* Auth content centered in page */}
        <div className="flex-grow flex items-center justify-center p-4">{children}</div>

        {/* Footer with language and theme toggles */}
        <div className="py-4 px-6 flex justify-center">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {t('common.language')}:
              </span>
              <LanguageSwitcher variant="dropdown" />
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">{t('common.theme')}:</span>
              <ThemeToggle theme={currentTheme.mode} onToggle={toggleTheme} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AuthLayout;
