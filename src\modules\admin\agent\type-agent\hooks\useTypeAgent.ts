import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminTypeAgentService } from '../services/type-agent.service';
import {
  TypeAgentDetail,
  CreateTypeAgentParams,
  UpdateTypeAgentParams,
  TypeAgentQueryParams,
} from '../types/type-agent.types';

// Query keys
export const ADMIN_TYPE_AGENT_QUERY_KEYS = {
  all: ['admin', 'type-agent'] as const,
  lists: () => [...ADMIN_TYPE_AGENT_QUERY_KEYS.all, 'list'] as const,
  list: (params: TypeAgentQueryParams) => [...ADMIN_TYPE_AGENT_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_TYPE_AGENT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_TYPE_AGENT_QUERY_KEYS.details(), id] as const,
};

export const useAdminTypeAgents = (params: TypeAgentQueryParams) => {
  return useQuery({
    queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.list(params),
    queryFn: () => adminTypeAgentService.getTypeAgents(params),
    staleTime: 5 * 60 * 1000,
  });
};

export const useAdminTypeAgentDetail = (id: string) => {
  return useQuery<TypeAgentDetail>({
    queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.detail(id),
    queryFn: () => adminTypeAgentService.getTypeAgentById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateAdminTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTypeAgentParams) => adminTypeAgentService.createTypeAgent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};

export const useUpdateAdminTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTypeAgentParams }) =>
      adminTypeAgentService.updateTypeAgent(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};

export const useDeleteAdminTypeAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminTypeAgentService.deleteTypeAgent(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_TYPE_AGENT_QUERY_KEYS.lists(),
      });
    },
  });
};
