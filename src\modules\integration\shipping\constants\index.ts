import type { ShippingProviderType } from '../types';

/**
 * Shipping Provider Constants
 */

export const SHIPPING_PROVIDER_TYPES: Record<ShippingProviderType, {
  id: ShippingProviderType;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  website: string;
  supportedServices: string[];
  requiredFields: string[];
  optionalFields: string[];
}> = {
  ghn: {
    id: 'ghn',
    name: '<PERSON><PERSON><PERSON>',
    displayName: 'GHN - Giao <PERSON>',
    description: 'Dịch vụ giao hàng nhanh hàng đầu Việt Nam',
    icon: 'truck',
    website: 'https://ghn.vn',
    supportedServices: ['standard', 'express'],
    requiredFields: ['apiKey', 'shopId'],
    optionalFields: ['token'],
  },
  ghtk: {
    id: 'ghtk',
    name: '<PERSON><PERSON><PERSON>',
    displayName: 'GHTK - Giao <PERSON>',
    description: 'Dị<PERSON> vụ giao hàng tiết kiệm chi phí',
    icon: 'package',
    website: 'https://ghtk.vn',
    supportedServices: ['standard', 'fast'],
    requiredFields: ['apiKey'],
    optionalFields: ['partnerId'],
  },
  'viettel-post': {
    id: 'viettel-post',
    name: 'Viettel Post',
    displayName: 'Viettel Post',
    description: 'Dịch vụ bưu chính Viettel',
    icon: 'mail',
    website: 'https://viettelpost.vn',
    supportedServices: ['standard', 'express', 'ems'],
    requiredFields: ['apiKey', 'clientId'],
    optionalFields: ['apiSecret'],
  },
  vnpost: {
    id: 'vnpost',
    name: 'VNPost',
    displayName: 'VNPost - Bưu điện Việt Nam',
    description: 'Tổng công ty Bưu điện Việt Nam',
    icon: 'mail-plus',
    website: 'https://vnpost.vn',
    supportedServices: ['standard', 'express', 'ems'],
    requiredFields: ['apiKey', 'clientId'],
    optionalFields: ['apiSecret'],
  },
};

export const SHIPPING_SERVICE_TYPES = {
  standard: {
    id: 'standard',
    name: 'Tiêu chuẩn',
    description: 'Giao hàng tiêu chuẩn (3-5 ngày)',
    estimatedDays: '3-5',
  },
  express: {
    id: 'express',
    name: 'Nhanh',
    description: 'Giao hàng nhanh (1-2 ngày)',
    estimatedDays: '1-2',
  },
  fast: {
    id: 'fast',
    name: 'Hỏa tốc',
    description: 'Giao hàng hỏa tốc (trong ngày)',
    estimatedDays: '0-1',
  },
  ems: {
    id: 'ems',
    name: 'EMS',
    description: 'Dịch vụ chuyển phát nhanh EMS',
    estimatedDays: '1-3',
  },
};

export const SHIPPING_ORDER_STATUSES = {
  pending: {
    id: 'pending',
    name: 'Chờ xử lý',
    color: 'warning',
    description: 'Đơn hàng đang chờ xử lý',
  },
  confirmed: {
    id: 'confirmed',
    name: 'Đã xác nhận',
    color: 'info',
    description: 'Đơn hàng đã được xác nhận',
  },
  picked_up: {
    id: 'picked_up',
    name: 'Đã lấy hàng',
    color: 'primary',
    description: 'Hàng đã được lấy từ người gửi',
  },
  in_transit: {
    id: 'in_transit',
    name: 'Đang vận chuyển',
    color: 'primary',
    description: 'Hàng đang được vận chuyển',
  },
  delivered: {
    id: 'delivered',
    name: 'Đã giao',
    color: 'success',
    description: 'Hàng đã được giao thành công',
  },
  cancelled: {
    id: 'cancelled',
    name: 'Đã hủy',
    color: 'error',
    description: 'Đơn hàng đã bị hủy',
  },
  returned: {
    id: 'returned',
    name: 'Hoàn trả',
    color: 'secondary',
    description: 'Hàng đã được hoàn trả',
  },
} as const;

export const DEFAULT_SHIPPING_SETTINGS = {
  ghn: {
    defaultServiceType: 'standard',
    defaultPackageType: 'box',
    enableWebhook: false,
  },
  ghtk: {
    defaultServiceType: 'standard',
    defaultPackageType: 'box',
    enableWebhook: false,
  },
  'viettel-post': {
    defaultServiceType: 'standard',
    defaultPackageType: 'box',
    enableWebhook: false,
  },
  vnpost: {
    defaultServiceType: 'standard',
    defaultPackageType: 'box',
    enableWebhook: false,
  },
};

export const PACKAGE_TYPES = [
  { id: 'box', name: 'Hộp', description: 'Đóng gói bằng hộp carton' },
  { id: 'envelope', name: 'Phong bì', description: 'Đóng gói bằng phong bì' },
  { id: 'bag', name: 'Túi', description: 'Đóng gói bằng túi nilon' },
  { id: 'tube', name: 'Ống', description: 'Đóng gói bằng ống tròn' },
];

export const WEIGHT_UNITS = [
  { id: 'kg', name: 'Kilogram (kg)', symbol: 'kg' },
  { id: 'g', name: 'Gram (g)', symbol: 'g' },
];

export const DIMENSION_UNITS = [
  { id: 'cm', name: 'Centimeter (cm)', symbol: 'cm' },
  { id: 'm', name: 'Meter (m)', symbol: 'm' },
];

export const CURRENCY_UNITS = [
  { id: 'VND', name: 'Việt Nam Đồng', symbol: '₫' },
  { id: 'USD', name: 'US Dollar', symbol: '$' },
];

/**
 * Form field configurations for each provider
 */
export const PROVIDER_FORM_CONFIGS = {
  ghn: {
    fields: [
      { name: 'apiKey', label: 'API Key', type: 'password', required: true },
      { name: 'shopId', label: 'Shop ID', type: 'text', required: true },
      { name: 'token', label: 'Token', type: 'password', required: false },
    ],
  },
  ghtk: {
    fields: [
      { name: 'apiKey', label: 'API Key', type: 'password', required: true },
      { name: 'partnerId', label: 'Partner ID', type: 'text', required: false },
    ],
  },
  'viettel-post': {
    fields: [
      { name: 'apiKey', label: 'API Key', type: 'password', required: true },
      { name: 'clientId', label: 'Client ID', type: 'text', required: true },
      { name: 'apiSecret', label: 'API Secret', type: 'password', required: false },
    ],
  },
  vnpost: {
    fields: [
      { name: 'apiKey', label: 'API Key', type: 'password', required: true },
      { name: 'clientId', label: 'Client ID', type: 'text', required: true },
      { name: 'apiSecret', label: 'API Secret', type: 'password', required: false },
    ],
  },
};
