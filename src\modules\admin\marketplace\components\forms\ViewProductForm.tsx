import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Divider,
  FormItem,
  Form,
  Chip,
  Modal,
  Icon,
} from '@/shared/components/common';
import { Product, ProductStatus } from '@/modules/admin/marketplace/types/product.types';

export interface ViewProductFormProps {
  /**
   * Product data to display
   */
  product: Product | null;

  /**
   * Function to handle form close
   */
  onClose: () => void;
}

/**
 * Form component for viewing product details
 */
const ViewProductForm: React.FC<ViewProductFormProps> = ({ product, onClose }) => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho image viewer
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);

  // Handle image click
  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index);
    setIsImageModalOpen(true);
  };

  // Handle close image modal
  const handleCloseImageModal = () => {
    setIsImageModalOpen(false);
    setSelectedImageIndex(null);
  };

  // Handle navigate images
  const handlePrevImage = () => {
    if (selectedImageIndex !== null && product?.images && selectedImageIndex > 0) {
      setSelectedImageIndex(selectedImageIndex - 1);
    }
  };

  const handleNextImage = () => {
    if (selectedImageIndex !== null && product?.images && selectedImageIndex < product.images.length - 1) {
      setSelectedImageIndex(selectedImageIndex + 1);
    }
  };

  if (!product) {
    return (
      <Card>
        <Typography variant="body1">
          {t('admin:marketplace.product.noData', 'Không có dữ liệu sản phẩm')}
        </Typography>
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onClose}>
            {t('common:close', 'Đóng')}
          </Button>
        </div>
      </Card>
    );
  }

  // Get status variant for chip
  const getStatusVariant = (status: ProductStatus) => {
    switch (status) {
      case ProductStatus.APPROVED:
        return 'success';
      case ProductStatus.PENDING:
        return 'warning';
      case ProductStatus.REJECTED:
        return 'danger';
      case ProductStatus.DRAFT:
        return 'default';
      case ProductStatus.DELETED:
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4 flex items-center justify-between">
          <span>{t('admin:marketplace.product.details', 'Chi tiết sản phẩm')}</span>
          <Chip
            size="sm"
            variant={
              getStatusVariant(product.status) as
                | 'default'
                | 'primary'
                | 'success'
                | 'warning'
                | 'danger'
            }
          >
            {t(`admin:marketplace.product.status.${product.status}`, product.status)}
          </Chip>
        </Typography>

        <Form className="space-y-6" onSubmit={() => {}}>
          {/* Basic Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.basicInfo', 'Thông tin cơ bản')}
            </Typography>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="id" label={t('admin:marketplace.product.form.id', 'ID')}>
                <div className="p-2 bg-card-muted rounded border border-border">{product.id}</div>
              </FormItem>

              <FormItem
                name="name"
                label={t('admin:marketplace.product.form.name', 'Tên sản phẩm')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">{product.name}</div>
              </FormItem>
            </div>
            <FormItem
              name="category"
              label={t('admin:marketplace.product.form.category', 'Loại sản phẩm')}
            >
              <div className="p-2 bg-card-muted rounded border border-border">
                {t(`admin:marketplace.product.category.${product.category}`, product.category)}
              </div>
            </FormItem>

            <FormItem
              name="description"
              label={t('admin:marketplace.product.form.description', 'Mô tả')}
            >
              <div className="p-2 bg-card-muted rounded border border-border min-h-[80px]">
                {product.description}
              </div>
            </FormItem>
          </div>

          <Divider />

          {/* Price Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.priceInfo', 'Thông tin giá')}
            </Typography>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="listedPrice"
                label={t('admin:marketplace.product.form.price', 'Giá gốc')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.listedPrice} points
                </div>
              </FormItem>

              <FormItem
                name="discountedPrice"
                label={t('admin:marketplace.product.form.discountedPrice', 'Giá khuyến mãi')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.discountedPrice} points
                </div>
              </FormItem>
            </div>
          </div>

          <Divider />

          {/* Seller Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.sellerInfo', 'Thông tin người bán')}
            </Typography>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                name="sellerName"
                label={t('admin:marketplace.product.form.sellerName', 'Tên người bán')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.name}
                </div>
              </FormItem>

              <FormItem
                name="sellerEmail"
                label={t('admin:marketplace.product.form.sellerEmail', 'Email')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.email || t('common:notAvailable', 'Không có')}
                </div>
              </FormItem>

              <FormItem
                name="sellerPhone"
                label={t('admin:marketplace.product.form.sellerPhone', 'Số điện thoại')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.phoneNumber || t('common:notAvailable', 'Không có')}
                </div>
              </FormItem>

              <FormItem
                name="sellerType"
                label={t('admin:marketplace.product.form.sellerType', 'Loại người bán')}
              >
                <div className="p-2 bg-card-muted rounded border border-border">
                  {product.seller.type}
                </div>
              </FormItem>
            </div>
          </div>

          <Divider />

          {/* Documents and Images */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:marketplace.product.documents', 'Tài liệu & Hình ảnh')}
            </Typography>

            {/* Images */}
            {product.images && product.images.length > 0 && (
              <FormItem
                name="images"
                label={t('admin:marketplace.product.form.images', 'Hình ảnh')}
              >
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {product.images.map((image, index) => {
                    // Handle both new format (object) and old format (string)
                    const imageUrl = typeof image === 'object' ? image.url : image;
                    return (
                      <div
                        key={index}
                        className="border border-border rounded overflow-hidden cursor-pointer hover:shadow-lg transition-shadow group relative"
                        onClick={() => handleImageClick(index)}
                      >
                        <img
                          src={`https://cdn.redai.vn/${imageUrl}`}
                          alt={`${product.name} - ${index + 1}`}
                          className="w-full h-32 object-cover group-hover:scale-105 transition-transform"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                          <Icon
                            name="eye"
                            size="lg"
                            className="text-white opacity-0 group-hover:opacity-100 transition-opacity"
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </FormItem>
            )}
          </div>

          <div className="flex justify-end pt-4">
            <Button variant="outline" onClick={onClose}>
              {t('common:close', 'Đóng')}
            </Button>
          </div>
        </Form>
      </div>

      {/* Image Viewer Modal */}
      <Modal
        isOpen={isImageModalOpen}
        onClose={handleCloseImageModal}
        title={t('admin:marketplace.product.imageViewer', 'Xem ảnh sản phẩm')}
        size="xl"
        footer={
          <div className="flex justify-between items-center w-full">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handlePrevImage}
                disabled={selectedImageIndex === 0}
                leftIcon={<Icon name="chevron-left" size="sm" />}
              >
                {t('common:previous', 'Trước')}
              </Button>
              <Button
                variant="outline"
                onClick={handleNextImage}
                disabled={selectedImageIndex === (product?.images?.length || 0) - 1}
                rightIcon={<Icon name="chevron-right" size="sm" />}
              >
                {t('common:next', 'Tiếp')}
              </Button>
            </div>
            <div className="text-sm text-muted">
              {selectedImageIndex !== null && product?.images &&
                `${selectedImageIndex + 1} / ${product.images.length}`
              }
            </div>
            <Button variant="outline" onClick={handleCloseImageModal}>
              {t('common:close', 'Đóng')}
            </Button>
          </div>
        }
      >
        {selectedImageIndex !== null && product?.images && (
          <div className="flex justify-center items-center p-4">
            <img
              src={typeof product.images[selectedImageIndex] === 'object'
                ? product.images[selectedImageIndex].url
                : product.images[selectedImageIndex]}
              alt={`${product.name} - ${selectedImageIndex + 1}`}
              className="max-w-full max-h-[70vh] object-contain rounded"
            />
          </div>
        )}
      </Modal>
    </Card>
  );
};

export default ViewProductForm;
