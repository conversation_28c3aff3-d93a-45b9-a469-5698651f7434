import { apiClient } from '@/shared/api/axios';
import type {
  GoogleCalendarConfiguration,
  CreateGoogleCalendarDto,
  UpdateGoogleCalendarDto,
  TestGoogleCalendarDto,
  TestGoogleCalendarWithConfigDto,
  GoogleCalendarQueryParams,
  GoogleCalendarTestResult,
  GoogleCalendarEvent,
  CalendarSyncStatus,
} from '../types';
import type { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Google Calendar API Service
 */
export class GoogleCalendarService {
  private static readonly BASE_URL = '/user/integration/google-calendar';

  /**
   * Get all Google Calendar configurations
   */
  static async getConfigurations(
    params?: GoogleCalendarQueryParams
  ): Promise<PaginatedResult<GoogleCalendarConfiguration>> {
    const response = await apiClient.get<PaginatedResult<GoogleCalendarConfiguration>>(
      this.BASE_URL,
      { params }
    );
    return response.result;
  }

  /**
   * Get Google Calendar configuration by ID
   */
  static async getConfiguration(id: number): Promise<GoogleCalendarConfiguration> {
    const response = await apiClient.get<GoogleCalendarConfiguration>(
      `${this.BASE_URL}/${id}`
    );
    return response.result;
  }

  /**
   * Create new Google Calendar configuration
   */
  static async createConfiguration(data: CreateGoogleCalendarDto): Promise<GoogleCalendarConfiguration> {
    const response = await apiClient.post<GoogleCalendarConfiguration>(
      this.BASE_URL,
      data
    );
    return response.result;
  }

  /**
   * Update Google Calendar configuration
   */
  static async updateConfiguration(
    id: number,
    data: UpdateGoogleCalendarDto
  ): Promise<GoogleCalendarConfiguration> {
    const response = await apiClient.put<GoogleCalendarConfiguration>(
      `${this.BASE_URL}/${id}`,
      data
    );
    return response.result;
  }

  /**
   * Delete Google Calendar configuration
   */
  static async deleteConfiguration(id: number): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Test Google Calendar configuration
   */
  static async testConfiguration(
    id: number,
    testData?: TestGoogleCalendarDto
  ): Promise<GoogleCalendarTestResult> {
    const response = await apiClient.post<GoogleCalendarTestResult>(
      `${this.BASE_URL}/${id}/test`,
      testData
    );
    return response.result;
  }

  /**
   * Test Google Calendar with configuration
   */
  static async testWithConfiguration(
    data: TestGoogleCalendarWithConfigDto
  ): Promise<GoogleCalendarTestResult> {
    const response = await apiClient.post<GoogleCalendarTestResult>(
      `${this.BASE_URL}/test-with-config`,
      data
    );
    return response.result;
  }

  /**
   * Sync calendar events
   */
  static async syncEvents(id: number): Promise<CalendarSyncStatus> {
    const response = await apiClient.post<CalendarSyncStatus>(
      `${this.BASE_URL}/${id}/sync`
    );
    return response.result;
  }

  /**
   * Get calendar events
   */
  static async getEvents(
    id: number,
    params?: {
      startDate?: string;
      endDate?: string;
      maxResults?: number;
    }
  ): Promise<GoogleCalendarEvent[]> {
    const response = await apiClient.get<GoogleCalendarEvent[]>(
      `${this.BASE_URL}/${id}/events`,
      { params }
    );
    return response.result;
  }

  /**
   * Create calendar event
   */
  static async createEvent(
    id: number,
    eventData: Partial<GoogleCalendarEvent>
  ): Promise<GoogleCalendarEvent> {
    const response = await apiClient.post<GoogleCalendarEvent>(
      `${this.BASE_URL}/${id}/events`,
      eventData
    );
    return response.result;
  }

  /**
   * Get sync status
   */
  static async getSyncStatus(id: number): Promise<CalendarSyncStatus> {
    const response = await apiClient.get<CalendarSyncStatus>(
      `${this.BASE_URL}/${id}/sync-status`
    );
    return response.result;
  }

  /**
   * Get OAuth URL for Google Calendar
   */
  static async getOAuthUrl(redirectUri: string): Promise<{ authUrl: string }> {
    const response = await apiClient.post<{ authUrl: string }>(
      `${this.BASE_URL}/oauth-url`,
      { redirectUri }
    );
    return response.result;
  }

  /**
   * Exchange OAuth code for tokens
   */
  static async exchangeOAuthCode(code: string, redirectUri: string): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const response = await apiClient.post<{
      accessToken: string;
      refreshToken: string;
    }>(
      `${this.BASE_URL}/oauth-exchange`,
      { code, redirectUri }
    );
    return response.result;
  }
}
