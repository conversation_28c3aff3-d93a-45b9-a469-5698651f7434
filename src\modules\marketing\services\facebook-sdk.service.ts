/**
 * Facebook Marketing API SDK Service
 * Wrapper cho Facebook Marketing API v18 với error handling và rate limiting
 */

import { FacebookAuthUtils } from './facebook-auth.service';
import { env } from '@/shared/utils/env';

export interface FacebookSDKConfig {
  appId: string;
  version: string;
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface FacebookAPIResponse<T = unknown> {
  data: T;
  paging?: {
    cursors?: {
      before: string;
      after: string;
    };
    next?: string;
    previous?: string;
  };
  error?: {
    message: string;
    type: string;
    code: number;
    error_subcode?: number;
    fbtrace_id?: string;
  };
}

/**
 * Facebook SDK Service Class
 */
export class FacebookSDKService {
  private config: FacebookSDKConfig;
  private rateLimitTracker: Map<string, { count: number; resetTime: number }> = new Map();

  constructor(config: Partial<FacebookSDKConfig> = {}) {
    // Extract Facebook App ID from the login URL if not provided directly
    const getFacebookAppId = (): string => {
      // First try direct environment variable
      const directAppId = env.get('VITE_FACEBOOK_APP_ID', '');
      if (directAppId) return directAppId;

      // Fallback to legacy variable
      const legacyAppId = env.get('REACT_APP_FACEBOOK_APP_ID', '');
      if (legacyAppId) return legacyAppId;

      // Extract from Facebook login URL
      const facebookLoginUrl = env.facebookLoginUrl;
      if (facebookLoginUrl) {
        const match = facebookLoginUrl.match(/client_id=([^&]+)/);
        if (match) return match[1];
      }

      return '';
    };

    this.config = {
      appId: getFacebookAppId(),
      version: 'v18.0',
      baseUrl: 'https://graph.facebook.com',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config,
    };
  }

  /**
   * Make authenticated API request
   */
  async makeRequest<T = unknown>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: unknown,
    accessToken?: string
  ): Promise<FacebookAPIResponse<T>> {
    const token = accessToken || FacebookAuthUtils.getAccessToken();
    
    if (!token) {
      throw new Error('No access token available');
    }

    const url = this.buildUrl(endpoint);
    const requestId = `${method}:${endpoint}`;

    // Check rate limits
    await this.checkRateLimit(requestId);

    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
      try {
        const response = await this.executeRequest<T>(url, method, data, token);
        
        // Update rate limit tracker
        this.updateRateLimit(requestId);
        
        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        // Don't retry on authentication errors
        if (this.isAuthError(error)) {
          throw lastError;
        }

        // Wait before retry
        if (attempt < this.config.retryAttempts - 1) {
          await this.delay(this.config.retryDelay * (attempt + 1));
        }
      }
    }

    throw lastError || new Error('Request failed after retries');
  }

  /**
   * Execute HTTP request
   */
  private async executeRequest<T>(
    url: string,
    method: string,
    data: unknown,
    token: string
  ): Promise<FacebookAPIResponse<T>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const requestOptions: RequestInit = {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      };

      if (data && (method === 'POST' || method === 'PUT')) {
        requestOptions.body = JSON.stringify(data);
      }

      const response = await fetch(url, requestOptions);
      const responseData = await response.json();

      if (!response.ok) {
        throw new FacebookAPIError(
          responseData.error?.message || 'API request failed',
          responseData.error?.type || 'unknown_error',
          responseData.error?.code || response.status,
          responseData.error?.error_subcode,
          responseData.error?.fbtrace_id
        );
      }

      return responseData;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * Build API URL
   */
  private buildUrl(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.config.baseUrl}/${this.config.version}/${cleanEndpoint}`;
  }

  /**
   * Check rate limits
   */
  private async checkRateLimit(requestId: string): Promise<void> {
    const now = Date.now();
    const tracker = this.rateLimitTracker.get(requestId);

    if (tracker && tracker.resetTime > now) {
      if (tracker.count >= 200) { // Facebook's default rate limit
        const waitTime = tracker.resetTime - now;
        await this.delay(waitTime);
      }
    }
  }

  /**
   * Update rate limit tracker
   */
  private updateRateLimit(requestId: string): void {
    const now = Date.now();
    const resetTime = now + (60 * 60 * 1000); // 1 hour window

    const tracker = this.rateLimitTracker.get(requestId) || { count: 0, resetTime };
    
    if (tracker.resetTime <= now) {
      tracker.count = 1;
      tracker.resetTime = resetTime;
    } else {
      tracker.count++;
    }

    this.rateLimitTracker.set(requestId, tracker);
  }

  /**
   * Check if error is authentication related
   */
  private isAuthError(error: unknown): boolean {
    if (error instanceof FacebookAPIError) {
      return error.code === 190 || error.code === 102 || error.type === 'OAuthException';
    }
    return false;
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get Ad Account information
   */
  async getAdAccount(accountId: string): Promise<FacebookAPIResponse> {
    return this.makeRequest(`${accountId}?fields=id,name,account_status,currency,timezone_name,business_name,spend_cap,balance`);
  }

  /**
   * Get Ad Account campaigns
   */
  async getCampaigns(accountId: string, params?: Record<string, string>): Promise<FacebookAPIResponse> {
    const queryParams = new URLSearchParams(params).toString();
    const endpoint = `${accountId}/campaigns?fields=id,name,objective,status,daily_budget,lifetime_budget,start_time,stop_time,created_time,updated_time&${queryParams}`;
    return this.makeRequest(endpoint);
  }

  /**
   * Get Campaign insights
   */
  async getCampaignInsights(campaignId: string, params?: Record<string, string>): Promise<FacebookAPIResponse> {
    const queryParams = new URLSearchParams(params).toString();
    const endpoint = `${campaignId}/insights?fields=impressions,clicks,ctr,cpc,spend,reach,frequency&${queryParams}`;
    return this.makeRequest(endpoint);
  }

  /**
   * Get Ad Sets
   */
  async getAdSets(campaignId: string, params?: Record<string, string>): Promise<FacebookAPIResponse> {
    const queryParams = new URLSearchParams(params).toString();
    const endpoint = `${campaignId}/adsets?fields=id,name,status,daily_budget,lifetime_budget,bid_strategy,billing_event,optimization_goal,targeting&${queryParams}`;
    return this.makeRequest(endpoint);
  }

  /**
   * Get Ads
   */
  async getAds(adSetId: string, params?: Record<string, string>): Promise<FacebookAPIResponse> {
    const queryParams = new URLSearchParams(params).toString();
    const endpoint = `${adSetId}/ads?fields=id,name,status,creative,tracking_specs,conversion_specs&${queryParams}`;
    return this.makeRequest(endpoint);
  }

  /**
   * Create Campaign
   */
  async createCampaign(accountId: string, campaignData: unknown): Promise<FacebookAPIResponse> {
    return this.makeRequest(`${accountId}/campaigns`, 'POST', campaignData);
  }

  /**
   * Update Campaign
   */
  async updateCampaign(campaignId: string, campaignData: unknown): Promise<FacebookAPIResponse> {
    return this.makeRequest(campaignId, 'POST', campaignData);
  }

  /**
   * Delete Campaign
   */
  async deleteCampaign(campaignId: string): Promise<FacebookAPIResponse> {
    return this.makeRequest(campaignId, 'DELETE');
  }

  /**
   * Get Custom Audiences
   */
  async getCustomAudiences(accountId: string): Promise<FacebookAPIResponse> {
    return this.makeRequest(`${accountId}/customaudiences?fields=id,name,description,subtype,approximate_count,data_source`);
  }

  /**
   * Create Custom Audience
   */
  async createCustomAudience(accountId: string, audienceData: unknown): Promise<FacebookAPIResponse> {
    return this.makeRequest(`${accountId}/customaudiences`, 'POST', audienceData);
  }
}

/**
 * Custom Facebook API Error class
 */
export class FacebookAPIError extends Error {
  public type: string;
  public code: number;
  public error_subcode?: number;
  public fbtrace_id?: string;

  constructor(
    message: string,
    type: string,
    code: number,
    error_subcode?: number,
    fbtrace_id?: string
  ) {
    super(message);
    this.name = 'FacebookAPIError';
    this.type = type;
    this.code = code;
    this.error_subcode = error_subcode;
    this.fbtrace_id = fbtrace_id;
  }
}

// Export singleton instance
export const facebookSDK = new FacebookSDKService();
