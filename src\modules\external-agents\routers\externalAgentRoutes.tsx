import React from 'react';
import { Routes, Route } from 'react-router-dom';
import {
  ExternalAgentsPage,
  AgentDetailPage,
  AgentTestingPage,
  AgentAnalyticsPage,
  MessageHistoryPage,
} from '../pages';

const ExternalAgentRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Main agents list page */}
      <Route path="/" element={<ExternalAgentsPage />} />
      
      {/* Agent detail page */}
      <Route path="/:id" element={<AgentDetailPage />} />
      
      {/* Agent testing page */}
      <Route path="/:id/testing" element={<AgentTestingPage />} />
      
      {/* Agent analytics page */}
      <Route path="/:id/analytics" element={<AgentAnalyticsPage />} />
      
      {/* Message history page */}
      <Route path="/:id/messages" element={<MessageHistoryPage />} />
    </Routes>
  );
};

export default ExternalAgentRoutes;
