import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Typography,
  Button,
  Input,
  Textarea,
  Select,
  FormItem,
  Card,
} from '@/shared/components/common';
import { UpdateSubscriptionPlanSchema } from '../../schemas/subscription-plans.admin.schemas';
import {
  SubscriptionPlan,
  UpdateSubscriptionPlanDto,
  PackageType
} from '../../types/subscription-plans.admin.types';

interface SubscriptionPlanEditFormProps {
  plan: SubscriptionPlan;
  onSubmit: (id: number, data: UpdateSubscriptionPlanDto) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form chỉnh sửa gói dịch vụ subscription
 */
const SubscriptionPlanEditForm: React.FC<SubscriptionPlanEditFormProps> = ({
  plan,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UpdateSubscriptionPlanDto>({
    resolver: zodResolver(UpdateSubscriptionPlanSchema),
    defaultValues: {
      name: plan.name,
      description: plan.description,
      packageType: plan.packageType,
    },
  });

  // Xử lý submit form
  const handleFormSubmit = async (data: UpdateSubscriptionPlanDto) => {
    try {
      await onSubmit(plan.id, data);
      reset(); // Reset form sau khi submit thành công
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Xử lý hủy
  const handleCancel = () => {
    reset();
    onCancel();
  };

  // Options cho package type
  const packageTypeOptions = [
    {
      value: PackageType.TIME_ONLY,
      label: t('admin:subscription.packageType.timeOnly'),
    },
    {
      value: PackageType.HYBRID,
      label: t('admin:subscription.packageType.hybrid'),
    },
  ];

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h3" className="mb-6">
          {t('admin:subscription.plan.form.edit.title')}
        </Typography>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Tên gói dịch vụ */}
          <FormItem
            label={t('admin:subscription.plan.form.name.label')}
            required
          >
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder={t('admin:subscription.plan.form.name.placeholder')}
                  error={errors.name?.message}
                  fullWidth
                />
              )}
            />
          </FormItem>

          {/* Mô tả */}
          <FormItem
            label={t('admin:subscription.plan.form.description.label')}
            required
          >
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  placeholder={t('admin:subscription.plan.form.description.placeholder')}
                  rows={4}
                  status={errors.description?.message ? 'error' : 'default'}
                  fullWidth 
                />
              )}
            />
          </FormItem>

          {/* Loại gói dịch vụ */}
          <FormItem
            label={t('admin:subscription.plan.form.packageType.label')}
            required
          >
            <Controller
              name="packageType"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  options={packageTypeOptions}
                  placeholder={t('admin:subscription.plan.form.packageType.placeholder')}
                  error={errors.packageType?.message}
                  fullWidth
                />
              )}
            />
          </FormItem>

          {/* Buttons */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              {t('admin:subscription.plan.form.edit.submit')}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default SubscriptionPlanEditForm;
