{"title": "External Agents", "subtitle": "<PERSON><PERSON><PERSON><PERSON> lý các agent bê<PERSON> ng<PERSON>i", "description": "<PERSON><PERSON><PERSON> n<PERSON>i và quản lý các agent bên ngoài thông qua nhiều giao thức khác nhau", "navigation": {"overview": "<PERSON><PERSON><PERSON> quan", "agents": "<PERSON><PERSON>", "protocols": "<PERSON><PERSON><PERSON> th<PERSON>", "templates": "Mẫu giao thức", "webhooks": "Webhooks", "analytics": "<PERSON><PERSON> tích", "messages": "<PERSON>", "settings": "Cài đặt"}, "agent": {"title": "Agent", "name": "Tên agent", "description": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "protocol": "<PERSON><PERSON><PERSON> th<PERSON>", "endpoint": "Endpoint", "capabilities": "<PERSON><PERSON><PERSON> n<PERSON>ng", "tags": "Thẻ", "created": "<PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON>", "lastConnected": "<PERSON><PERSON><PERSON> n<PERSON> cu<PERSON>i", "version": "<PERSON><PERSON><PERSON>", "isActive": "<PERSON><PERSON> ho<PERSON>t động"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "connecting": "<PERSON><PERSON> kết n<PERSON>i", "error": "Lỗi", "maintenance": "<PERSON><PERSON><PERSON> trì"}, "protocol": {"mcp": "Model Context Protocol", "google_agent": "Google Agent Communication", "rest_api": "REST API", "websocket": "WebSocket", "grpc": "gRPC", "custom": "<PERSON><PERSON><PERSON> thức tùy chỉnh"}, "authentication": {"none": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c thực", "api_key": "API Key", "bearer_token": "<PERSON><PERSON>", "oauth2": "OAuth 2.0", "basic_auth": "Basic Authentication", "custom": "<PERSON><PERSON><PERSON> thực tùy chỉnh"}, "capabilities": {"text_processing": "<PERSON><PERSON> lý văn bản", "image_analysis": "<PERSON><PERSON> tích h<PERSON>nh <PERSON>nh", "data_retrieval": "<PERSON><PERSON><PERSON> xu<PERSON>t dữ liệu", "file_operations": "<PERSON><PERSON> t<PERSON> file", "api_calls": "Gọi API", "real_time_communication": "<PERSON><PERSON><PERSON> ti<PERSON>p thời gian thực", "custom": "<PERSON><PERSON><PERSON> chỉnh"}, "actions": {"create": "<PERSON><PERSON><PERSON> mới", "edit": "Chỉnh sửa", "delete": "Xóa", "test": "<PERSON><PERSON><PERSON> tra", "connect": "<PERSON><PERSON><PERSON>", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "export": "<PERSON><PERSON><PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON> lại"}, "form": {"required": "<PERSON><PERSON><PERSON> b<PERSON>", "optional": "<PERSON><PERSON><PERSON>", "placeholder": {"name": "<PERSON><PERSON><PERSON><PERSON> tên agent", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả agent", "endpoint": "https://api.example.com", "apiKey": "Nhập API key", "token": "Nhập token", "username": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "password": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "search": "<PERSON><PERSON><PERSON> k<PERSON> agents..."}, "validation": {"required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "invalidUrl": "URL không hợp lệ", "invalidEmail": "<PERSON><PERSON> h<PERSON> l<PERSON>", "minLength": "<PERSON><PERSON><PERSON> thiểu {min} ký tự", "maxLength": "T<PERSON>i đa {max} ký tự"}}, "messages": {"success": {"created": "Tạo agent thà<PERSON> công", "updated": "<PERSON><PERSON><PERSON> nhật agent th<PERSON><PERSON> công", "deleted": "Xóa agent th<PERSON><PERSON> công", "connected": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "tested": "<PERSON><PERSON><PERSON> tra kết nối thành công"}, "error": {"createFailed": "Tạo agent thất b<PERSON>i", "updateFailed": "<PERSON><PERSON><PERSON> nhật agent thất bại", "deleteFailed": "Xóa agent thất b<PERSON>i", "connectionFailed": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại", "testFailed": "<PERSON><PERSON><PERSON> tra kết nối thất bại", "loadFailed": "<PERSON><PERSON><PERSON> dữ liệu thất bại"}}, "connection": {"test": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testing": "<PERSON><PERSON> kiểm tra...", "success": "<PERSON><PERSON><PERSON> n<PERSON>i thành công", "failed": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại", "timeout": "<PERSON><PERSON><PERSON> th<PERSON>i gian chờ", "responseTime": "<PERSON><PERSON><PERSON><PERSON> gian ph<PERSON>n hồi", "lastTest": "<PERSON><PERSON><PERSON> tra cuối"}, "performance": {"title": "<PERSON><PERSON><PERSON>", "totalRequests": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "successfulRequests": "<PERSON><PERSON><PERSON> c<PERSON>u thành công", "failedRequests": "<PERSON><PERSON><PERSON> c<PERSON>u thất bại", "averageResponseTime": "<PERSON>h<PERSON><PERSON> gian phản hồi trung bình", "uptime": "<PERSON><PERSON><PERSON><PERSON> gian ho<PERSON>t động", "successRate": "Tỷ lệ thành công"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "protocol": "<PERSON><PERSON><PERSON> th<PERSON>", "capabilities": "<PERSON><PERSON><PERSON> n<PERSON>ng", "tags": "Thẻ", "dateRange": "<PERSON><PERSON><PERSON><PERSON> thời gian", "clear": "Xóa bộ lọc"}, "pagination": {"previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON>", "of": "c<PERSON>a", "items": "m<PERSON>c", "showing": "<PERSON><PERSON><PERSON> thị", "to": "<PERSON><PERSON><PERSON>"}, "empty": {"noAgents": "Chưa có agent nào", "noMessages": "<PERSON><PERSON><PERSON> có tin nhắn nào", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "createFirst": "Tạo agent <PERSON><PERSON><PERSON> tiên của bạn"}, "loading": {"agents": "<PERSON><PERSON> tải agents...", "messages": "<PERSON><PERSON> tải tin nhắn...", "testing": "<PERSON><PERSON> kiểm tra kết nối...", "saving": "<PERSON><PERSON> l<PERSON>...", "deleting": "Đang xóa..."}}