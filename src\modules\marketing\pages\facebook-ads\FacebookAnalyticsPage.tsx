import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Select,
  DatePicker,
  Tabs,
} from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import FacebookAnalyticsDashboard from '../../components/facebook-ads/FacebookAnalyticsDashboard';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';

/**
 * Facebook Analytics Page
 * Trang phân tích và báo cáo Facebook Ads
 */
const FacebookAnalyticsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const [selectedAccount, setSelectedAccount] = useState<string>('all');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    endDate: new Date(),
  });
  const [activeTab, setActiveTab] = useState('overview');

  const { isAuthenticated, adAccounts } = useFacebookAuth();

  const accountOptions = [
    { value: 'all', label: t('common:filter.allAccounts', 'Tất cả tài khoản') },
    ...adAccounts.map(account => ({
      value: account.accountId,
      label: `${account.name} (${account.accountId})`,
    })),
  ];

  const handleExportReport = () => {
    console.log('Export report');
    // TODO: Implement export functionality
  };

  const handleRefreshData = () => {
    console.log('Refresh data');
    // TODO: Implement refresh functionality
  };

  if (!isAuthenticated) {
    return (
      <div className="w-full bg-background text-foreground">
        <MarketingViewHeader
          title={t('marketing:facebookAds.analytics.title', 'Phân tích Facebook Ads')}
          description={t('marketing:facebookAds.analytics.description', 'Theo dõi hiệu suất và tối ưu hóa chiến dịch quảng cáo')}
          icon="bar-chart"
        />

        <Card className="p-8 text-center">
          <Icon name="facebook" size="xl" className="text-muted-foreground mb-4" />
          <Typography variant="h6" className="mb-2">
            {t('marketing:facebookAds.analytics.notConnected.title', 'Chưa kết nối Facebook')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('marketing:facebookAds.analytics.notConnected.description', 'Kết nối tài khoản Facebook để xem phân tích và báo cáo')}
          </Typography>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground">
      <MarketingViewHeader
        title={t('marketing:facebookAds.analytics.title', 'Phân tích Facebook Ads')}
        description={t('marketing:facebookAds.analytics.description', 'Theo dõi hiệu suất và tối ưu hóa chiến dịch quảng cáo')}
        icon="bar-chart"
        actions={
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleRefreshData}>
              <Icon name="refresh-cw" className="mr-2" />
              {t('common:button.refresh', 'Làm mới')}
            </Button>
            <Button variant="primary" onClick={handleExportReport}>
              <Icon name="download" className="mr-2" />
              {t('marketing:facebookAds.analytics.export', 'Xuất báo cáo')}
            </Button>
          </div>
        }
      />

      {/* Filters */}
      <Card className="mb-6">
        <div className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Typography variant="body2" className="mb-2 text-muted-foreground">
                {t('marketing:facebookAds.analytics.filters.account', 'Tài khoản')}
              </Typography>
              <Select
                value={selectedAccount}
                onChange={(value) => setSelectedAccount(value as string)}
                options={accountOptions}
              />
            </div>
            
            <div>
              <Typography variant="body2" className="mb-2 text-muted-foreground">
                {t('marketing:facebookAds.analytics.filters.startDate', 'Từ ngày')}
              </Typography>
              <DatePicker
                value={dateRange.startDate}
                onChange={(value) => setDateRange(prev => ({ ...prev, startDate: value || new Date() }))}
              />
            </div>
            
            <div>
              <Typography variant="body2" className="mb-2 text-muted-foreground">
                {t('marketing:facebookAds.analytics.filters.endDate', 'Đến ngày')}
              </Typography>
              <DatePicker
                value={dateRange.endDate}
                onChange={(value) => setDateRange(prev => ({ ...prev, endDate: value || new Date() }))}
                minDate={dateRange.startDate}
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Analytics Tabs */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'overview',
            label: (
              <div className="flex items-center">
                <Icon name="bar-chart" className="mr-2" />
                {t('marketing:facebookAds.analytics.tabs.overview', 'Tổng quan')}
              </div>
            ),
            children: (
              <FacebookAnalyticsDashboard
                accountId={selectedAccount === 'all' ? undefined : selectedAccount}
                dateRange={{
                  startDate: dateRange.startDate.toISOString().split('T')[0],
                  endDate: dateRange.endDate.toISOString().split('T')[0],
                }}
                showDetailedMetrics={true}
              />
            ),
          },
          {
            key: 'campaigns',
            label: (
              <div className="flex items-center">
                <Icon name="campaign" className="mr-2" />
                {t('marketing:facebookAds.analytics.tabs.campaigns', 'Chiến dịch')}
              </div>
            ),
            children: (
              <Card className="p-8 text-center">
                <Icon name="campaign" size="xl" className="text-muted-foreground mb-4" />
                <Typography variant="h6" className="mb-2">
                  {t('marketing:facebookAds.analytics.campaigns.title', 'Phân tích chiến dịch')}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('marketing:facebookAds.analytics.campaigns.description', 'Phân tích chi tiết hiệu suất từng chiến dịch sẽ được hiển thị ở đây')}
                </Typography>
              </Card>
            ),
          },
          {
            key: 'audiences',
            label: (
              <div className="flex items-center">
                <Icon name="users" className="mr-2" />
                {t('marketing:facebookAds.analytics.tabs.audiences', 'Đối tượng')}
              </div>
            ),
            children: (
              <Card className="p-8 text-center">
                <Icon name="users" size="xl" className="text-muted-foreground mb-4" />
                <Typography variant="h6" className="mb-2">
                  {t('marketing:facebookAds.analytics.audiences.title', 'Phân tích đối tượng')}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('marketing:facebookAds.analytics.audiences.description', 'Thông tin chi tiết về hiệu suất các nhóm đối tượng sẽ được hiển thị ở đây')}
                </Typography>
              </Card>
            ),
          },
          {
            key: 'creatives',
            label: (
              <div className="flex items-center">
                <Icon name="image" className="mr-2" />
                {t('marketing:facebookAds.analytics.tabs.creatives', 'Creative')}
              </div>
            ),
            children: (
              <Card className="p-8 text-center">
                <Icon name="image" size="xl" className="text-muted-foreground mb-4" />
                <Typography variant="h6" className="mb-2">
                  {t('marketing:facebookAds.analytics.creatives.title', 'Phân tích Creative')}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('marketing:facebookAds.analytics.creatives.description', 'Hiệu suất của các creative quảng cáo sẽ được phân tích ở đây')}
                </Typography>
              </Card>
            ),
          },
        ]}
      />
    </div>
  );
};

export default FacebookAnalyticsPage;
