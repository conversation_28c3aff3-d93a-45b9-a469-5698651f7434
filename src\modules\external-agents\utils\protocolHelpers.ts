import { ProtocolType, AuthenticationType, ExternalAgentStatus } from '../types';
import { PROTOCOL_LABELS, AUTH_TYPE_LABELS, STATUS_LABELS, STATUS_COLORS } from '../constants';

// Protocol detection helpers
export const detectProtocolFromUrl = (url: string): ProtocolType | null => {
  try {
    const parsedUrl = new URL(url);
    
    // WebSocket detection
    if (parsedUrl.protocol === 'ws:' || parsedUrl.protocol === 'wss:') {
      return ProtocolType.WEBSOCKET;
    }
    
    // gRPC detection (common patterns)
    if (parsedUrl.port === '443' || parsedUrl.port === '9090' || url.includes('grpc')) {
      return ProtocolType.GRPC;
    }
    
    // Default to REST API for HTTP/HTTPS
    if (parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:') {
      return ProtocolType.REST_API;
    }
    
    return null;
  } catch {
    return null;
  }
};

// Get protocol display name
export const getProtocolLabel = (protocol: ProtocolType): string => {
  return PROTOCOL_LABELS[protocol] || protocol;
};

// Get authentication type display name
export const getAuthTypeLabel = (authType: AuthenticationType): string => {
  return AUTH_TYPE_LABELS[authType] || authType;
};

// Get status display name
export const getStatusLabel = (status: ExternalAgentStatus): string => {
  return STATUS_LABELS[status] || status;
};

// Get status color
export const getStatusColor = (status: ExternalAgentStatus): string => {
  return STATUS_COLORS[status] || 'secondary';
};

// Validate URL format
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Extract domain from URL
export const extractDomain = (url: string): string | null => {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.hostname;
  } catch {
    return null;
  }
};

// Check if protocol supports real-time communication
export const supportsRealTime = (protocol: ProtocolType): boolean => {
  return [ProtocolType.WEBSOCKET, ProtocolType.GRPC].includes(protocol);
};

// Check if protocol requires authentication
export const requiresAuthentication = (protocol: ProtocolType): boolean => {
  return protocol !== ProtocolType.WEBSOCKET; // Most protocols require auth except basic WebSocket
};

// Get default port for protocol
export const getDefaultPort = (protocol: ProtocolType): number | null => {
  switch (protocol) {
    case ProtocolType.REST_API:
      return 443; // HTTPS
    case ProtocolType.WEBSOCKET:
      return 443; // WSS
    case ProtocolType.GRPC:
      return 443; // gRPC over TLS
    default:
      return null;
  }
};

// Generate endpoint suggestions based on protocol
export const generateEndpointSuggestions = (protocol: ProtocolType, domain: string): string[] => {
  const suggestions: string[] = [];
  
  switch (protocol) {
    case ProtocolType.REST_API:
      suggestions.push(
        `https://${domain}/api`,
        `https://${domain}/api/v1`,
        `https://${domain}/rest`
      );
      break;
    case ProtocolType.WEBSOCKET:
      suggestions.push(
        `wss://${domain}/ws`,
        `wss://${domain}/websocket`,
        `wss://${domain}/api/ws`
      );
      break;
    case ProtocolType.GRPC:
      suggestions.push(
        `grpc://${domain}:443`,
        `grpcs://${domain}:443`,
        `${domain}:9090`
      );
      break;
    case ProtocolType.MCP:
      suggestions.push(
        `https://${domain}/mcp`,
        `https://${domain}/api/mcp`
      );
      break;
    default:
      suggestions.push(`https://${domain}`);
  }
  
  return suggestions;
};

// Validate authentication configuration
export const validateAuthConfig = (authType: AuthenticationType, credentials: Record<string, string>): string[] => {
  const errors: string[] = [];
  
  switch (authType) {
    case AuthenticationType.API_KEY:
      if (!credentials.apiKey) {
        errors.push('API Key is required');
      }
      break;
    case AuthenticationType.BEARER_TOKEN:
      if (!credentials.token) {
        errors.push('Bearer token is required');
      }
      break;
    case AuthenticationType.BASIC_AUTH:
      if (!credentials.username || !credentials.password) {
        errors.push('Username and password are required');
      }
      break;
    case AuthenticationType.OAUTH2:
      if (!credentials.clientId || !credentials.clientSecret) {
        errors.push('Client ID and Client Secret are required');
      }
      break;
  }
  
  return errors;
};

// Format endpoint for display
export const formatEndpointForDisplay = (endpoint: string, maxLength = 50): string => {
  if (endpoint.length <= maxLength) {
    return endpoint;
  }
  
  try {
    const url = new URL(endpoint);
    const domain = url.hostname;
    const path = url.pathname;
    
    if (domain.length + path.length <= maxLength - 3) {
      return `${domain}${path}`;
    }
    
    return `${domain}...${path.slice(-20)}`;
  } catch {
    return endpoint.length > maxLength ? `${endpoint.slice(0, maxLength - 3)}...` : endpoint;
  }
};

// Check if agent is healthy based on status and last connection
export const isAgentHealthy = (status: ExternalAgentStatus, lastConnectedAt?: string): boolean => {
  if (status !== ExternalAgentStatus.ACTIVE) {
    return false;
  }
  
  if (!lastConnectedAt) {
    return false;
  }
  
  const lastConnection = new Date(lastConnectedAt);
  const now = new Date();
  const timeDiff = now.getTime() - lastConnection.getTime();
  const fiveMinutes = 5 * 60 * 1000;
  
  return timeDiff < fiveMinutes;
};
