/**
 * Enum cho trạng thái agent template
 */
export enum AgentTemplateStatusEnum {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DEPRECATED = 'DEPRECATED',
}

/**
 * Enum cho các trường sắp xếp
 */
export enum AgentTemplateSortBy {
  ID = 'id',
  NAME = 'name',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  employeeId: string;
  name: string;
  avatar: string | null;
  date?: Date;
}

/**
 * Interface cho agent template trong danh sách
 */
export interface AgentTemplateListItem {
  id: string;
  name: string;
  avatar: string | null;
  status: AgentTemplateStatusEnum;
  typeAgent: string;
  createdAt: number;
}

/**
 * Interface cho thông tin chi tiết agent template
 */
export interface AgentTemplateDetail {
  id: string;
  name: string;
  avatar: string | null;
  status: AgentTemplateStatusEnum;
  typeAgent: string;
  description?: string;
  instruction?: string;
  created?: EmployeeInfo;
  updated?: EmployeeInfo;
  deleted?: EmployeeInfo;
}

/**
 * Interface cho tham số tạo agent template
 */
export interface CreateAgentTemplateParams {
  name: string;
  typeAgentId: string;
  avatarMimeType?: string;
  description?: string;
  instruction?: string;
  status?: AgentTemplateStatusEnum;
}

/**
 * Interface cho tham số cập nhật agent template
 */
export interface UpdateAgentTemplateParams {
  name?: string;
  typeAgentId?: string;
  avatarMimeType?: string;
  description?: string;
  instruction?: string;
  status?: AgentTemplateStatusEnum;
}

/**
 * Interface cho tham số cập nhật trạng thái
 */
export interface UpdateAgentTemplateStatusParams {
  status: AgentTemplateStatusEnum;
}

/**
 * Interface cho tham số query
 */
export interface AgentTemplateQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: AgentTemplateStatusEnum;
  sortBy?: AgentTemplateSortBy;
  sortDirection?: SortDirection;
}

/**
 * Interface cho tham số khôi phục
 */
export interface RestoreAgentTemplateParams {
  ids: string[];
}

/**
 * Interface cho response tạo
 */
export interface CreateAgentTemplateResponse {
  avatarUrlUpload: string | null;
}

/**
 * Interface cho response cập nhật
 */
export interface UpdateAgentTemplateResponse {
  avatarUrlUpload?: string;
}

/**
 * Interface cho response khôi phục
 */
export interface RestoreAgentTemplateResponse {
  restoredCount: number;
}
