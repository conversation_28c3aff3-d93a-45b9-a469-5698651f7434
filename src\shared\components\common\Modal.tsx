import { ReactNode, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Button } from './index';
import { useTranslation } from 'react-i18next';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnClickOutside?: boolean;
  closeOnEsc?: boolean;
}

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  closeOnClickOutside = true,
  closeOnEsc = true,
}: ModalProps) => {
  const { t } = useTranslation();
  const modalRef = useRef<HTMLDivElement>(null);

  // Size classes
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
  };

  // Handle click outside
  const handleClickOutside = useCallback(
    (e: MouseEvent) => {
      if (closeOnClickOutside && modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    },
    [closeOnClickOutside, onClose]
  );

  // Handle escape key
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (closeOnEsc && e.key === 'Escape') {
        onClose();
      }
    },
    [closeOnEsc, onClose]
  );

  // Add event listeners
  useEffect(() => {
    if (isOpen) {
      // Tạo các hàm xử lý sự kiện trong useEffect để tránh re-render không cần thiết
      const currentHandleClickOutside = handleClickOutside;
      const currentHandleKeyDown = handleKeyDown;

      document.addEventListener('mousedown', currentHandleClickOutside);
      document.addEventListener('keydown', currentHandleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open

      return () => {
        document.removeEventListener('mousedown', currentHandleClickOutside);
        document.removeEventListener('keydown', currentHandleKeyDown);
        document.body.style.overflow = ''; // Restore scrolling when modal is closed
      };
    }
    return undefined;
  }, [isOpen, handleClickOutside, handleKeyDown]);

  // Don't render if not open
  if (!isOpen) return null;

  // Create portal to render modal at the end of the document body
  return createPortal(
    <div className="fixed inset-0 z-[9500] flex items-center justify-center p-4 bg-black bg-opacity-50 animate-fade-in">
      <div
        ref={modalRef}
        className={`bg-white dark:bg-dark-light rounded shadow-lg ${sizeClasses[size]} w-full animate-slide-in`}
      >
        {title && (
          <div className="flex items-center justify-between p-4">
            <h3 className="text-lg font-semibold">{title}</h3>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-dark-lighter"
              aria-label="Close"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        )}

        <div className="p-4">{children}</div>

        {footer ? (
          <div className="p-4">{footer}</div>
        ) : (
          <div className="flex justify-end p-4">
            <Button variant="outline" onClick={onClose} className="mr-2">
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button variant="primary" onClick={onClose}>
              {t('common.ok', 'OK')}
            </Button>
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default Modal;
