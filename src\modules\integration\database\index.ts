/**
 * Database Integration Module
 * 
 * This module provides database connection management functionality
 * following the EmailServerManagementPage pattern.
 */

// Export types
export * from './types';

// Export constants
export * from './constants';

// Export schemas
export * from './schemas';

// Export services
export * from './services';

// Export hooks
export * from './hooks';

// Export components
export { default as DatabaseConnectionForm } from './components/DatabaseConnectionForm';
export { default as DatabaseConnectionCard } from './components/DatabaseConnectionCard';
export { default as DatabaseConnectionList } from './components/DatabaseConnectionList';

/**
 * Database Integration Module Configuration
 */
export const DATABASE_INTEGRATION_CONFIG = {
  name: 'Database Integration',
  version: '1.0.0',
  description: 'Database connections integration and management',
  supportedDatabases: [
    'mysql',
    'postgresql', 
    'mongodb',
    'redis',
    'sqlite'
  ],
  routes: {
    main: '/integrations/database',
    connections: '/integrations/database/connections',
    settings: '/integrations/database/settings'
  }
} as const;
