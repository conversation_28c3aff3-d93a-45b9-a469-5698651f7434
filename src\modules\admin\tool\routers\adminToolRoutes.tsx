import { RouteObject } from 'react-router-dom';
import { Suspense } from 'react';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Loading } from '@/shared';
import { lazy } from 'react';
const ToolsPage = lazy(() => import('../pages/ToolsPage'));
const ToolManagementPage = lazy(() => import('../pages/ToolManagementPage'));
const TrashToolsPage = lazy(() => import('../pages/TrashToolsPage'));

/**
 * Routes cho module admin tool
 */
const adminToolRoutes: RouteObject[] = [
  // Trang tổng quan quản lý tools
  {
    path: '/admin/tools',
    element: (
      <AdminLayout title="Admin Tool Management">
        <Suspense fallback={<Loading />}>
          <ToolManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },

  // Trang quản lý danh sách tools
  {
    path: '/admin/tools/list',
    element: (
      <AdminLayout title="Admin Tools List">
        <Suspense fallback={<Loading />}>
          <ToolsPage />
        </Suspense>
      </AdminLayout>
    ),
  },

  // Trang thùng rác tools đã xóa mềm
  {
    path: '/admin/tools/trash',
    element: (
      <AdminLayout title="Admin Tools Trash">
        <Suspense fallback={<Loading />}>
          <TrashToolsPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default adminToolRoutes;
