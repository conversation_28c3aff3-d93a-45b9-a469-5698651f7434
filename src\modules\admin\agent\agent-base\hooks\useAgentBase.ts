import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminAgentBaseService } from '../services/agent-base.service';
import {
  AgentBaseDetail,
  CreateAgentBaseParams,
  UpdateAgentBaseParams,
  AgentBaseQueryParams,
} from '../types/agent-base.types';

// Query keys
export const ADMIN_AGENT_BASE_QUERY_KEYS = {
  all: ['admin', 'agent-base'] as const,
  lists: () => [...ADMIN_AGENT_BASE_QUERY_KEYS.all, 'list'] as const,
  list: (params: AgentBaseQueryParams) => [...ADMIN_AGENT_BASE_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_AGENT_BASE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_AGENT_BASE_QUERY_KEYS.details(), id] as const,
  trash: () => [...ADMIN_AGENT_BASE_QUERY_KEYS.all, 'trash'] as const,
  trashList: (params: AgentBaseQueryParams) =>
    [...ADMIN_AGENT_BASE_QUERY_KEYS.trash(), params] as const,
};

/**
 * Hook để lấy danh sách agent base
 */
export const useAdminAgentBases = (params: AgentBaseQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.list(params),
    queryFn: () => adminAgentBaseService.getAgentBases(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy danh sách agent base đã xóa
 */
export const useAdminAgentBasesTrash = (params: AgentBaseQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.trashList(params),
    queryFn: () => adminAgentBaseService.getDeletedAgentBases(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thông tin chi tiết agent base
 */
export const useAdminAgentBaseDetail = (id: string) => {
  return useQuery<AgentBaseDetail>({
    queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.detail(id),
    queryFn: () => adminAgentBaseService.getAgentBaseById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo agent base mới
 */
export const useCreateAdminAgentBase = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAgentBaseParams) => adminAgentBaseService.createAgentBase(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent base
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật agent base
 */
export const useUpdateAdminAgentBase = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentBaseParams }) =>
      adminAgentBaseService.updateAgentBase(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết agent base
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.detail(variables.id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa agent base
 */
export const useDeleteAdminAgentBase = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentBaseService.deleteAgentBase(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent base
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách trash
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.trash(),
      });
    },
  });
};

/**
 * Hook để đặt agent base thành active
 */
export const useSetAdminAgentBaseActive = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentBaseService.setAgentBaseActive(id),
    onSuccess: (_, id) => {
      // Invalidate và refetch chi tiết agent base
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.detail(id),
      });
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để khôi phục agent base đã xóa
 */
export const useRestoreAdminAgentBase = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentBaseService.restoreAgentBase(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách agent base
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.lists(),
      });
      // Invalidate danh sách trash
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_BASE_QUERY_KEYS.trash(),
      });
    },
  });
};
