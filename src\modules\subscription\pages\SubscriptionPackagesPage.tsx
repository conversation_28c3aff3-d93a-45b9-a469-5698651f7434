/**
 * Trang hiển thị danh sách các gói dịch vụ - Glassmorphism Design
 */
import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Typography, Button, Card, ResponsiveGrid } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { ServicePackageCard } from '../components';
import { useSubscriptionPackages } from '../hooks';
import { ServiceType, ServicePackage, SubscriptionDuration } from '../types';

/**
 * Trang hiển thị danh sách các gói dịch vụ - Glassmorphism Design
 */
const SubscriptionPackagesPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('subscription');
  const [selectedType, setSelectedType] = useState<ServiceType>(ServiceType.MAIN);

  // Sử dụng hook theme
  useTheme();

  // State cho bộ lọc thời hạn
  const [selectedDuration, setSelectedDuration] = useState<SubscriptionDuration>(
    SubscriptionDuration.MONTHLY
  );

  // Lấy dữ liệu gói dịch vụ
  const { packages } = useSubscriptionPackages(selectedType);

  // Hiển thị tất cả packages (không phân trang)
  const displayPackages = useMemo(() => {
    return packages;
  }, [packages]);

  // Xử lý khi chọn gói
  const handleSelectPackage = useCallback(
    (pkg: ServicePackage) => {
      console.log('Selected package:', pkg);
      // Chuyển hướng đến trang đơn hàng với thông tin gói
      navigate('/subscription/order', {
        state: {
          packageInfo: {
            id: pkg.id,
            name: pkg.name,
            type: pkg.type,
            price: pkg.prices[selectedDuration],
            duration: selectedDuration,
          },
        },
      });
    },
    [navigate, selectedDuration]
  );

  return (
    <div>
      <div className="relative mx-auto px-4 sm:px-6 lg:px-8">
        {/* Controls */}
        <div className="mb-12">
          <div className="relative">
            <Card className="relative backdrop-blur-lg bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/20 rounded-2xl p-6">
              <ResponsiveGrid maxColumns={{ xs: 1, md: 2 }} gap={6}>
                {/* Service Type Selector */}
                <div>
                  <Typography
                    variant="h6"
                    className="font-semibold mb-3 text-gray-900 dark:text-white"
                  >
                    {t('subscription:serviceType', 'Loại Dịch Vụ')}
                  </Typography>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant={selectedType === ServiceType.MAIN ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedType(ServiceType.MAIN)}
                      className="backdrop-blur-sm"
                    >
                      {t('subscription:types.main', 'Gói Chính')}
                    </Button>
                    <Button
                      variant={selectedType === ServiceType.FEATURE ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedType(ServiceType.FEATURE)}
                      className="backdrop-blur-sm"
                    >
                      {t('subscription:types.feature', 'Tính Năng')}
                    </Button>
                  </div>
                </div>

                {/* Duration Selector */}
                <div>
                  <Typography
                    variant="h6"
                    className="font-semibold mb-3 text-gray-900 dark:text-white"
                  >
                    {t('subscription:billingPeriod', 'Chu Kỳ Thanh Toán')}
                  </Typography>
                  <div className="flex flex-wrap gap-2">
                    {Object.values(SubscriptionDuration).map(duration => (
                      <Button
                        key={duration}
                        variant={selectedDuration === duration ? 'primary' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedDuration(duration)}
                        className="backdrop-blur-sm"
                      >
                        {duration === SubscriptionDuration.MONTHLY &&
                          t('subscription:duration.monthly', 'Hàng tháng')}
                        {duration === SubscriptionDuration.SEMI_ANNUAL &&
                          t('subscription:duration.semi_annual', '6 tháng')}
                        {duration === SubscriptionDuration.ANNUAL &&
                          t('subscription:duration.annual', 'Hàng năm')}
                      </Button>
                    ))}
                  </div>
                </div>
              </ResponsiveGrid>
            </Card>
          </div>
        </div>

        {/* Cards Grid */}
        <div className="mb-12">
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
            gap={8}
            className="items-stretch"
          >
            {displayPackages.map(pkg => (
              <div key={pkg.id} className="flex" style={{ minHeight: '600px' }}>
                <ServicePackageCard
                  package={pkg}
                  duration={selectedDuration}
                  onSelect={handleSelectPackage}
                  cardType="glassmorphism"
                  className="w-full"
                />
              </div>
            ))}
          </ResponsiveGrid>
        </div>

        {/* Empty State */}
        {displayPackages.length === 0 && (
          <div className="relative">
            {/* Background Gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 via-purple-400/20 to-pink-400/20 rounded-2xl blur-xl"></div>

            <Card className="relative backdrop-blur-lg bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/20 rounded-2xl p-8 text-center">
              <Typography variant="h4" className="text-gray-900 dark:text-white mb-2">
                {t('subscription:noResults', 'Không tìm thấy gói dịch vụ nào')}
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                {t('subscription:tryDifferentSearch', 'Vui lòng thử tìm kiếm khác')}
              </Typography>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionPackagesPage;
