// Đị<PERSON> nghĩa các định dạng file được hỗ trợ
export const SUPPORTED_FILE_TYPES = {
  DOC: 'application/msword',
  DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  JSON: 'application/json',
  PDF: 'application/pdf',
  PPTX: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  HTML: 'text/html',
  TXT: 'text/plain',
} as const;

// Tạo chuỗi accept cho input file
export const DOCUMENT_ACCEPT_TYPES = Object.values(SUPPORTED_FILE_TYPES).join(', ');

// Tạo mảng các định dạng file được hỗ trợ cho hiển thị
export const SUPPORTED_FILE_EXTENSIONS = [
  'PDF', 'DOC', 'DOCX', 'TXT', 'HTML', 'JSON', 'PPTX'
];

// Tạo chuỗi hiển thị các định dạng được hỗ trợ
export const SUPPORTED_FORMATS_TEXT = SUPPORTED_FILE_EXTENSIONS.join(', ');
