/**
 * Types cho Business Report Module
 */

/**
 * Enum cho các khoảng thời gian báo cáo
 */
export enum ReportPeriodEnum {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

/**
 * DTO cho query parameters của API tổng quan báo cáo
 */
export interface ReportOverviewQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
}

/**
 * DTO cho dữ liệu so sánh với kỳ trước
 */
export interface PreviousPeriodDataDto {
  totalRevenue: number;
  totalOrders: number;
  newCustomers: number;
  revenueGrowth: number;
  ordersGrowth: number;
  customersGrowth: number;
}

/**
 * DTO cho response API tổng quan báo cáo
 */
export interface ReportOverviewResponseDto {
  totalRevenue: number;
  totalOrders: number;
  newCustomers: number;
  period: string;
  startDate: string;
  endDate: string;
  previousPeriod?: PreviousPeriodDataDto;
}

/**
 * DTO cho query parameters của API biểu đồ doanh thu
 */
export interface SalesChartQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

/**
 * DTO cho dữ liệu điểm biểu đồ doanh thu
 */
export interface SalesChartDataPoint {
  period: string;
  revenue: number;
  orders: number;
  date: string;
}

/**
 * DTO cho response API biểu đồ doanh thu
 */
export interface SalesChartResponseDto {
  data: SalesChartDataPoint[];
  totalRevenue: number;
  totalOrders: number;
  period: string;
  groupBy: string;
}

/**
 * DTO cho query parameters của API biểu đồ đơn hàng
 */
export interface OrdersChartQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  status?: string[];
}

/**
 * DTO cho dữ liệu điểm biểu đồ đơn hàng
 */
export interface OrdersChartDataPoint {
  period: string;
  pending: number;
  processing: number;
  shipped: number;
  delivered: number;
  cancelled: number;
  total: number;
  date: string;
}

/**
 * DTO cho response API biểu đồ đơn hàng
 */
export interface OrdersChartResponseDto {
  data: OrdersChartDataPoint[];
  totalOrders: number;
  period: string;
  groupBy: string;
}

/**
 * DTO cho query parameters của API biểu đồ khách hàng
 */
export interface CustomersChartQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

/**
 * DTO cho dữ liệu điểm biểu đồ khách hàng
 */
export interface CustomersChartDataPoint {
  period: string;
  newCustomers: number;
  totalCustomers: number;
  date: string;
}

/**
 * DTO cho response API biểu đồ khách hàng
 */
export interface CustomersChartResponseDto {
  data: CustomersChartDataPoint[];
  totalNewCustomers: number;
  totalCustomers: number;
  period: string;
  groupBy: string;
}

/**
 * DTO cho query parameters của API biểu đồ sản phẩm
 */
export interface ProductsChartQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  limit?: number;
}

/**
 * DTO cho dữ liệu điểm biểu đồ sản phẩm
 */
export interface ProductsChartDataPoint {
  period: string;
  totalProducts: number;
  newProducts: number;
  soldProducts: number;
  date: string;
}

/**
 * DTO cho response API biểu đồ sản phẩm
 */
export interface ProductsChartResponseDto {
  data: ProductsChartDataPoint[];
  totalProducts: number;
  newProducts: number;
  soldProducts: number;
  period: string;
  groupBy: string;
}

/**
 * DTO cho query parameters của API sản phẩm bán chạy
 */
export interface TopSellingProductsQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
  limit?: number;
  sortBy?: 'revenue' | 'quantity';
}

/**
 * DTO cho dữ liệu sản phẩm bán chạy
 */
export interface TopSellingProductDto {
  id: number;
  name: string;
  sku: string;
  image?: string;
  revenue: number;
  quantity: number;
  orders: number;
  rank: number;
}

/**
 * DTO cho response API sản phẩm bán chạy
 */
export interface TopSellingProductsResponseDto {
  products: TopSellingProductDto[];
  totalRevenue: number;
  totalQuantity: number;
  period: string;
}

/**
 * DTO cho query parameters của API khách hàng tiềm năng
 */
export interface PotentialCustomersQueryDto {
  startDate?: string;
  endDate?: string;
  period?: ReportPeriodEnum;
  limit?: number;
  minScore?: number;
}

/**
 * DTO cho dữ liệu khách hàng tiềm năng
 */
export interface PotentialCustomerDto {
  id: number;
  name: string;
  email: string;
  phone?: string;
  totalOrders: number;
  totalRevenue: number;
  lastOrderDate?: string;
  potentialScore: number;
  rank: number;
}

/**
 * DTO cho response API khách hàng tiềm năng
 */
export interface PotentialCustomersResponseDto {
  customers: PotentialCustomerDto[];
  totalCustomers: number;
  averageScore: number;
  period: string;
}
