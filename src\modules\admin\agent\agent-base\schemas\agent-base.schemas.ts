import { z } from 'zod';
import { AgentStatusEnum, AgentBaseSortBy, SortDirection } from '../types/agent-base.types';

/**
 * Schema cho c<PERSON>u h<PERSON>nh model AI
 */
export const modelConfigSchema = z.object({
  model_id: z.string().optional().nullable(),
  type_provider: z.string().optional().nullable(),
  temperature: z.number().min(0).max(2).optional(),
  max_tokens: z.number().min(1).optional(),
  top_p: z.number().min(0).max(1).optional(),
  frequency_penalty: z.number().min(-2).max(2).optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),
});

/**
 * Schema cho tạo agent base
 */
export const createAgentBaseSchema = z.object({
  name: z.string().min(1, 'Tên agent l<PERSON> bắt buộ<PERSON>').max(255, 'Tên agent không đượ<PERSON> quá 255 ký tự'),
  avatarMimeType: z.string().optional(),
  modelConfig: modelConfigSchema,
  instruction: z.string().optional().nullable(),
  vectorStoreId: z.string().optional(),
  active: z.boolean().optional().default(false),
  model_base_id: z.string().optional(),
  model_finetuning_id: z.string().optional(),
}).refine(
  (data) => data.model_base_id || data.model_finetuning_id,
  {
    message: 'Phải có ít nhất một trong hai: model_base_id hoặc model_finetuning_id',
    path: ['model_base_id'],
  }
);

/**
 * Schema cho cập nhật agent base
 */
export const updateAgentBaseSchema = z.object({
  name: z.string().min(1, 'Tên agent là bắt buộc').max(255, 'Tên agent không được quá 255 ký tự').optional(),
  avatarMimeType: z.string().optional(),
  modelConfig: modelConfigSchema.optional(),
  instruction: z.string().optional().nullable(),
  vectorStoreId: z.string().optional(),
  active: z.boolean().optional(),
  model_base_id: z.string().optional(),
  model_finetuning_id: z.string().optional(),
});

/**
 * Schema cho query agent base
 */
export const agentBaseQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  active: z.boolean().optional(),
  sortBy: z.nativeEnum(AgentBaseSortBy).optional().default(AgentBaseSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema cho thông tin nhân viên
 */
export const employeeInfoSchema = z.object({
  employeeId: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  date: z.date().optional(),
});

/**
 * Schema cho thông tin vector store
 */
export const vectorStoreSchema = z.object({
  vectorStoreId: z.string(),
  vectorStoreName: z.string(),
});

/**
 * Schema cho thông tin model
 */
export const modelInfoSchema = z.object({
  model_base_id: z.string().optional().nullable(),
  model_finetuning_id: z.string().optional().nullable(),
  model_id: z.string().optional().nullable(),
  typeProvider: z.string().optional().nullable(),
});

/**
 * Schema cho quan hệ multi-agent
 */
export const multiAgentRelationSchema = z.object({
  id: z.string(),
  relatedAgentId: z.string(),
  relatedAgentName: z.string(),
  relationType: z.string(),
});

/**
 * Schema cho agent base list item
 */
export const agentBaseListItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  model: z.string(),
  model_id: z.string().optional().nullable(),
  type_provider: z.string().optional().nullable(),
  status: z.nativeEnum(AgentStatusEnum),
  createdAt: z.number(),
  active: z.boolean(),
});

/**
 * Schema cho agent base detail
 */
export const agentBaseDetailSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  modelConfig: modelConfigSchema,
  model: modelInfoSchema.optional(),
  instruction: z.string().nullable(),
  vector: vectorStoreSchema.optional().nullable(),
  status: z.nativeEnum(AgentStatusEnum),
  active: z.boolean(),
  created: employeeInfoSchema.optional(),
  updated: employeeInfoSchema.optional(),
  avatarUrlUpload: z.string().optional(),
  multiAgentRelations: z.array(multiAgentRelationSchema).optional(),
});

/**
 * Schema cho agent base trash item
 */
export const agentBaseTrashItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  model: z.string(),
  model_id: z.string().optional().nullable(),
  type_provider: z.string().optional().nullable(),
  status: z.nativeEnum(AgentStatusEnum),
  deletedAt: z.number().optional(),
  deleted: employeeInfoSchema.optional(),
});

/**
 * Schema cho response tạo agent base
 */
export const createAgentBaseResponseSchema = z.object({
  avatarUrlUpload: z.string().nullable(),
});

/**
 * Schema cho response cập nhật agent base
 */
export const updateAgentBaseResponseSchema = z.object({
  avatarUrlUpload: z.string().nullable(),
});

// Export types từ schemas
export type CreateAgentBaseFormData = z.infer<typeof createAgentBaseSchema>;
export type UpdateAgentBaseFormData = z.infer<typeof updateAgentBaseSchema>;
export type AgentBaseQueryFormData = z.infer<typeof agentBaseQuerySchema>;
