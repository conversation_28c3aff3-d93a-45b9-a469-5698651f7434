/**
 * Google Calendar Integration Types
 */

import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Google Calendar Configuration
 */
export interface GoogleCalendarConfiguration {
  id: number;
  userId: number;
  accountName: string;
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  accessToken?: string;
  calendarId?: string;
  isActive: boolean;
  syncEnabled: boolean;
  lastSyncAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create Google Calendar DTO
 */
export interface CreateGoogleCalendarDto {
  accountName: string;
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  calendarId?: string;
  isActive: boolean;
  syncEnabled: boolean;
}

/**
 * Update Google Calendar DTO
 */
export interface UpdateGoogleCalendarDto {
  accountName?: string;
  clientId?: string;
  clientSecret?: string;
  refreshToken?: string;
  calendarId?: string;
  isActive?: boolean;
  syncEnabled?: boolean;
}

/**
 * Google Calendar Test DTO
 */
export interface TestGoogleCalendarDto {
  testEventTitle?: string;
  testEventDescription?: string;
}

/**
 * Google Calendar Config for testing
 */
export interface GoogleCalendarConfigDto {
  accountName: string;
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  calendarId?: string;
}

/**
 * Test Google Calendar with config DTO
 */
export interface TestGoogleCalendarWithConfigDto {
  calendarConfig: GoogleCalendarConfigDto;
  testInfo: TestGoogleCalendarDto;
}

/**
 * Query parameters for Google Calendar
 */
export interface GoogleCalendarQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  isActive?: boolean;
  syncEnabled?: boolean;
}

/**
 * Google Calendar test result
 */
export interface GoogleCalendarTestResult {
  success: boolean;
  message: string;
  details?: {
    error?: string;
    statusCode?: number;
    responseTime?: number;
    calendarInfo?: {
      id: string;
      summary: string;
      timeZone: string;
    };
  };
}

/**
 * Form data for Google Calendar
 */
export interface GoogleCalendarFormData extends Omit<CreateGoogleCalendarDto, 'isActive' | 'syncEnabled'> {
  isActive: boolean;
  syncEnabled: boolean;
}

/**
 * Google Calendar Event
 */
export interface GoogleCalendarEvent {
  id: string;
  summary: string;
  description?: string;
  start: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  end: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  location?: string;
  attendees?: Array<{
    email: string;
    displayName?: string;
    responseStatus?: 'needsAction' | 'declined' | 'tentative' | 'accepted';
  }>;
  status: 'confirmed' | 'tentative' | 'cancelled';
  created: string;
  updated: string;
}

/**
 * Calendar sync status
 */
export interface CalendarSyncStatus {
  configId: number;
  lastSyncAt?: string;
  syncInProgress: boolean;
  eventsCount: number;
  errorMessage?: string;
}
