
import { useTranslation } from 'react-i18next';
import { Users, Send, Mail, MousePointer } from 'lucide-react';
import { ResponsiveGrid, Card, Typography } from '@/shared/components/common';

/**
 * Interface cho dữ liệu analytics stats
 */
export interface CampaignAnalyticsStats {
  /**
   * Tổng số người nhận
   */
  totalRecipients: number;
  
  /**
   * Số lượng đã gửi thành công
   */
  deliveredCount: number;
  
  /**
   * Số lượng đã mở
   */
  openedCount: number;
  
  /**
   * Số lượng đã click
   */
  clickedCount: number;
}

/**
 * Props cho CampaignAnalyticsStatsGrid
 */
interface CampaignAnalyticsStatsGridProps {
  /**
   * Dữ liệu analytics
   */
  stats: CampaignAnalyticsStats;
  
  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
  
  /**
   * Namespace cho translation keys
   */
  translationNamespace?: string;
  
  /**
   * <PERSON><PERSON> cột tối đa cho mỗi breakpoint khi chatpanel đóng
   * @default { xs: 2, sm: 2, md: 4, lg: 4 }
   */
  maxColumns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Số cột tối đa cho mỗi breakpoint khi chatpanel mở
   * @default { xs: 2, sm: 2, md: 2, lg: 4, xl: 4 }
   */
  maxColumnsWithChatPanel?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Class name bổ sung
   */
  className?: string;
}

/**
 * Component grid hiển thị thống kê analytics campaign theo quy tắc RedAI
 */
export function CampaignAnalyticsStatsGrid({
  stats,
  isLoading = false,
  translationNamespace = 'marketing',
  maxColumns = { xs: 2, sm: 2, md: 4, lg: 4 },
  maxColumnsWithChatPanel = { xs: 2, sm: 2, md: 2, lg: 4, xl: 4 },
  className = '',
}: CampaignAnalyticsStatsGridProps) {
  const { t } = useTranslation([translationNamespace, 'common']);

  const statsItems = [
    {
      icon: Users,
      value: stats.totalRecipients,
      label: t(`${translationNamespace}:email.campaigns.analytics.totalRecipients`, 'Tổng người nhận'),
      color: 'text-blue-600',
    },
    {
      icon: Send,
      value: stats.deliveredCount,
      label: t(`${translationNamespace}:email.campaigns.analytics.delivered`, 'Đã gửi'),
      color: 'text-green-600',
    },
    {
      icon: Mail,
      value: stats.openedCount,
      label: t(`${translationNamespace}:email.campaigns.analytics.opened`, 'Đã mở'),
      color: 'text-orange-600',
    },
    {
      icon: MousePointer,
      value: stats.clickedCount,
      label: t(`${translationNamespace}:email.campaigns.analytics.clicked`, 'Đã click'),
      color: 'text-purple-600',
    },
  ];

  if (isLoading) {
    return (
      <ResponsiveGrid
        maxColumns={maxColumns}
        maxColumnsWithChatPanel={maxColumnsWithChatPanel}
        className={className}
      >
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index} className="p-4 text-center animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </Card>
        ))}
      </ResponsiveGrid>
    );
  }

  return (
    <ResponsiveGrid
      maxColumns={maxColumns}
      maxColumnsWithChatPanel={maxColumnsWithChatPanel}
      className={className}
    >
      {statsItems.map((item, index) => {
        return (
          <Card key={index} className="p-4 text-center">
            <Typography variant="h4" className={item.color}>
              {item.value.toLocaleString()}
            </Typography>
            <Typography variant="caption" color="muted">
              {item.label}
            </Typography>
          </Card>
        );
      })}
    </ResponsiveGrid>
  );
}

export default CampaignAnalyticsStatsGrid;
