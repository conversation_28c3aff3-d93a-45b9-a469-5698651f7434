{"data": {"title": "Data Management", "description": "Centralized management of system data including media, knowledge files, URLs, and tools.", "media": {"title": "Media Library", "description": "Manage media files such as images, videos, audio, and documents.", "descriptionDetail": "Description", "totalFiles": "Total files", "manage": "Manage Media", "form": {"title": "Media Details", "createTitle": "Add New Media", "editTitle": "Edit Media", "name": "File name", "description": "Description", "type": "Type", "tags": "Tags", "status": "Status"}, "detail": {"title": "Media Details", "name": "File name", "description": "Description", "type": "Type", "tags": "Tags", "status": "Status", "ownedBy": "Owned by", "uploadDate": "Upload date", "viewUrl": "View URL"}, "actions": {"view": "View", "delete": "Delete"}, "table": {"name": "File name", "type": "File type", "size": "Size", "viewUrl": "View URL", "status": "Status", "ownedBy": "Owned by", "description": "Description", "uploadDate": "Upload date", "actions": "Actions", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>"}, "upload": {"title": "Upload Media", "button": "Upload", "progress": "Uploading... {{progress}}%", "success": "Upload successful", "error": "Upload failed"}, "delete": {"confirm": "Are you sure you want to delete this file?", "success": "File deleted successfully", "error": "Failed to delete file"}}, "knowledgeFiles": {"title": "Knowledge Files", "description": "Manage knowledge files used for AI and vector stores.", "totalFiles": "Total files", "manage": "Manage Knowledge Files", "uploadFiles": "Upload knowledge files", "selectFilesToUpload": "Select files to upload", "dragAndDrop": "Drag and drop files here or click to upload", "supportedFormats": "Supported formats: PDF, DOCX, TXT, CSV, JSON", "table": {"name": "File name", "extension": "Format", "size": "Size", "viewUrl": "View URL", "actions": "Actions", "url": "URL"}, "upload": {"title": "Upload Knowledge Files", "button": "Upload", "progress": "Uploading... {{progress}}%", "success": "Upload successful", "error": "Upload failed"}, "delete": {"confirm": "Are you sure you want to delete this file?", "success": "File deleted successfully", "error": "Failed to delete file"}}, "files": {"form": {"files": "Files", "dragAndDrop": "Drag and drop files here or click to upload", "fileDetails": "File Details", "fileDetailsDescription": "Edit information for each file", "name": "Document Name", "description": "Description", "tags": "Tags", "tagsPlaceholder": "Enter tag and press Enter", "invalidFile": "Invalid file", "nameRequired": "Document name is required", "descriptionRequired": "Description is required", "fileRequired": "Please select at least one file", "selectedFiles": "Selected files", "descriptionPlaceholder": "Enter description"}}, "url": {"title": "URL Management", "description": "Manage URLs and web resources used in the system.", "totalUrls": "Total URLs", "manage": "Manage URLs", "viewUrl": "URL Details", "editUrl": "Edit URL", "addNew": "Add New URL", "table": {"url": "URL", "title": "Title", "type": "Type", "tags": "Tags", "description": "Description", "createdAt": "Created date", "actions": "Actions"}, "form": {"url": "URL", "urlDescription": "Enter full URL including http:// or https://", "title": "Title", "description": "Description", "type": "Type", "tags": "Tags", "tagsDescription": "Enter tags separated by commas", "tagsPlaceholder": "Enter tag and press Enter", "urlPlaceholder": "https://example.com", "titlePlaceholder": "Enter title", "descriptionPlaceholder": "Enter description", "typePlaceholder": "Select URL type", "ownedBy": "Owned by", "isActive": "Is Active", "depth": "De<PERSON><PERSON>", "maxUrls": "Max URLs", "ignoreRobotsTxt": "Ignore robots.txt", "startCrawl": "Start Crawl", "activeStatus": "Active Status", "ownedByPlaceholder": "Enter owner ID"}, "add": {"title": "Add New URL", "button": "Add URL", "success": "URL added successfully", "error": "Failed to add URL"}, "delete": {"confirm": "Are you sure you want to delete this URL?", "success": "URL deleted successfully", "error": "Failed to delete URL"}}, "common": {"actions": "Actions", "createdAt": "Created Date", "cancel": "Cancel", "save": "Save", "delete": "Delete", "confirmDelete": "Confirm Delete", "all": "All", "search": "Search...", "noData": "No data available", "loading": "Loading...", "error": "An error occurred", "upload": "Upload", "uploading": "Uploading...", "close": "Close", "copy": "Copy", "start": "Start"}, "vectorStore": {"title": "Vector Store", "description": "Manage vector stores and embeddings for AI applications and semantic search.", "totalStores": "Total vector stores", "manage": "Manage Vector Store", "assignFiles": "Assign Files", "confirmDeleteMessage": "Are you sure you want to delete this Vector Store?", "form": {"title": "Create New Vector Store", "name": "Vector Store Name", "assignFiles": "Assign Files to Vector Store", "fileIds": "File IDs (comma separated)", "namePlaceholder": "Enter vector store name"}, "table": {"name": "Vector Store Name", "files": "Files Count", "size": "Size", "agents": "Agents Count"}}}}