import { useState, useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import { Icon } from '@/shared/components/common';
import SelectOption from './SelectOption';
import { SelectOption as SelectOptionType } from './Select';
import { debounce } from 'lodash';

export interface AsyncSelectProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | string[] | number | number[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | string[] | number | number[]) => void;

  /**
   * Hàm load options từ API
   */
  loadOptions: (inputValue: string) => Promise<SelectOptionType[]>;

  /**
   * Thời gian debounce (ms)
   */
  debounceTime?: number;

  /**
   * Cho phép chọn nhiều
   */
  multiple?: boolean;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Custom rendering
   */
  renderOption?: (option: SelectOptionType) => React.ReactNode;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * Kích thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;

  /**
   * Số lượng options tối đa hiển thị
   */
  maxOptions?: number;

  /**
   * Hiển thị thông báo khi không có kết quả
   */
  noOptionsMessage?: string;

  /**
   * Hiển thị thông báo khi đang tải
   */
  loadingMessage?: string;
}

/**
 * Component AsyncSelect - Select với khả năng tải dữ liệu từ API
 */
const AsyncSelect = forwardRef<HTMLInputElement, AsyncSelectProps>(
  (
    {
      value,
      onChange,
      loadOptions,
      debounceTime = 300,
      multiple = false,
      placeholder = '',
      label,
      disabled = false,
      renderOption,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      maxOptions = 100,
      noOptionsMessage,
      loadingMessage,
    },
    ref
  ) => {
    const { t } = useTranslation();
    useTheme(); // Keep the hook call to avoid React hooks rules violation
    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [options, setOptions] = useState<SelectOptionType[]>([]);
    const [loading, setLoading] = useState(false);
    const [selectedValues, setSelectedValues] = useState<(string | number)[]>(
      multiple
        ? Array.isArray(value)
          ? (value as (string | number)[])
          : []
        : value !== undefined
          ? [value as string | number]
          : []
    );
    const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number; width: number } | null>(null);

    const selectRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Calculate dropdown position when opening
    const calculateDropdownPosition = useCallback(() => {
      if (!selectRef.current) return null;

      const rect = selectRef.current.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      return {
        top: rect.bottom + scrollTop + 4, // 4px gap
        left: rect.left + scrollLeft,
        width: rect.width,
      };
    }, []);

    // Update selectedValues when value prop changes
    useEffect(() => {
      if (multiple) {
        setSelectedValues(Array.isArray(value) ? (value as (string | number)[]) : []);
      } else {
        setSelectedValues(value !== undefined ? [value as string | number] : []);
      }
    }, [value, multiple]);

    // Close dropdown when clicking outside and handle scroll
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node;

        // Check if click is outside both select trigger and dropdown
        const isOutsideSelect = selectRef.current && !selectRef.current.contains(target);
        const isOutsideDropdown = !document.querySelector('.async-select-dropdown')?.contains(target);

        if (isOutsideSelect && isOutsideDropdown) {
          setIsOpen(false);
        }
      };

      const handleScroll = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      const handleResize = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleResize);
      };
    }, [isOpen, calculateDropdownPosition]);

    // Focus input when dropdown opens and calculate position
    useEffect(() => {
      if (isOpen) {
        if (inputRef.current) {
          inputRef.current.focus();
        }
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
      } else {
        setDropdownPosition(null);
      }
    }, [isOpen, calculateDropdownPosition]);

    // Debounced function to load options
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedLoadOptions = useCallback(
      debounce(async (input: string) => {
        if (!input) {
          setOptions([]);
          setLoading(false);
          return;
        }

        try {
          const result = await loadOptions(input);
          setOptions(result.slice(0, maxOptions));
        } catch (error) {
          console.error('Error loading options:', error);
          setOptions([]);
        } finally {
          setLoading(false);
        }
      }, debounceTime),
      [loadOptions, maxOptions, debounceTime]
    );

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setInputValue(value);
      setLoading(true);
      debouncedLoadOptions(value);
    };

    // Handle option click
    const handleOptionClick = (optionValue: string | number) => {
      let newSelectedValues: (string | number)[];

      if (multiple) {
        // Toggle selection for multiple select
        if (selectedValues.includes(optionValue)) {
          newSelectedValues = selectedValues.filter(val => val !== optionValue);
        } else {
          newSelectedValues = [...selectedValues, optionValue];
        }
      } else {
        // Single select
        newSelectedValues = [optionValue];
        setIsOpen(false); // Close dropdown for single select
      }

      setSelectedValues(newSelectedValues);

      // Call onChange with the new value(s)
      if (onChange) {
        if (multiple) {
          onChange(newSelectedValues as string[] | number[]);
        } else {
          onChange(newSelectedValues[0]);
        }
      }

      // Clear input value when option is selected
      setInputValue('');
    };

    // Get display value
    const getDisplayValue = () => {
      if (selectedValues.length === 0) return placeholder;

      if (multiple) {
        if (selectedValues.length === 1) {
          const selectedOption = options.find(opt => opt.value === selectedValues[0]);
          return selectedOption ? selectedOption.label : '';
        } else {
          return t('common.selected', { count: selectedValues.length });
        }
      } else {
        const selectedOption = options.find(opt => opt.value === selectedValues[0]);
        return selectedOption ? selectedOption.label : '';
      }
    };

    // Render single option
    const renderSingleOption = (option: SelectOptionType) => {
      const isSelected = selectedValues.includes(option.value);

      if (renderOption) {
        return (
          <div
            key={`option-${option.value}`}
            onClick={() => !option.disabled && handleOptionClick(option.value)}
            className={`${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            {renderOption(option)}
          </div>
        );
      }

      return (
        <SelectOption
          key={`option-${option.value}`}
          value={option.value}
          label={option.label}
          icon={option.icon}
          disabled={option.disabled}
          selected={isSelected}
          onClick={() => handleOptionClick(option.value)}
          data={option.data}
        />
      );
    };

    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={multiple ? selectedValues.join(',') : selectedValues[0] || ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
          flex items-center justify-between px-3
          border-0 rounded-md bg-card-muted text-foreground
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'ring-1 ring-error' : ''}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex-grow truncate">{getDisplayValue()}</div>

          <div className="flex items-center">
            {loading ? (
              <Icon name="loading" className="animate-spin" size="sm" />
            ) : (
              <svg
                className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{helperText}</p>
        )}

        {/* Dropdown Portal */}
        {isOpen && dropdownPosition && createPortal(
          <div
            className="async-select-dropdown fixed z-[99999] bg-card rounded-md shadow-lg max-h-60 overflow-auto animate-fade-in border-0"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
            }}
          >
            {/* Search input */}
            <div className="sticky top-0 p-2 bg-card border-b-0">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                placeholder={t('common.search', 'Search...')}
                className="w-full px-3 py-1 text-sm border-0 rounded-md focus:outline-none focus:ring-1 focus:ring-primary/20 bg-card-muted text-foreground"
                onClick={e => e.stopPropagation()}
              />
            </div>

            {/* Options */}
            <div role="listbox" aria-multiselectable={multiple}>
              {loading ? (
                <div className="px-4 py-2 text-sm text-muted flex items-center">
                  <Icon name="loading" className="animate-spin mr-2" size="sm" />
                  {loadingMessage || t('common.loading', 'Loading...')}
                </div>
              ) : options.length > 0 ? (
                options.map(option => renderSingleOption(option))
              ) : (
                <div className="px-4 py-2 text-sm text-muted">
                  {noOptionsMessage || t('common.noResults', 'No results found')}
                </div>
              )}
            </div>
          </div>,
          document.body
        )}
      </div>
    );
  }
);

AsyncSelect.displayName = 'AsyncSelect';

export default AsyncSelect;
