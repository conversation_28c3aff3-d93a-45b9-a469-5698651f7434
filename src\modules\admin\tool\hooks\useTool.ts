import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AdminToolService } from '../services/tool.service';
import {
  ToolQueryParams,
  ToolDetail,
  ToolListItem,
  CreateToolParams,
  UpdateToolParams,
  PaginatedResult,
} from '../types/tool.types';

// Khởi tạo service
const toolService = new AdminToolService();

// Các key cho React Query
export const ADMIN_TOOL_QUERY_KEYS = {
  all: ['admin', 'tools'] as const,
  lists: () => [...ADMIN_TOOL_QUERY_KEYS.all, 'list'] as const,
  list: (params: ToolQueryParams) => [...ADMIN_TOOL_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_TOOL_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_TOOL_QUERY_KEYS.details(), id] as const,
  trash: () => [...ADMIN_TOOL_QUERY_KEYS.all, 'trash'] as const,
  trashList: (params: ToolQueryParams) => [...ADMIN_TOOL_QUERY_KEYS.trash(), params] as const,
};

/**
 * Hook để lấy danh sách tool
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useAdminTools = (params: ToolQueryParams = {}) => {
  return useQuery<PaginatedResult<ToolListItem>>({
    queryKey: ADMIN_TOOL_QUERY_KEYS.list(params),
    queryFn: () => toolService.getTools(params),
  });
};

/**
 * Hook để lấy thông tin chi tiết tool
 * @param id ID của tool
 * @returns Query object
 */
export const useAdminToolDetail = (id: string) => {
  return useQuery<ToolDetail>({
    queryKey: ADMIN_TOOL_QUERY_KEYS.detail(id),
    queryFn: () => toolService.getToolById(id),
    enabled: !!id,
  });
};

/**
 * Hook để tạo tool mới
 * @returns Mutation object
 */
export const useCreateAdminTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateToolParams) => toolService.createTool(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật thông tin tool
 * @returns Mutation object
 */
export const useUpdateAdminTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateToolParams }) =>
      toolService.updateTool(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết tool và danh sách tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa tool
 * @returns Mutation object
 */
export const useDeleteAdminTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => toolService.deleteTool(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để đặt phiên bản mặc định cho tool
 * @returns Mutation object
 */
export const useSetDefaultVersion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ toolId, versionId }: { toolId: string; versionId: string }) =>
      toolService.setDefaultVersion(toolId, versionId),
    onSuccess: (_, variables) => {
      // Invalidate và refetch chi tiết tool
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.detail(variables.toolId),
      });
    },
  });
};

/**
 * Hook để lấy danh sách tool đã xóa mềm
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useDeletedAdminTools = (params: ToolQueryParams = {}) => {
  return useQuery<PaginatedResult<ToolListItem>>({
    queryKey: ADMIN_TOOL_QUERY_KEYS.trashList(params),
    queryFn: () => toolService.getDeletedTools(params),
  });
};

/**
 * Hook để khôi phục tool đã xóa mềm
 * @returns Mutation object
 */
export const useRollbackAdminTool = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => toolService.rollbackTool(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách tool đã xóa và danh sách tool chính
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.trash(),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_TOOL_QUERY_KEYS.lists(),
      });
    },
  });
};
