import { z } from 'zod';

/**
 * Cloud Storage Integration Schemas
 */

export const cloudStorageProviderConfigurationSchema = z.object({
  providerType: z.enum(['google-drive', 'onedrive', 'dropbox', 'box'], {
    errorMap: () => ({ message: 'integration:cloudStorage.validation.providerType.invalid' }),
  }),

  providerName: z
    .string()
    .min(1, 'integration:cloudStorage.validation.providerName.required')
    .max(100, 'integration:cloudStorage.validation.providerName.maxLength'),

  clientId: z
    .string()
    .min(1, 'integration:cloudStorage.validation.clientId.required')
    .max(255, 'integration:cloudStorage.validation.clientId.maxLength'),

  clientSecret: z
    .string()
    .min(1, 'integration:cloudStorage.validation.clientSecret.required')
    .max(255, 'integration:cloudStorage.validation.clientSecret.maxLength'),

  refreshToken: z
    .string()
    .min(1, 'integration:cloudStorage.validation.refreshToken.required'),

  rootFolderId: z
    .string()
    .optional(),

  isActive: z.boolean().default(true),

  autoSync: z.boolean().default(false),

  syncFolders: z
    .string()
    .optional()
    .refine((val) => {
      if (!val || val.trim() === '') return true;
      try {
        const parsed = JSON.parse(val);
        return Array.isArray(parsed);
      } catch {
        return false;
      }
    }, 'integration:cloudStorage.validation.syncFolders.invalidJson'),

  userId: z.number().optional(),
});

export const updateCloudStorageProviderConfigurationSchema = cloudStorageProviderConfigurationSchema.partial();

export const testCloudStorageProviderSchema = z.object({
  testFolderName: z
    .string()
    .max(100, 'integration:cloudStorage.validation.testFolderName.maxLength')
    .optional(),

  testFileName: z
    .string()
    .max(100, 'integration:cloudStorage.validation.testFileName.maxLength')
    .optional(),
});

export const cloudStorageProviderQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  providerType: z.enum(['google-drive', 'onedrive', 'dropbox', 'box']).optional(),
  isActive: z.boolean().optional(),
  userId: z.number().optional(),
});

export const fileUploadSchema = z.object({
  fileName: z.string().min(1, 'integration:cloudStorage.validation.fileName.required'),
  parentFolderId: z.string().optional(),
  description: z.string().optional(),
  mimeType: z.string().optional(),
});

export const folderCreateSchema = z.object({
  folderName: z.string().min(1, 'integration:cloudStorage.validation.folderName.required'),
  parentFolderId: z.string().optional(),
  description: z.string().optional(),
});

export const fileSearchSchema = z.object({
  query: z.string().min(1, 'integration:cloudStorage.validation.query.required'),
  fileType: z.enum(['file', 'folder']).optional(),
  mimeType: z.string().optional(),
  parentFolderId: z.string().optional(),
  maxResults: z.number().min(1).max(100).optional(),
});

export const batchOperationSchema = z.object({
  operation: z.enum(['copy', 'move', 'delete'], {
    errorMap: () => ({ message: 'integration:cloudStorage.validation.operation.invalid' }),
  }),
  fileIds: z.array(z.string()).min(1, 'integration:cloudStorage.validation.fileIds.required'),
  targetFolderId: z.string().optional(),
});

export const shareLinkSchema = z.object({
  fileId: z.string().min(1, 'integration:cloudStorage.validation.fileId.required'),
  permission: z.enum(['reader', 'writer', 'commenter'], {
    errorMap: () => ({ message: 'integration:cloudStorage.validation.permission.invalid' }),
  }),
  expirationTime: z.string().optional(),
  password: z.string().optional(),
});

export type CloudStorageProviderConfigurationFormData = z.infer<typeof cloudStorageProviderConfigurationSchema>;
export type UpdateCloudStorageProviderConfigurationFormData = z.infer<typeof updateCloudStorageProviderConfigurationSchema>;
export type TestCloudStorageProviderFormData = z.infer<typeof testCloudStorageProviderSchema>;
export type CloudStorageProviderQueryFormData = z.infer<typeof cloudStorageProviderQuerySchema>;
export type FileUploadFormData = z.infer<typeof fileUploadSchema>;
export type FolderCreateFormData = z.infer<typeof folderCreateSchema>;
export type FileSearchFormData = z.infer<typeof fileSearchSchema>;
export type BatchOperationFormData = z.infer<typeof batchOperationSchema>;
export type ShareLinkFormData = z.infer<typeof shareLinkSchema>;
