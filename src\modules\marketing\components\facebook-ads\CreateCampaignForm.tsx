import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Select,
  DatePicker,
  Switch,
  Textarea,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';

interface CreateCampaignFormData {
  name: string;
  objective: string;
  accountId: string;
  budgetType: 'daily' | 'lifetime';
  budget: number;
  startDate: string;
  endDate?: string;
  status: 'active' | 'paused';
  specialAdCategories: string[];
  description?: string;
}

interface CreateCampaignFormProps {
  /**
   * Callback khi submit form
   */
  onSubmit: (data: CreateCampaignFormData) => Promise<void>;
  
  /**
   * Callback khi cancel
   */
  onCancel?: () => void;
  
  /**
   * Loading state
   */
  isLoading?: boolean;
  
  /**
   * Initial data for editing
   */
  initialData?: Partial<CreateCampaignFormData>;
}

/**
 * Create Campaign Form Component
 * Form tạo chiến dịch Facebook Ads mới
 */
const CreateCampaignForm: React.FC<CreateCampaignFormProps> = ({
  onSubmit,
  onCancel,
  isLoading = false,
  initialData,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const { formRef, setFormErrors } = useFormErrors();
  const { adAccounts } = useFacebookAuth();
  
  const [formData, setFormData] = useState<CreateCampaignFormData>({
    name: '',
    objective: 'TRAFFIC',
    accountId: '',
    budgetType: 'daily',
    budget: 100000,
    startDate: new Date().toISOString().split('T')[0],
    status: 'paused',
    specialAdCategories: [],
    description: '',
    ...initialData,
  });

  const objectiveOptions = useMemo(() => [
    { value: 'TRAFFIC', label: t('marketing:facebookAds.objectives.traffic', 'Lưu lượng truy cập') },
    { value: 'CONVERSIONS', label: t('marketing:facebookAds.objectives.conversions', 'Chuyển đổi') },
    { value: 'BRAND_AWARENESS', label: t('marketing:facebookAds.objectives.brandAwareness', 'Nhận diện thương hiệu') },
    { value: 'REACH', label: t('marketing:facebookAds.objectives.reach', 'Tiếp cận') },
    { value: 'ENGAGEMENT', label: t('marketing:facebookAds.objectives.engagement', 'Tương tác') },
    { value: 'LEAD_GENERATION', label: t('marketing:facebookAds.objectives.leadGeneration', 'Thu thập khách hàng tiềm năng') },
    { value: 'MESSAGES', label: t('marketing:facebookAds.objectives.messages', 'Tin nhắn') },
    { value: 'VIDEO_VIEWS', label: t('marketing:facebookAds.objectives.videoViews', 'Lượt xem video') },
  ], [t]);

  const budgetTypeOptions = useMemo(() => [
    { value: 'daily', label: t('marketing:facebookAds.budget.daily', 'Ngân sách hàng ngày') },
    { value: 'lifetime', label: t('marketing:facebookAds.budget.lifetime', 'Ngân sách trọn đời') },
  ], [t]);

  const accountOptions = useMemo(() => {
    return adAccounts.map(account => ({
      value: account.accountId,
      label: `${account.name} (${account.accountId})`,
    }));
  }, [adAccounts]);

  const handleInputChange = (field: keyof CreateCampaignFormData, value: string | number | boolean | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (data: CreateCampaignFormData) => {
    // Validation
    const errors: Record<string, string> = {};

    if (!data.name.trim()) {
      errors.name = t('marketing:facebookAds.validation.nameRequired', 'Tên chiến dịch là bắt buộc');
    }

    if (!data.accountId) {
      errors.accountId = t('marketing:facebookAds.validation.accountRequired', 'Vui lòng chọn tài khoản quảng cáo');
    }

    if (!data.budget || data.budget <= 0) {
      errors.budget = t('marketing:facebookAds.validation.budgetRequired', 'Ngân sách phải lớn hơn 0');
    }

    if (data.budgetType === 'daily' && data.budget < 50000) {
      errors.budget = t('marketing:facebookAds.validation.minDailyBudget', 'Ngân sách hàng ngày tối thiểu là 50,000 VND');
    }

    if (data.budgetType === 'lifetime' && data.budget < 350000) {
      errors.budget = t('marketing:facebookAds.validation.minLifetimeBudget', 'Ngân sách trọn đời tối thiểu là 350,000 VND');
    }

    if (!data.startDate) {
      errors.startDate = t('marketing:facebookAds.validation.startDateRequired', 'Ngày bắt đầu là bắt buộc');
    }

    if (data.endDate && new Date(data.endDate) <= new Date(data.startDate)) {
      errors.endDate = t('marketing:facebookAds.validation.endDateAfterStart', 'Ngày kết thúc phải sau ngày bắt đầu');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Failed to create campaign:', error);
    }
  };

  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Icon name="plus" className="text-primary" />
          <Typography variant="h5">
            {initialData 
              ? t('marketing:facebookAds.campaigns.edit', 'Chỉnh sửa chiến dịch')
              : t('marketing:facebookAds.campaigns.create', 'Tạo chiến dịch mới')
            }
          </Typography>
        </div>

        <Form ref={formRef} onSubmit={(data) => handleSubmit(data as CreateCampaignFormData)}>
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <Typography variant="h6" className="mb-4">
                {t('marketing:facebookAds.campaigns.form.basicInfo', 'Thông tin cơ bản')}
              </Typography>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.name', 'Tên chiến dịch')}
                  name="name"
                  required
                >
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={t('marketing:facebookAds.campaigns.form.namePlaceholder', 'Nhập tên chiến dịch')}
                  />
                </FormItem>

                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.account', 'Tài khoản quảng cáo')}
                  name="accountId"
                  required
                >
                  <Select
                    value={formData.accountId}
                    onChange={(value) => handleInputChange('accountId', value as string)}
                    options={accountOptions}
                    placeholder={t('marketing:facebookAds.campaigns.form.selectAccount', 'Chọn tài khoản')}
                  />
                </FormItem>

                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.objective', 'Mục tiêu chiến dịch')}
                  name="objective"
                  required
                >
                  <Select
                    value={formData.objective}
                    onChange={(value) => handleInputChange('objective', value as string)}
                    options={objectiveOptions}
                  />
                </FormItem>

                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.status', 'Trạng thái')}
                  name="status"
                >
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={formData.status === 'active'}
                      onChange={(checked) =>
                        handleInputChange('status', checked ? 'active' : 'paused')
                      }
                    />
                    <Typography variant="body2">
                      {formData.status === 'active' 
                        ? t('common:status.active', 'Hoạt động')
                        : t('common:status.paused', 'Tạm dừng')
                      }
                    </Typography>
                  </div>
                </FormItem>
              </div>

              <FormItem
                label={t('marketing:facebookAds.campaigns.form.description', 'Mô tả')}
                name="description"
                className="mt-4"
              >
                <Textarea
                  value={formData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('marketing:facebookAds.campaigns.form.descriptionPlaceholder', 'Mô tả ngắn về chiến dịch (tùy chọn)')}
                  rows={3}
                />
              </FormItem>
            </div>

            {/* Budget Settings */}
            <div>
              <Typography variant="h6" className="mb-4">
                {t('marketing:facebookAds.campaigns.form.budget', 'Cài đặt ngân sách')}
              </Typography>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.budgetType', 'Loại ngân sách')}
                  name="budgetType"
                  required
                >
                  <Select
                    value={formData.budgetType}
                    onChange={(value) => handleInputChange('budgetType', value as 'daily' | 'lifetime')}
                    options={budgetTypeOptions}
                  />
                </FormItem>

                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.budgetAmount', 'Số tiền ngân sách (VND)')}
                  name="budget"
                  required
                >
                  <Input
                    type="number"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', parseInt(e.target.value) || 0)}
                    placeholder="100000"
                    min={formData.budgetType === 'daily' ? 50000 : 350000}
                  />
                </FormItem>
              </div>
            </div>

            {/* Schedule */}
            <div>
              <Typography variant="h6" className="mb-4">
                {t('marketing:facebookAds.campaigns.form.schedule', 'Lịch trình')}
              </Typography>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.startDate', 'Ngày bắt đầu')}
                  name="startDate"
                  required
                >
                  <DatePicker
                    value={formData.startDate ? new Date(formData.startDate) : null}
                    onChange={(value) => handleInputChange('startDate', value?.toISOString().split('T')[0] || '')}
                    minDate={new Date()}
                  />
                </FormItem>

                <FormItem
                  label={t('marketing:facebookAds.campaigns.form.endDate', 'Ngày kết thúc (tùy chọn)')}
                  name="endDate"
                >
                  <DatePicker
                    value={formData.endDate ? new Date(formData.endDate) : null}
                    onChange={(value) => handleInputChange('endDate', value?.toISOString().split('T')[0] || undefined)}
                    minDate={formData.startDate ? new Date(formData.startDate) : new Date()}
                  />
                </FormItem>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 pt-6 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  {t('common:button.cancel', 'Hủy')}
                </Button>
              )}
              
              <Button
                type="submit"
                variant="primary"
                isLoading={isLoading}
              >
                <Icon name="save" className="mr-2" />
                {initialData 
                  ? t('common:button.update', 'Cập nhật')
                  : t('common:button.create', 'Tạo chiến dịch')
                }
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default CreateCampaignForm;
