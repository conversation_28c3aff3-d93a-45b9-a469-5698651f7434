import { z } from 'zod';

/**
 * Google Calendar Configuration Schemas
 */

export const googleCalendarConfigurationSchema = z.object({
  accountName: z
    .string()
    .min(1, 'integration:calendar.validation.accountName.required')
    .max(100, 'integration:calendar.validation.accountName.maxLength'),

  clientId: z
    .string()
    .min(1, 'integration:calendar.validation.clientId.required')
    .max(255, 'integration:calendar.validation.clientId.maxLength'),

  clientSecret: z
    .string()
    .min(1, 'integration:calendar.validation.clientSecret.required')
    .max(255, 'integration:calendar.validation.clientSecret.maxLength'),

  refreshToken: z
    .string()
    .min(1, 'integration:calendar.validation.refreshToken.required'),

  calendarId: z
    .string()
    .optional(),

  isActive: z.boolean().default(true),

  syncEnabled: z.boolean().default(true),

  userId: z.number().optional(),
});

export const updateGoogleCalendarConfigurationSchema = googleCalendarConfigurationSchema.partial();

export const testGoogleCalendarSchema = z.object({
  testEventTitle: z
    .string()
    .max(200, 'integration:calendar.validation.testEventTitle.maxLength')
    .optional(),

  testEventDescription: z
    .string()
    .max(1000, 'integration:calendar.validation.testEventDescription.maxLength')
    .optional(),
});

export const googleCalendarQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  userId: z.number().optional(),
  isActive: z.boolean().optional(),
  syncEnabled: z.boolean().optional(),
});

export const googleCalendarEventSchema = z.object({
  summary: z.string().min(1, 'integration:calendar.validation.summary.required'),
  description: z.string().optional(),
  start: z.object({
    dateTime: z.string().optional(),
    date: z.string().optional(),
    timeZone: z.string().optional(),
  }),
  end: z.object({
    dateTime: z.string().optional(),
    date: z.string().optional(),
    timeZone: z.string().optional(),
  }),
  location: z.string().optional(),
  attendees: z.array(z.object({
    email: z.string().email(),
    displayName: z.string().optional(),
  })).optional(),
});

export type GoogleCalendarConfigurationFormData = z.infer<typeof googleCalendarConfigurationSchema>;
export type UpdateGoogleCalendarConfigurationFormData = z.infer<typeof updateGoogleCalendarConfigurationSchema>;
export type TestGoogleCalendarFormData = z.infer<typeof testGoogleCalendarSchema>;
export type GoogleCalendarQueryFormData = z.infer<typeof googleCalendarQuerySchema>;
export type GoogleCalendarEventFormData = z.infer<typeof googleCalendarEventSchema>;
