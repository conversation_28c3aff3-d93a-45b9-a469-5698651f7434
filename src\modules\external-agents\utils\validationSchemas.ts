import { z } from 'zod';
import { ProtocolType, AuthenticationType, ExternalAgentStatus, AgentCapability } from '../types';

// Base schemas
export const urlSchema = z.string().url('Invalid URL format');

export const protocolTypeSchema = z.nativeEnum(ProtocolType);

export const authenticationTypeSchema = z.nativeEnum(AuthenticationType);

export const agentStatusSchema = z.nativeEnum(ExternalAgentStatus);

export const agentCapabilitySchema = z.nativeEnum(AgentCapability);

// Authentication configuration schema
export const authenticationConfigSchema = z.object({
  type: authenticationTypeSchema,
  credentials: z.record(z.string()).optional(),
  headers: z.record(z.string()).optional(),
  parameters: z.record(z.string()).optional(),
}).refine((data) => {
  // Validate required credentials based on auth type
  if (data.type === AuthenticationType.API_KEY) {
    return data.credentials?.apiKey;
  }
  if (data.type === AuthenticationType.BEARER_TOKEN) {
    return data.credentials?.token;
  }
  if (data.type === AuthenticationType.BASIC_AUTH) {
    return data.credentials?.username && data.credentials?.password;
  }
  if (data.type === AuthenticationType.OAUTH2) {
    return data.credentials?.clientId && data.credentials?.clientSecret;
  }
  return true;
}, {
  message: 'Required credentials missing for authentication type',
});

// External agent create schema
export const externalAgentCreateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  protocol: protocolTypeSchema,
  endpoint: urlSchema,
  authentication: authenticationConfigSchema,
  capabilities: z.array(agentCapabilitySchema).optional(),
  metadata: z.record(z.any()).optional(),
  tags: z.array(z.string()).optional(),
});

// External agent update schema
export const externalAgentUpdateSchema = externalAgentCreateSchema.partial().extend({
  status: agentStatusSchema.optional(),
  isActive: z.boolean().optional(),
});

// Query parameters schema
export const externalAgentQuerySchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  status: agentStatusSchema.optional(),
  protocol: protocolTypeSchema.optional(),
  capabilities: z.array(agentCapabilitySchema).optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// Protocol configuration schema
export const protocolConfigSchema = z.object({
  type: protocolTypeSchema,
  version: z.string().optional(),
  specifications: z.record(z.any()).optional(),
  supportedMethods: z.array(z.string()).optional(),
  requiredHeaders: z.array(z.string()).optional(),
  optionalHeaders: z.array(z.string()).optional(),
  authenticationMethods: z.array(authenticationTypeSchema).optional(),
});

// Protocol template schema
export const protocolTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  protocol: protocolTypeSchema,
  config: protocolConfigSchema,
  isDefault: z.boolean().default(false),
  isPublic: z.boolean().default(false),
  createdBy: z.string().optional(),
});

// Webhook configuration schema
export const webhookConfigSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  url: urlSchema,
  events: z.array(z.string()).min(1, 'At least one event is required'),
  isActive: z.boolean().default(true),
  secret: z.string().optional(),
  headers: z.record(z.string()).optional(),
  retryPolicy: z.object({
    maxRetries: z.number().min(0).max(10),
    retryDelay: z.number().min(100),
    backoffMultiplier: z.number().min(1).max(5),
  }).optional(),
});

// Message query schema
export const messageQuerySchema = z.object({
  agentId: z.string().optional(),
  type: z.enum(['request', 'response', 'error', 'notification']).optional(),
  status: z.enum(['pending', 'success', 'error']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(1000).optional(),
  search: z.string().optional(),
}).refine((data) => {
  // Validate date range
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) <= new Date(data.endDate);
  }
  return true;
}, {
  message: 'Start date must be before end date',
});

// Connection test schema
export const connectionTestSchema = z.object({
  timeout: z.number().min(1000).max(60000).optional(),
});

// Bulk operations schema
export const bulkOperationSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one ID is required').max(50, 'Too many items selected'),
  action: z.enum(['delete', 'activate', 'deactivate', 'test']),
});

// Validation helper functions
export const validateExternalAgentCreate = (data: unknown) => {
  return externalAgentCreateSchema.safeParse(data);
};

export const validateExternalAgentUpdate = (data: unknown) => {
  return externalAgentUpdateSchema.safeParse(data);
};

export const validateExternalAgentQuery = (data: unknown) => {
  return externalAgentQuerySchema.safeParse(data);
};

export const validateProtocolConfig = (data: unknown) => {
  return protocolConfigSchema.safeParse(data);
};

export const validateProtocolTemplate = (data: unknown) => {
  return protocolTemplateSchema.safeParse(data);
};

export const validateWebhookConfig = (data: unknown) => {
  return webhookConfigSchema.safeParse(data);
};

export const validateMessageQuery = (data: unknown) => {
  return messageQuerySchema.safeParse(data);
};

export const validateConnectionTest = (data: unknown) => {
  return connectionTestSchema.safeParse(data);
};

export const validateBulkOperation = (data: unknown) => {
  return bulkOperationSchema.safeParse(data);
};
