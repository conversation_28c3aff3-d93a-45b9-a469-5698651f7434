/**
 * Đ<PERSON><PERSON> nghĩa các types cho Product trong module business
 */

/**
 * Enum cho loại sản phẩm
 */
export enum ProductTypeEnum {
  PHYSICAL = 'PHYSICAL',
  DIGITAL = 'DIGITAL',
  SERVICE = 'SERVICE',
  EVENT = 'EVENT',
}

/**
 * Enum cho loại giá sản phẩm
 */
export enum PriceTypeEnum {
  /**
   * Có giá cố định
   */
  HAS_PRICE = 'HAS_PRICE',

  /**
   * Gi<PERSON> dạng chuỗi(mô tả)
   */
  STRING_PRICE = 'STRING_PRICE',

  /**
   * Không có giá
   */
  NO_PRICE = 'NO_PRICE',
}

/**
 * Interface cho giá sản phẩm khi typePrice là HAS_PRICE
 */
export interface HasPriceDto {
  /**
   * Giá niêm yết
   */
  listPrice: number;

  /**
   * <PERSON><PERSON><PERSON> bán
   */
  salePrice: number;

  /**
   * Đơn vị tiền tệ
   */
  currency: string;
}

/**
 * Interface cho giá sản phẩm khi typePrice là STRING_PRICE
 */
export interface StringPriceDto {
  /**
   * Mô tả giá
   */
  priceDescription: string;
}

/**
 * Interface cho thông tin hình ảnh sản phẩm
 */
export interface ProductImageDto {
  /**
   * Key của hình ảnh trên S3
   */
  key: string;

  /**
   * Vị trí của hình ảnh
   */
  position: number;

  /**
   * URL để xem hình ảnh
   */
  url: string;
}

/**
 * Interface cho cấu hình vận chuyển
 */
export interface ShipmentConfigDto {
  /**
   * Chiều dài (cm)
   */
  lengthCm?: number;

  /**
   * Chiều rộng (cm)
   */
  widthCm?: number;

  /**
   * Chiều cao (cm)
   */
  heightCm?: number;

  /**
   * Cân nặng (gram)
   */
  weightGram?: number;
}

/**
 * Interface cho phân loại sản phẩm
 */
export interface ClassificationDto {
  /**
   * ID của phân loại
   */
  id: number;

  /**
   * Loại phân loại
   */
  type: string;

  /**
   * Giá của phân loại
   */
  price: {
    currency: string;
    listPrice: number;
    salePrice: number;
    value: number;
  };

  /**
   * Danh sách custom fields của phân loại (theo cấu trúc API thực tế)
   */
  customFields: Array<{
    customFieldId: number;
    value: Record<string, unknown>;
  }>;
}

/**
 * Interface cho metadata chứa custom fields
 */
export interface ProductMetadataDto {
  /**
   * Danh sách custom fields
   */
  customFields?: Array<{
    id: number;
    tags: string[];
    type: string;
    label: string;
    value: Record<string, unknown>;
    configId: string;
    required: boolean;
    configJson: Record<string, unknown>;
  }>;
}

/**
 * Interface cho thông tin sản phẩm
 */
export interface ProductDto {
  /**
   * ID của sản phẩm
   */
  id: number;

  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Giá sản phẩm
   */
  price: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice: PriceTypeEnum;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách hình ảnh
   */
  images?: ProductImageDto[];

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * ID người tạo
   */
  createdBy: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  updatedAt: number;

  /**
   * Cấu hình vận chuyển
   */
  shipmentConfig?: ShipmentConfigDto;

  /**
   * Metadata chứa custom fields
   */
  metadata?: ProductMetadataDto;

  /**
   * Danh sách phân loại sản phẩm
   */
  classifications?: ClassificationDto[];
}

/**
 * Interface cho tham số truy vấn sản phẩm
 */
export interface ProductQueryParams {
  /**
   * Trang hiện tại
   */
  page?: number;

  /**
   * Số lượng sản phẩm trên một trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm theo tên sản phẩm
   */
  search?: string;

  /**
   * Loại giá
   */
  typePrice?: PriceTypeEnum;

  /**
   * Sắp xếp theo trường
   */
  sortBy?: string;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  /**
   * Danh sách items
   */
  items: T[];

  /**
   * Thông tin phân trang
   */
  meta: {
    /**
     * Tổng số items
     */
    totalItems: number;

    /**
     * Số lượng items trên trang hiện tại
     */
    itemCount: number;

    /**
     * Số lượng items trên một trang
     */
    itemsPerPage: number;

    /**
     * Tổng số trang
     */
    totalPages: number;

    /**
     * Trang hiện tại
     */
    currentPage: number;
  };
}

/**
 * Interface cho trường tùy chỉnh
 */
export interface CustomFieldDto {
  /**
   * ID của trường tùy chỉnh
   */
  customFieldId: number;

  /**
   * Giá trị của trường tùy chỉnh
   */
  value: Record<string, unknown>;
}

/**
 * Interface cho biến thể sản phẩm (để gửi lên backend)
 */
export interface ProductVariantDto {
  /**
   * Loại phân loại
   */
  type: string;

  /**
   * Giá của phân loại
   */
  price?: {
    listPrice: number;
    salePrice: number;
    currency: string;
  };

  /**
   * Danh sách trường tùy chỉnh của phân loại
   */
  customFields?: CustomFieldDto[];
}

/**
 * Interface cho nhóm trường tùy chỉnh
 */
export interface CustomGroupFormDto {
  /**
   * ID của nhóm trường tùy chỉnh
   */
  groupId: number;

  /**
   * Danh sách trường tùy chỉnh trong nhóm
   */
  fields: CustomFieldDto[];
}

/**
 * Interface cho tạo sản phẩm mới (đã sync với Backend API)
 */
export interface CreateProductDto {
  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Giá sản phẩm
   */
  price: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice: PriceTypeEnum;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * Cấu hình vận chuyển
   */
  shipmentConfig?: ShipmentConfigDto;

  /**
   * Danh sách media types của ảnh sản phẩm
   */
  imagesMediaTypes?: string[];

  /**
   * Danh sách phân loại sản phẩm (sử dụng cấu trúc của ProductVariantDto)
   */
  classifications?: ProductVariantDto[];

  /**
   * Danh sách trường tùy chỉnh
   */
  customFields?: CustomFieldDto[];
}

/**
 * Interface cho cập nhật sản phẩm
 */
export interface UpdateProductDto {
  /**
   * Tên sản phẩm
   */
  name?: string;

  /**
   * Giá sản phẩm
   */
  price?: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice?: PriceTypeEnum;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách thao tác ảnh
   */
  images?: Array<{
    operation: 'ADD' | 'DELETE';
    position?: number;
    key?: string;
    mimeType?: string;
  }>;

  /**
   * Danh sách tag
   */
  tags?: string[];

  /**
   * Cấu hình vận chuyển
   */
  shipmentConfig?: ShipmentConfigDto;

  /**
   * Danh sách custom fields cho sản phẩm
   */
  customFields?: Array<{
    customFieldId: number;
    value: {
      value: unknown;
    };
  }>;

  /**
   * Danh sách phân loại sản phẩm (sử dụng cấu trúc của ProductVariantDto)
   */
  classifications?: ProductVariantDto[];
}

/**
 * Interface cho response khi tạo sản phẩm với upload URLs
 */
export interface CreateProductResponse {
  /**
   * ID của sản phẩm đã tạo
   */
  id: string;

  /**
   * Tên sản phẩm
   */
  name: string;

  /**
   * Giá sản phẩm
   */
  price: HasPriceDto | StringPriceDto | null;

  /**
   * Loại giá
   */
  typePrice: string;

  /**
   * Mô tả sản phẩm
   */
  description?: string;

  /**
   * Danh sách hình ảnh với upload URLs
   */
  images: Array<{
    /**
     * Key của hình ảnh trên S3
     */
    key: string;
    /**
     * Vị trí của hình ảnh
     */
    position: number;
    /**
     * URL để upload ảnh lên cloud
     */
    url: string;
  }>;
}
