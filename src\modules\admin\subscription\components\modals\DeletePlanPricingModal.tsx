import React from 'react';
// import { useTranslation } from 'react-i18next';
import DeleteConfirmModal from '@/shared/components/common/DeleteConfirmModal';
import { useDeletePlanPricing } from '../../hooks/usePlanPricingAdmin';
import { PlanPricing, BillingCycle, UsageUnit } from '../../types/plan-pricing.admin.types';

interface DeletePlanPricingModalProps {
  isOpen: boolean;
  onClose: () => void;
  pricing: PlanPricing;
  onSuccess?: () => void;
}

/**
 * Modal xác nhận xóa tùy chọn giá
 */
const DeletePlanPricingModal: React.FC<DeletePlanPricingModalProps> = ({
  isOpen,
  onClose,
  pricing,
  onSuccess,
}) => {
  // const { t } = useTranslation(['admin', 'common']);
  const { mutateAsync: deletePricing, isPending: isDeleting } = useDeletePlanPricing();

  // Xử lý xóa
  const handleDelete = async () => {
    try {
      await deletePricing(pricing.id);
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error('Lỗi khi xóa tùy chọn giá:', error);
    }
  };

  // Format giá tiền
  const formatPrice = (price: string) => {
    const numPrice = parseFloat(price);
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(numPrice);
  };

  // Format billing cycle
  const formatBillingCycle = (billingCycle: BillingCycle) => {
    const cycleMap = {
      [BillingCycle.MONTH]: 'Tháng',
      [BillingCycle.SIX_MONTHS]: '6 Tháng',
      [BillingCycle.YEAR]: 'Năm',
    };
    return cycleMap[billingCycle];
  };

  // Format usage limit
  const formatUsageLimit = (usageLimit: string, usageUnit: UsageUnit) => {
    const numLimit = parseFloat(usageLimit);
    if (usageUnit === UsageUnit.BYTES) {
      // Convert bytes to GB
      const gb = numLimit / (1024 * 1024 * 1024);
      return `${gb.toFixed(2)} GB`;
    }
    return `${numLimit.toLocaleString('vi-VN')} ${usageUnit}`;
  };

  // Tạo tên hiển thị cho pricing
  const pricingDisplayName = `${formatPrice(pricing.price)} - ${formatBillingCycle(pricing.billingCycle)} (Plan #${pricing.planId})`;

  // Tạo description với thông tin chi tiết
  const detailDescription = `
ID: #${pricing.id}
Plan ID: #${pricing.planId}
Chu kỳ: ${formatBillingCycle(pricing.billingCycle)}
Giá: ${formatPrice(pricing.price)}
Giới hạn: ${formatUsageLimit(pricing.usageLimit, pricing.usageUnit)}
Trạng thái: ${pricing.isActive ? 'Hoạt động' : 'Không hoạt động'}

Hành động này không thể hoàn tác.`;

  return (
    <DeleteConfirmModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={handleDelete}
      title="Xác nhận xóa tùy chọn giá"
      itemName={pricingDisplayName}
      description={detailDescription}
      isLoading={isDeleting}
      size="md"
    />
  );
};

export default DeletePlanPricingModal;
