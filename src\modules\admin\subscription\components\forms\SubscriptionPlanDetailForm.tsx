import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Chip,
  Loading,
} from '@/shared/components/common';
import { useGetSubscriptionPlanDetail } from '../../hooks/useSubscriptionPlansAdmin';
import { PackageType } from '../../types/subscription-plans.admin.types';

interface SubscriptionPlanDetailFormProps {
  planId: number;
  onClose: () => void;
}

/**
 * Form hiển thị chi tiết gói dịch vụ subscription
 */
const SubscriptionPlanDetailForm: React.FC<SubscriptionPlanDetailFormProps> = ({
  planId,
  onClose,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  
  // Lấy chi tiết subscription plan
  const { data: planResponse, isLoading, error } = useGetSubscriptionPlanDetail(planId);
  const plan = planResponse?.result;

  // Format date
  const formatDate = (timestamp: string) => {
    if (!timestamp) return '-';
    const date = new Date(parseInt(timestamp));
    if (isNaN(date.getTime())) return '-';
    return date.toLocaleString('vi-VN');
  };

  // Render package type chip
  const renderPackageTypeChip = (packageType: PackageType) => {
    const typeConfig = {
      [PackageType.TIME_ONLY]: {
        label: t('admin:subscription.packageType.timeOnly'),
        variant: 'primary' as const,
      },
      [PackageType.HYBRID]: {
        label: t('admin:subscription.packageType.hybrid'),
        variant: 'info' as const,
      },
    };
    
    const config = typeConfig[packageType] || {
      label: packageType,
      variant: 'default' as const,
    };
    
    return (
      <Chip variant={config.variant} size="md">
        {config.label}
      </Chip>
    );
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <div className="p-6 flex justify-center">
          <Loading />
        </div>
      </Card>
    );
  }

  if (error || !plan) {
    return (
      <Card className="w-full">
        <div className="p-6">
          <Typography variant="h3" className="mb-4 text-red-600">
            Lỗi
          </Typography>
          <Typography className="mb-4">
            Không thể tải thông tin chi tiết gói dịch vụ.
          </Typography>
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <Typography variant="h3" className="mb-2">
              Chi tiết gói dịch vụ #{plan.id}
            </Typography>
            <Typography variant="h4" className="text-gray-600 font-medium">
              {plan.name}
            </Typography>
          </div>
          {renderPackageTypeChip(plan.packageType)}
        </div>

        <div className="space-y-6">
          {/* Thông tin cơ bản */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1 font-medium">
                ID
              </Typography>
              <Typography variant="body1" className="font-semibold text-lg">
                #{plan.id}
              </Typography>
            </div>

            <div>
              <Typography variant="body2" className="text-gray-500 mb-1 font-medium">
                Loại gói dịch vụ
              </Typography>
              <div className="mt-1">
                {renderPackageTypeChip(plan.packageType)}
              </div>
            </div>
          </div>

          {/* Tên gói dịch vụ */}
          <div className="border-t pt-6">
            <Typography variant="h4" className="mb-3 text-gray-800">
              Tên gói dịch vụ
            </Typography>
            <div className="bg-gray-50 p-4 rounded-lg">
              <Typography variant="h3" className="text-blue-600 font-bold">
                {plan.name}
              </Typography>
            </div>
          </div>

          {/* Mô tả */}
          <div className="border-t pt-6">
            <Typography variant="h4" className="mb-3 text-gray-800">
              Mô tả
            </Typography>
            <div className="bg-gray-50 p-4 rounded-lg">
              <Typography variant="body1" className="text-gray-700 leading-relaxed">
                {plan.description || 'Không có mô tả'}
              </Typography>
            </div>
          </div>

          {/* Thông tin loại gói */}
          <div className="border-t pt-6">
            <Typography variant="h4" className="mb-3 text-gray-800">
              Thông tin loại gói
            </Typography>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                {renderPackageTypeChip(plan.packageType)}
                <Typography variant="body1" className="font-medium">
                  {plan.packageType === PackageType.TIME_ONLY 
                    ? 'Gói dịch vụ theo thời gian'
                    : 'Gói dịch vụ kết hợp'
                  }
                </Typography>
              </div>
              
              <Typography variant="body2" className="text-gray-600">
                {plan.packageType === PackageType.TIME_ONLY 
                  ? 'Gói dịch vụ này được tính phí theo thời gian sử dụng (tháng/năm).'
                  : 'Gói dịch vụ này kết hợp cả thời gian và mức sử dụng.'
                }
              </Typography>
            </div>
          </div>

          {/* Thông tin thời gian */}
          <div className="border-t pt-6">
            <Typography variant="h4" className="mb-3 text-gray-800">
              Thông tin thời gian
            </Typography>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <Typography variant="body2" className="text-green-700 mb-1 font-medium">
                  Ngày tạo
                </Typography>
                <Typography variant="body1" className="font-semibold">
                  {formatDate(plan.createdAt)}
                </Typography>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <Typography variant="body2" className="text-yellow-700 mb-1 font-medium">
                  Ngày cập nhật
                </Typography>
                <Typography variant="body1" className="font-semibold">
                  {formatDate(plan.updatedAt)}
                </Typography>
              </div>
            </div>
          </div>

          {/* Thống kê */}
          <div className="border-t pt-6">
            <Typography variant="h4" className="mb-3 text-gray-800">
              Thống kê
            </Typography>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-purple-50 p-4 rounded-lg text-center">
                <Typography variant="h2" className="text-purple-600 font-bold mb-1">
                  #{plan.id}
                </Typography>
                <Typography variant="body2" className="text-purple-700">
                  ID Gói dịch vụ
                </Typography>
              </div>

              <div className="bg-indigo-50 p-4 rounded-lg text-center">
                <Typography variant="h2" className="text-indigo-600 font-bold mb-1">
                  {plan.packageType === PackageType.TIME_ONLY ? '1' : '2'}
                </Typography>
                <Typography variant="body2" className="text-indigo-700">
                  Loại tính phí
                </Typography>
              </div>

              <div className="bg-pink-50 p-4 rounded-lg text-center">
                <Typography variant="h2" className="text-pink-600 font-bold mb-1">
                  {plan.name.length}
                </Typography>
                <Typography variant="body2" className="text-pink-700">
                  Độ dài tên
                </Typography>
              </div>
            </div>
          </div>

          {/* Ghi chú */}
          <div className="border-t pt-6">
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <Typography variant="body2" className="text-blue-700">
                    <strong>Lưu ý:</strong> Đây là thông tin chi tiết của gói dịch vụ. 
                    Để chỉnh sửa thông tin, vui lòng sử dụng chức năng "Chỉnh sửa" từ danh sách gói dịch vụ.
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end pt-6 border-t mt-6">
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default SubscriptionPlanDetailForm;
