import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  ResponsiveGrid,
  Select,
  Badge,
} from '@/shared/components/common';

interface AnalyticsMetric {
  title: string;
  value: string | number;
  change: string;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: string;
  description?: string;
}

interface CampaignPerformance {
  id: string;
  name: string;
  status: 'active' | 'paused' | 'archived';
  spend: number;
  impressions: number;
  clicks: number;
  ctr: number;
  cpc: number;
  conversions: number;
  roas: number;
}

interface FacebookAnalyticsDashboardProps {
  /**
   * Account ID filter
   */
  accountId?: string;
  
  /**
   * Date range
   */
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  
  /**
   * Show detailed metrics
   */
  showDetailedMetrics?: boolean;
}

/**
 * Facebook Analytics Dashboard Component
 * Dashboard hiển thị analytics và insights cho Facebook Ads
 */
const FacebookAnalyticsDashboard: React.FC<FacebookAnalyticsDashboardProps> = ({
  showDetailedMetrics = true,
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  // Mock data - in real app, this would come from API
  const overviewMetrics: AnalyticsMetric[] = useMemo(() => [
    {
      title: t('marketing:facebookAds.analytics.totalSpend', 'Tổng chi phí'),
      value: '₫12,450,000',
      change: '+15.2%',
      changeType: 'increase',
      icon: 'dollar-sign',
      description: t('marketing:facebookAds.analytics.totalSpendDesc', 'So với kỳ trước'),
    },
    {
      title: t('marketing:facebookAds.analytics.impressions', 'Lượt hiển thị'),
      value: '2,345,678',
      change: '+8.7%',
      changeType: 'increase',
      icon: 'eye',
      description: t('marketing:facebookAds.analytics.impressionsDesc', 'Tổng lượt hiển thị quảng cáo'),
    },
    {
      title: t('marketing:facebookAds.analytics.clicks', 'Lượt nhấp'),
      value: '45,230',
      change: '+12.3%',
      changeType: 'increase',
      icon: 'mouse-pointer',
      description: t('marketing:facebookAds.analytics.clicksDesc', 'Tổng lượt nhấp vào quảng cáo'),
    },
    {
      title: t('marketing:facebookAds.analytics.ctr', 'Tỷ lệ nhấp (CTR)'),
      value: '1.93%',
      change: '+0.15%',
      changeType: 'increase',
      icon: 'trending-up',
      description: t('marketing:facebookAds.analytics.ctrDesc', 'Clicks / Impressions'),
    },
    {
      title: t('marketing:facebookAds.analytics.cpc', 'Chi phí/nhấp (CPC)'),
      value: '₫275',
      change: '-8.2%',
      changeType: 'decrease',
      icon: 'target',
      description: t('marketing:facebookAds.analytics.cpcDesc', 'Spend / Clicks'),
    },
    {
      title: t('marketing:facebookAds.analytics.conversions', 'Chuyển đổi'),
      value: '1,234',
      change: '+22.1%',
      changeType: 'increase',
      icon: 'check-circle',
      description: t('marketing:facebookAds.analytics.conversionsDesc', 'Tổng số chuyển đổi'),
    },
    {
      title: t('marketing:facebookAds.analytics.roas', 'ROAS'),
      value: '4.2x',
      change: '+0.8x',
      changeType: 'increase',
      icon: 'trending-up',
      description: t('marketing:facebookAds.analytics.roasDesc', 'Return on Ad Spend'),
    },
    {
      title: t('marketing:facebookAds.analytics.reach', 'Tiếp cận'),
      value: '1,876,543',
      change: '+5.4%',
      changeType: 'increase',
      icon: 'users',
      description: t('marketing:facebookAds.analytics.reachDesc', 'Số người được tiếp cận'),
    },
  ], [t]);

  const topCampaigns: CampaignPerformance[] = useMemo(() => [
    {
      id: '1',
      name: 'Summer Sale 2024',
      status: 'active',
      spend: 2500000,
      impressions: 450000,
      clicks: 8500,
      ctr: 1.89,
      cpc: 294,
      conversions: 245,
      roas: 4.8,
    },
    {
      id: '2',
      name: 'Brand Awareness Q2',
      status: 'active',
      spend: 1800000,
      impressions: 680000,
      clicks: 12400,
      ctr: 1.82,
      cpc: 145,
      conversions: 189,
      roas: 3.2,
    },
    {
      id: '3',
      name: 'Product Launch',
      status: 'paused',
      spend: 3200000,
      impressions: 520000,
      clicks: 15600,
      ctr: 3.0,
      cpc: 205,
      conversions: 456,
      roas: 5.1,
    },
  ], []);

  const periodOptions = [
    { value: '1d', label: t('marketing:facebookAds.analytics.periods.1d', 'Hôm nay') },
    { value: '7d', label: t('marketing:facebookAds.analytics.periods.7d', '7 ngày qua') },
    { value: '30d', label: t('marketing:facebookAds.analytics.periods.30d', '30 ngày qua') },
    { value: '90d', label: t('marketing:facebookAds.analytics.periods.90d', '90 ngày qua') },
    { value: 'custom', label: t('marketing:facebookAds.analytics.periods.custom', 'Tùy chỉnh') },
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const getStatusConfig = (status: string) => {
    const configs = {
      active: { label: t('common:status.active', 'Hoạt động'), variant: 'success' as const },
      paused: { label: t('common:status.paused', 'Tạm dừng'), variant: 'warning' as const },
      archived: { label: t('common:status.archived', 'Lưu trữ'), variant: 'secondary' as const },
    };
    return configs[status as keyof typeof configs] || configs.paused;
  };

  return (
    <div className="space-y-6">
      {/* Header with filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Typography variant="h5">
            {t('marketing:facebookAds.analytics.title', 'Phân tích Facebook Ads')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {t('marketing:facebookAds.analytics.description', 'Theo dõi hiệu suất và tối ưu hóa chiến dịch quảng cáo')}
          </Typography>
        </div>
        
        <div className="flex space-x-2">
          <Select
            value={selectedPeriod}
            onChange={(value) => setSelectedPeriod(value as string)}
            options={periodOptions}
          />
          
          <Button variant="outline">
            <Icon name="download" className="mr-2" />
            {t('marketing:facebookAds.analytics.export', 'Xuất báo cáo')}
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div>
        <Typography variant="h6" className="mb-4">
          {t('marketing:facebookAds.analytics.overview', 'Tổng quan hiệu suất')}
        </Typography>
        
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}>
          {overviewMetrics.map((metric, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Icon name={metric.icon} className="text-primary" />
                <div className={`flex items-center text-sm ${
                  metric.changeType === 'increase' ? 'text-green-600' : 
                  metric.changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  <Icon 
                    name={metric.changeType === 'increase' ? 'trending-up' : 
                          metric.changeType === 'decrease' ? 'trending-down' : 'minus'} 
                    size="sm" 
                    className="mr-1" 
                  />
                  {metric.change}
                </div>
              </div>
              
              <Typography variant="h4" className="font-bold mb-1">
                {metric.value}
              </Typography>
              
              <Typography variant="body2" className="text-muted-foreground mb-1">
                {metric.title}
              </Typography>
              
              {metric.description && (
                <Typography variant="caption" className="text-muted-foreground">
                  {metric.description}
                </Typography>
              )}
            </Card>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Top Performing Campaigns */}
      <div>
        <Typography variant="h6" className="mb-4">
          {t('marketing:facebookAds.analytics.topCampaigns', 'Chiến dịch hiệu suất cao')}
        </Typography>
        
        <Card>
          <div className="p-4">
            <div className="space-y-4">
              {topCampaigns.map((campaign) => {
                const statusConfig = getStatusConfig(campaign.status);
                
                return (
                  <div key={campaign.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <Typography variant="body1" className="font-medium">
                          {campaign.name}
                        </Typography>
                        <Badge variant={statusConfig.variant}>
                          {statusConfig.label}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 text-sm">
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">
                            {t('marketing:facebookAds.metrics.spend', 'Chi phí')}:
                          </Typography>
                          <Typography variant="body2" className="font-medium">
                            {formatCurrency(campaign.spend)}
                          </Typography>
                        </div>
                        
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">
                            {t('marketing:facebookAds.metrics.clicks', 'Nhấp')}:
                          </Typography>
                          <Typography variant="body2" className="font-medium">
                            {formatNumber(campaign.clicks)}
                          </Typography>
                        </div>
                        
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">
                            CTR:
                          </Typography>
                          <Typography variant="body2" className="font-medium">
                            {campaign.ctr.toFixed(2)}%
                          </Typography>
                        </div>
                        
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">
                            ROAS:
                          </Typography>
                          <Typography variant="body2" className="font-medium text-green-600">
                            {campaign.roas.toFixed(1)}x
                          </Typography>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Icon name="eye" size="sm" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Icon name="edit" size="sm" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
      </div>

      {/* Performance Trends */}
      {showDetailedMetrics && (
        <div>
          <Typography variant="h6" className="mb-4">
            {t('marketing:facebookAds.analytics.trends', 'Xu hướng hiệu suất')}
          </Typography>
          
          <Card className="p-6">
            <div className="text-center py-8">
              <Icon name="bar-chart" size="xl" className="text-muted-foreground mb-4" />
              <Typography variant="h6" className="mb-2">
                {t('marketing:facebookAds.analytics.chartPlaceholder', 'Biểu đồ hiệu suất')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:facebookAds.analytics.chartDescription', 'Biểu đồ chi tiết sẽ được hiển thị ở đây khi tích hợp với thư viện chart')}
              </Typography>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default FacebookAnalyticsDashboard;
