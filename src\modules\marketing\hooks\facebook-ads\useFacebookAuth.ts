import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  createFacebookAuthUrl,
  handleFacebookAuthCallback,
  getFacebookUserInfo,
  getFacebookUserAdAccounts,
  validateFacebookToken,
  revokeF<PERSON>bookToken,
  FacebookUserInfo,
  FacebookAuthCallbackRequest,
  FacebookAuthUtils,
} from '../../services/facebook-auth.service';

/**
 * Facebook Authentication Hooks
 * React Query hooks cho Facebook OAuth authentication
 */

// Query Keys
export const FACEBOOK_AUTH_QUERY_KEYS = {
  AUTH_URL: 'facebook-auth-url',
  USER_INFO: 'facebook-user-info',
  AD_ACCOUNTS: 'facebook-ad-accounts',
  TOKEN_VALIDATION: 'facebook-token-validation',
} as const;

/**
 * Hook để tạo Facebook OAuth URL
 */
export const useFacebookAuthUrl = (
  redirectUri: string,
  scopes?: string[]
) => {
  return useQuery({
    queryKey: [FACEBOOK_AUTH_QUERY_KEYS.AUTH_URL, redirectUri, scopes],
    queryFn: () => createFacebookAuthUrl(redirectUri, scopes),
    enabled: false, // Only run when manually triggered
    staleTime: 0, // Always fresh
  });
};

/**
 * Hook để xử lý Facebook OAuth callback
 */
export const useFacebookAuthCallback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FacebookAuthCallbackRequest) => handleFacebookAuthCallback(data),
    onSuccess: (response) => {
      if (response.result) {
        // Store token
        FacebookAuthUtils.storeToken(response.result);
        
        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: [FACEBOOK_AUTH_QUERY_KEYS.USER_INFO],
        });
        queryClient.invalidateQueries({
          queryKey: [FACEBOOK_AUTH_QUERY_KEYS.AD_ACCOUNTS],
        });
      }
    },
  });
};

/**
 * Hook để lấy thông tin user Facebook
 */
export const useFacebookUserInfo = (accessToken?: string) => {
  const token = accessToken || FacebookAuthUtils.getAccessToken();

  const query = useQuery({
    queryKey: [FACEBOOK_AUTH_QUERY_KEYS.USER_INFO, token],
    queryFn: () => getFacebookUserInfo(token!),
    enabled: !!token,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Handle successful response
  useEffect(() => {
    if (query.data?.result) {
      FacebookAuthUtils.storeUserInfo(query.data.result);
    }
  }, [query.data]);

  return query;
};

/**
 * Hook để lấy danh sách ad accounts
 */
export const useFacebookUserAdAccounts = (accessToken?: string) => {
  const token = accessToken || FacebookAuthUtils.getAccessToken();
  
  return useQuery({
    queryKey: [FACEBOOK_AUTH_QUERY_KEYS.AD_ACCOUNTS, token],
    queryFn: () => getFacebookUserAdAccounts(token!),
    enabled: !!token,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để validate token
 */
export const useFacebookTokenValidation = (accessToken?: string) => {
  const token = accessToken || FacebookAuthUtils.getAccessToken();

  const query = useQuery({
    queryKey: [FACEBOOK_AUTH_QUERY_KEYS.TOKEN_VALIDATION, token],
    queryFn: () => validateFacebookToken(token!),
    enabled: !!token,
    staleTime: 1 * 60 * 1000, // 1 minute
    retry: false,
  });

  // Handle error response
  useEffect(() => {
    if (query.error) {
      // Token is invalid, clear it
      FacebookAuthUtils.clearToken();
    }
  }, [query.error]);

  return query;
};

/**
 * Hook để revoke token
 */
export const useFacebookTokenRevoke = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (accessToken: string) => revokeFacebookToken(accessToken),
    onSuccess: () => {
      // Clear stored data
      FacebookAuthUtils.clearToken();
      
      // Clear all related queries
      queryClient.removeQueries({
        queryKey: [FACEBOOK_AUTH_QUERY_KEYS.USER_INFO],
      });
      queryClient.removeQueries({
        queryKey: [FACEBOOK_AUTH_QUERY_KEYS.AD_ACCOUNTS],
      });
      queryClient.removeQueries({
        queryKey: [FACEBOOK_AUTH_QUERY_KEYS.TOKEN_VALIDATION],
      });
    },
  });
};

/**
 * Main Facebook Authentication Hook
 * Tổng hợp tất cả authentication logic
 */
export const useFacebookAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(FacebookAuthUtils.isAuthenticated());
  const [authState, setAuthState] = useState<string | null>(null);

  // Queries
  const userInfoQuery = useFacebookUserInfo();
  const adAccountsQuery = useFacebookUserAdAccounts();
  const tokenValidationQuery = useFacebookTokenValidation();

  // Mutations
  const callbackMutation = useFacebookAuthCallback();
  const revokeMutation = useFacebookTokenRevoke();

  // Check authentication status on mount and token changes
  useEffect(() => {
    const checkAuth = () => {
      const authenticated = FacebookAuthUtils.isAuthenticated();
      setIsAuthenticated(authenticated);
    };

    checkAuth();
    
    // Listen for storage changes (multi-tab support)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'facebook_access_token') {
        checkAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  /**
   * Start OAuth flow
   */
  const startAuth = useCallback(async (
    redirectUri: string,
    scopes?: string[]
  ) => {
    try {
      const state = FacebookAuthUtils.generateState();
      FacebookAuthUtils.storeState(state);
      setAuthState(state);

      const response = await createFacebookAuthUrl(redirectUri, scopes);
      
      if (response.result?.authUrl) {
        // Redirect to Facebook
        window.location.href = response.result.authUrl;
      }
      
      return response;
    } catch (error) {
      console.error('Failed to start Facebook auth:', error);
      throw error;
    }
  }, []);

  /**
   * Handle OAuth callback
   */
  const handleCallback = useCallback(async (
    code: string,
    state: string,
    redirectUri: string
  ) => {
    try {
      // Validate state
      if (!FacebookAuthUtils.validateState(state)) {
        throw new Error('Invalid OAuth state');
      }

      const response = await callbackMutation.mutateAsync({
        code,
        state,
        redirectUri,
      });

      setIsAuthenticated(true);
      return response;
    } catch (error) {
      console.error('Failed to handle Facebook callback:', error);
      throw error;
    }
  }, [callbackMutation]);

  /**
   * Logout user
   */
  const logout = useCallback(async () => {
    try {
      const token = FacebookAuthUtils.getAccessToken();
      
      if (token) {
        await revokeMutation.mutateAsync(token);
      } else {
        FacebookAuthUtils.clearToken();
      }
      
      setIsAuthenticated(false);
      setAuthState(null);
    } catch (error) {
      console.error('Failed to logout:', error);
      // Clear local data even if API call fails
      FacebookAuthUtils.clearToken();
      setIsAuthenticated(false);
      setAuthState(null);
    }
  }, [revokeMutation]);

  /**
   * Get current user info from cache or storage
   */
  const getCurrentUser = useCallback((): FacebookUserInfo | null => {
    return userInfoQuery.data?.result || FacebookAuthUtils.getUserInfo();
  }, [userInfoQuery.data]);

  /**
   * Get current access token
   */
  const getAccessToken = useCallback((): string | null => {
    return FacebookAuthUtils.getAccessToken();
  }, []);

  return {
    // State
    isAuthenticated,
    authState,
    
    // Data
    user: getCurrentUser(),
    adAccounts: adAccountsQuery.data?.result || [],
    accessToken: getAccessToken(),
    
    // Loading states
    isLoading: userInfoQuery.isLoading || adAccountsQuery.isLoading,
    isValidating: tokenValidationQuery.isLoading,
    isAuthenticating: callbackMutation.isPending,
    isLoggingOut: revokeMutation.isPending,
    
    // Error states
    error: userInfoQuery.error || adAccountsQuery.error || tokenValidationQuery.error,
    authError: callbackMutation.error,
    logoutError: revokeMutation.error,
    
    // Actions
    startAuth,
    handleCallback,
    logout,
    
    // Utilities
    getCurrentUser,
    getAccessToken,
    refetchUser: userInfoQuery.refetch,
    refetchAdAccounts: adAccountsQuery.refetch,
  };
};
