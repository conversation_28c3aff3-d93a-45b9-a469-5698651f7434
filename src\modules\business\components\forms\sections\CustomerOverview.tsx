import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Icon, ProgressBar } from '@/shared/components/common';
import ListOverviewCard from '@/shared/components/widgets/ListOverviewCard/ListOverviewCard';
import { ShoppingCart, TrendingUp, DollarSign, GitBranch, Megaphone, List } from 'lucide-react';
import { CustomerDetailData } from './types';

interface CustomerOverviewProps {
  customer: CustomerDetailData;
}

/**
 * Component hiển thị tổng quan về khách hàng
 */
const CustomerOverview: React.FC<CustomerOverviewProps> = ({ customer }) => {
  const { t } = useTranslation('business');

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  // Get device icon
  const getDeviceIcon = (deviceName: string) => {
    const name = deviceName.toLowerCase();
    if (name.includes('mobile') || name.includes('phone')) return 'smartphone';
    if (name.includes('tablet') || name.includes('ipad')) return 'tablet';
    if (name.includes('desktop') || name.includes('computer')) return 'monitor';
    return 'device-mobile';
  };

  // Get channel icon
  const getChannelIcon = (channelName: string) => {
    const name = channelName.toLowerCase();
    if (name.includes('email')) return 'mail';
    if (name.includes('sms')) return 'message-square';
    if (name.includes('facebook')) return 'facebook';
    if (name.includes('zalo')) return 'message-circle';
    if (name.includes('website')) return 'globe';
    return 'message-square';
  };

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('customer.detail.overview')}
        </Typography>
      }
      defaultOpen={true}
    >
      <div className="space-y-8">
        {/* Mục Doanh thu */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.revenue')}
          </Typography>
          <ListOverviewCard
            items={[
              {
                title: t('customer.detail.totalOrders'),
                value: customer.totalOrders,
                icon: ShoppingCart,
                color: 'blue',
              },
              {
                title: t('customer.detail.averageOrderValue'),
                value: formatCurrency(customer.averageOrderValue),
                icon: TrendingUp,
                color: 'green',
              },
              {
                title: t('customer.detail.totalSpent'),
                value: formatCurrency(customer.totalSpent),
                icon: DollarSign,
                color: 'purple',
              },
            ]}
            maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3 }}
          />
        </div>

        {/* Mục lượt tương tác */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.interactions')}
          </Typography>
          <ListOverviewCard
            items={[
              {
                title: t('customer.overview.flows'),
                value: customer.flowCount || 0,
                icon: GitBranch,
                color: 'purple',
              },
              {
                title: t('customer.overview.campaigns'),
                value: customer.campaignCount || 0,
                icon: Megaphone,
                color: 'orange',
              },
              {
                title: t('customer.overview.sequences'),
                value: customer.sequenceCount || 0,
                icon: List,
                color: 'blue',
              },
            ]}
            maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3 }}
          />
        </div>

        {/* Top kênh nhận tin nhắn */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.topChannels')}
          </Typography>
          {customer.topChannels && customer.topChannels.length > 0 ? (
            <div className="space-y-3">
              {customer.topChannels.slice(0, 5).map((channel) => (
                <div key={channel.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-primary/10">
                      <Icon name={getChannelIcon(channel.name)} size="sm" className="text-primary" />
                    </div>
                    <div>
                      <Typography variant="body1" className="text-foreground font-medium">
                        {channel.name}
                      </Typography>
                      <Typography variant="body2" className="text-muted">
                        {channel.count} {t('customer.overview.messages')}
                      </Typography>
                    </div>
                  </div>
                  <div className="text-right">
                    <Typography variant="body1" className="text-foreground font-medium">
                      {channel.percentage}%
                    </Typography>
                    <div className="w-20 mt-1">
                      <ProgressBar value={channel.percentage} size="sm" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 bg-muted/20 rounded-lg">
              <Icon name="message-circle" size="lg" className="text-muted mx-auto mb-2" />
              <Typography variant="body2" className="text-muted">
                {t('customer.overview.noChannelData')}
              </Typography>
            </div>
          )}
        </div>

        {/* Top thiết bị khách hàng sử dụng */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('customer.overview.topDevices')}
          </Typography>
          {customer.topDevices && customer.topDevices.length > 0 ? (
            <div className="space-y-3">
              {customer.topDevices.slice(0, 5).map((device) => (
                <div key={device.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-info/10">
                      <Icon name={getDeviceIcon(device.name)} size="sm" className="text-info" />
                    </div>
                    <div>
                      <Typography variant="body1" className="text-foreground font-medium">
                        {device.name}
                      </Typography>
                      <Typography variant="body2" className="text-muted">
                        {device.count} {t('customer.overview.sessions')}
                      </Typography>
                    </div>
                  </div>
                  <div className="text-right">
                    <Typography variant="body1" className="text-foreground font-medium">
                      {device.percentage}%
                    </Typography>
                    <div className="w-20 mt-1">
                      <ProgressBar value={device.percentage} size="sm" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 bg-muted/20 rounded-lg">
              <Icon name="monitor" size="lg" className="text-muted mx-auto mb-2" />
              <Typography variant="body2" className="text-muted">
                {t('customer.overview.noDeviceData')}
              </Typography>
            </div>
          )}
        </div>
      </div>
    </CollapsibleCard>
  );
};

export default CustomerOverview;
