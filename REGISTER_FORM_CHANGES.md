# RegisterForm Changes Summary

## Thay đổi đã thực hiện:

### 1. **Cập nhật Axios Interceptor** (`src/shared/api/axios.ts`)
- Thêm header `Accept-Language` từ localStorage với key `'language'`
- Thêm header `X-Theme` từ localStorage với key `'theme-mode'`
- Thêm header `X-Country` từ localStorage với key `'country'`

```typescript
// Thêm language header
const language = localStorage.getItem('language') || 'vi';
if (config.headers) {
  config.headers['Accept-Language'] = language;
}

// Thêm theme header
const theme = localStorage.getItem('theme-mode') || 'light';
if (config.headers) {
  config.headers['X-Theme'] = theme;
}

// Thêm country header
const country = localStorage.getItem('country') || 'VN';
if (config.headers) {
  config.headers['X-Country'] = country;
}
```

### 2. **Cập nhật RegisterForm** (`src/modules/auth/components/RegisterForm.tsx`)
- Import `findCountryByCode` từ countries data
- Khởi tạo `selectedCountry` từ localStorage
- Lưu country vào localStorage khi thay đổi
- Kết hợp country code với phone number khi submit
- Thêm logging để debug phone number processing

```typescript
// State cho country selection
const [selectedCountry, setSelectedCountry] = useState<string>(() => {
  return localStorage.getItem('country') || 'VN';
});

// Handler cho country change
const handleCountryChange = (country: Country) => {
  setSelectedCountry(country.code);
  localStorage.setItem('country', country.code);
};

// Trong submit handler
const selectedCountryData = findCountryByCode(selectedCountry);
const dialCode = selectedCountryData?.dialCode || '+84';

let fullPhoneNumber = registerValues.phone || '';
if (!fullPhoneNumber.startsWith('+')) {
  fullPhoneNumber = `${dialCode}${fullPhoneNumber}`;
}
```

### 3. **Cập nhật Phone Validation Schema** (`src/modules/auth/schemas/auth.schema.ts`)
- Cho phép phone number với hoặc không có country code
- Validation linh hoạt hơn cho các định dạng phone number khác nhau

```typescript
phone: z
  .string()
  .min(1, t('validation.required', { field: t('auth.phone') }))
  .refine((value) => {
    if (!value) return true;
    // Nếu có dấu +, kiểm tra định dạng quốc tế
    if (value.startsWith('+')) {
      return isPossiblePhoneNumber(value);
    }
    // Nếu không có dấu +, kiểm tra định dạng số điện thoại cơ bản
    return /^[0-9]{8,15}$/.test(value);
  }, t('validation.phone'))
```

## Cách hoạt động:

### 1. **Headers được gửi với mọi API request:**
- `Accept-Language`: Ngôn ngữ hiện tại (vi, en, zh)
- `X-Theme`: Theme hiện tại (light, dark, custom)
- `X-Country`: Quốc gia được chọn (VN, US, JP, etc.)

### 2. **Phone number processing:**
- User chọn country từ CountrySelect
- User nhập phone number (không cần country code)
- Khi submit, system tự động kết hợp country code với phone number
- Gửi full phone number (với country code) lên API

### 3. **Data flow:**
```
User Input: "0123456789"
Selected Country: "VN" (+84)
Final Phone: "+840123456789"
API Request: { phoneNumber: "+840123456789" }
```

## Kiểm tra:

1. **Language Header**: Thay đổi ngôn ngữ và kiểm tra Network tab
2. **Theme Header**: Thay đổi theme và kiểm tra Network tab  
3. **Country Header**: Chọn country khác và kiểm tra Network tab
4. **Phone Validation**: Thử nhập phone number với/không có country code
5. **Phone Submission**: Kiểm tra console log để xem phone number processing

## Lưu ý:

- Tất cả headers được thêm tự động cho mọi API request
- Country được lưu vào localStorage khi user thay đổi
- Phone number validation linh hoạt với nhiều định dạng
- System tự động thêm country code nếu chưa có
