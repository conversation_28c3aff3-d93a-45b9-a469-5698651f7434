import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  IconButton,
  Chip,
  SlideInForm
} from '@/shared/components/common';
import StatusIndicator from '../components/indicators/StatusIndicator';
import ProtocolBadge from '../components/indicators/ProtocolBadge';
import CapabilityMatrix from '../components/indicators/CapabilityMatrix';
import AgentStatusCard from '../components/cards/AgentStatusCard';
import ExternalAgentForm from '../components/forms/ExternalAgentForm';
import { formatDateTime, formatRelativeTime } from '../utils';
import { useAgentPerformance, useExternalAgent } from '../hooks/useExternalAgent';
import { useDeleteExternalAgent, useUpdateExternalAgent } from '../hooks/useExternalAgents';
import { useRealTimeStatus } from '../hooks/useRealTimeStatus';
import { useConnectionTester } from '../hooks/useConnectionTest';
import { ExternalAgentUpdateDto, ConnectionTestResult } from '../types/externalAgent';

// Type guard function
const isConnectionTestResult = (obj: unknown): obj is ConnectionTestResult => {
  return typeof obj === 'object' && obj !== null && 'success' in obj;
};

const AgentDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['common', 'external-agents']);

  // State
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);

  // API hooks
  const { data: agent, isLoading, error, refetch } = useExternalAgent(id!);
  const { data: performance } = useAgentPerformance(id!, !!agent);
  const updateMutation = useUpdateExternalAgent();
  const deleteMutation = useDeleteExternalAgent();
  const { testConnection, isTestingConnection, lastTestResult } = useConnectionTester(id!);
  const { isConnected } = useRealTimeStatus(id);

  // Event handlers
  const handleEdit = () => {
    setIsEditFormOpen(true);
  };

  const handleDelete = async () => {
    if (window.confirm(t('external-agents:messages.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id!);
        navigate('/external-agents');
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  const handleTestConnection = async () => {
    if (id) {
      testConnection();
    }
  };

  const handleFormSubmit = async (data: ExternalAgentUpdateDto) => {
    try {
      await updateMutation.mutateAsync({ id: id!, data });
      setIsEditFormOpen(false);
      refetch();
    } catch (error) {
      console.error('Update failed:', error);
    }
  };

  const handleFormCancel = () => {
    setIsEditFormOpen(false);
  };

  const handleBack = () => {
    navigate('/external-agents');
  };

  if (isLoading) {
    return (
      <div className="w-full bg-background text-foreground">
        <div className="p-8 text-center">
          <Typography variant="body1">
            {t('external-agents:loading.agents')}
          </Typography>
        </div>
      </div>
    );
  }

  if (error || !agent) {
    return (
      <div className="w-full bg-background text-foreground">
        <div className="p-8 text-center">
          <Typography variant="h3" className="mb-2">
            {t('external-agents:messages.error.notFound')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            {t('external-agents:messages.error.loadFailed')}
          </Typography>
          <Button variant="primary" onClick={handleBack}>
            {t('external-agents:actions.back')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <IconButton
              icon="arrow-left"
              onClick={handleBack}
              variant="outline"
            />
            <div>
              <Typography variant="h1" className="font-bold">
                {agent.name}
              </Typography>
              <div className="flex items-center gap-2 mt-1">
                <StatusIndicator status={agent.status} />
                <ProtocolBadge protocol={agent.protocol} />
                {isConnected && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <Typography variant="caption" className="text-green-600">
                      {t('common:realTime')}
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleTestConnection}
              disabled={isTestingConnection}
            >
              {isTestingConnection 
                ? t('external-agents:connection.testing')
                : t('external-agents:connection.test')
              }
            </Button>
            <Button variant="outline" onClick={handleEdit}>
              {t('external-agents:actions.edit')}
            </Button>
            <Button
              variant="danger"
              onClick={handleDelete}
              disabled={deleteMutation.isPending}
            >
              {t('external-agents:actions.delete')}
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <div className="p-6">
              <Typography variant="h3" className="mb-4">
                {t('external-agents:agent.basicInfo')}
              </Typography>

              <div className="space-y-4">
                {agent.description && (
                  <div>
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('external-agents:agent.description')}
                    </Typography>
                    <Typography variant="body1">
                      {agent.description}
                    </Typography>
                  </div>
                )}

                <div>
                  <Typography variant="caption" className="text-muted-foreground">
                    {t('external-agents:agent.endpoint')}
                  </Typography>
                  <Typography variant="body1" className="font-mono">
                    {agent.endpoint}
                  </Typography>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('external-agents:agent.created')}
                    </Typography>
                    <Typography variant="body2">
                      {formatDateTime(agent.createdAt)}
                    </Typography>
                  </div>
                  <div>
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('external-agents:agent.updated')}
                    </Typography>
                    <Typography variant="body2">
                      {formatDateTime(agent.updatedAt)}
                    </Typography>
                  </div>
                </div>

                {agent.lastConnectedAt && (
                  <div>
                    <Typography variant="caption" className="text-muted-foreground">
                      {t('external-agents:agent.lastConnected')}
                    </Typography>
                    <Typography variant="body2">
                      {formatRelativeTime(agent.lastConnectedAt)}
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* Capabilities */}
          {agent.capabilities && agent.capabilities.length > 0 && (
            <Card>
              <div className="p-6">
                <Typography variant="h3" className="mb-4">
                  {t('external-agents:agent.capabilities')}
                </Typography>
                <CapabilityMatrix
                  capabilities={agent.capabilities}
                  showUnsupported={false}
                  layout="grid"
                  size="md"
                />
              </div>
            </Card>
          )}

          {/* Tags */}
          {agent.tags && agent.tags.length > 0 && (
            <Card>
              <div className="p-6">
                <Typography variant="h3" className="mb-4">
                  {t('external-agents:agent.tags')}
                </Typography>
                <div className="flex flex-wrap gap-2">
                  {agent.tags.map((tag: string, index: number) => (
                    <Chip key={index} variant="default">
                      {tag}
                    </Chip>
                  ))}
                </div>
              </div>
            </Card>
          )}

          {/* Connection Test Result */}
          {isConnectionTestResult(lastTestResult) && (
            <Card>
              <div className="p-6">
                <Typography variant="h3" className="mb-4">
                  {t('external-agents:connection.lastTest')}
                </Typography>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Typography variant="body2" className="font-medium">
                      {t('external-agents:connection.status')}:
                    </Typography>
                    <Typography
                      variant="body2"
                      className={lastTestResult.success ? 'text-green-600' : 'text-red-600'}
                    >
                      {lastTestResult.success
                        ? t('external-agents:connection.success')
                        : t('external-agents:connection.failed')
                      }
                    </Typography>
                  </div>
                  {lastTestResult.responseTime && (
                    <div className="flex items-center gap-2">
                      <Typography variant="body2" className="font-medium">
                        {t('external-agents:connection.responseTime')}:
                      </Typography>
                      <Typography variant="body2">
                        {lastTestResult.responseTime}ms
                      </Typography>
                    </div>
                  )}
                  {lastTestResult.error && (
                    <div>
                      <Typography variant="body2" className="font-medium text-red-600">
                        {t('external-agents:connection.error')}:
                      </Typography>
                      <Typography variant="body2" className="text-red-600">
                        {lastTestResult.error}
                      </Typography>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Card */}
          <AgentStatusCard
            agentId={agent.id}
            agentName={agent.name}
            status={agent.status}
            performance={performance}
            isRealTime={isConnected}
          />

          {/* Quick Actions */}
          <Card>
            <div className="p-6">
              <Typography variant="h3" className="mb-4">
                {t('external-agents:actions.quickActions')}
              </Typography>
              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => navigate(`/external-agents/${id}/messages`)}
                >
                  {t('external-agents:navigation.messages')}
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => navigate(`/external-agents/${id}/analytics`)}
                >
                  {t('external-agents:navigation.analytics')}
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => navigate(`/external-agents/${id}/testing`)}
                >
                  {t('external-agents:actions.test')}
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Edit Form */}
      <SlideInForm
        isVisible={isEditFormOpen}
      >
        <ExternalAgentForm
          agent={agent}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={updateMutation.isPending}
          mode="edit"
        />
      </SlideInForm>
    </div>
  );
};

export default AgentDetailPage;
