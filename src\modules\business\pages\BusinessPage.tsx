import React from 'react';
import { useTranslation } from 'react-i18next';
import { ResponsiveGrid } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';

/**
 * Trang tổng quan về module Business
 */
const BusinessPage: React.FC = () => {
  const { t } = useTranslation('business');

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Sản phẩm */}
        <ModuleCard
          title={t('product.title')}
          description={t('product.description')}
          icon="box"
          linkTo="/business/product"
        />

        {/* Chuyển đổi */}
        <ModuleCard
          title={t('conversion.title')}
          description={t('conversion.description')}
          icon="refresh-cw"
          linkTo="/business/conversion"
        />

        {/* Đơn hàng */}
        <ModuleCard
          title={t('order.title')}
          description={t('order.description')}
          icon="shopping-cart"
          linkTo="/business/order"
        />

        {/* Khách hàng */}
        <ModuleCard
          title={t('customer.title')}
          description={t('customer.description')}
          icon="users"
          linkTo="/business/customer"
        />

        {/* Báo cáo */}
        <ModuleCard
          title={t('report.title')}
          description={t('report.description')}
          icon="bar-chart"
          linkTo="/business/report"
        />

        {/* Quản lý kho */}
        <ModuleCard
          title={t('inventory.title')}
          description={t('inventory.description')}
          icon="package"
          linkTo="/business/inventory"
        />

        {/* Kho ảo */}
        <ModuleCard
          title={t('virtualWarehouse.title')}
          description={t('virtualWarehouse.description')}
          icon="cloud"
          linkTo="/business/virtual-warehouse"
        />

        {/* Trường tùy chỉnh */}
        <ModuleCard
          title={t('customField.title')}
          description={t('customField.description')}
          icon="database"
          linkTo="/business/custom-field"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default BusinessPage;
