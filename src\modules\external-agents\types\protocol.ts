import { ProtocolType, AuthenticationType } from './enums';

export interface ProtocolConfig {
  type: ProtocolType;
  version?: string;
  specifications?: Record<string, unknown>;
  supportedMethods?: string[];
  requiredHeaders?: string[];
  optionalHeaders?: string[];
  authenticationMethods?: AuthenticationType[];
}

export interface ProtocolTemplate {
  id: string;
  name: string;
  description?: string;
  protocol: ProtocolType;
  config: ProtocolConfig;
  isDefault: boolean;
  isPublic: boolean;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProtocolDetectionResult {
  detectedProtocol?: ProtocolType;
  confidence: number;
  supportedFeatures: string[];
  recommendations: string[];
  errors?: string[];
}

export interface ProtocolValidationResult {
  isValid: boolean;
  errors?: string[];
  warnings?: string[];
  suggestions?: string[];
}

export interface WebhookConfig {
  id: string;
  name: string;
  url: string;
  events: string[];
  isActive: boolean;
  secret?: string;
  headers?: Record<string, string>;
  retryPolicy?: {
    maxRetries: number;
    retryDelay: number;
    backoffMultiplier: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  event: string;
  payload: Record<string, unknown>;
  status: 'pending' | 'success' | 'failed' | 'retrying';
  responseCode?: number;
  responseBody?: string;
  attempts: number;
  nextRetryAt?: string;
  createdAt: string;
  deliveredAt?: string;
}
