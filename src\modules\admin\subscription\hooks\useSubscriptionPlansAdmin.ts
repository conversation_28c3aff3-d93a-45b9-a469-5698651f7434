import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import {
  getSubscriptionPlans,
  getSubscriptionPlanDetail,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
} from '../services/subscription-plans.admin.service';
import {
  GetSubscriptionPlansQueryDto,
  CreateSubscriptionPlanDto,
  UpdateSubscriptionPlanDto,
} from '../types/subscription-plans.admin.types';
import { SUBSCRIPTION_PLANS_ADMIN_QUERY_KEYS } from '../constants/subscription-plans.admin.query-keys';

/**
 * Interface cho error response từ API
 */
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}

/**
 * Hook để lấy danh sách gói dịch vụ
 */
export const useGetSubscriptionPlans = (params?: GetSubscriptionPlansQueryDto) => {
  return useQuery({
    queryKey: [SUBSCRIPTION_PLANS_ADMIN_QUERY_KEYS.SUBSCRIPTION_PLANS, params],
    queryFn: () => getSubscriptionPlans(params),
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để lấy chi tiết gói dịch vụ
 */
export const useGetSubscriptionPlanDetail = (id: number, enabled = true) => {
  return useQuery({
    queryKey: [SUBSCRIPTION_PLANS_ADMIN_QUERY_KEYS.SUBSCRIPTION_PLAN_DETAIL, id],
    queryFn: () => getSubscriptionPlanDetail(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để tạo gói dịch vụ mới
 */
export const useCreateSubscriptionPlan = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (data: CreateSubscriptionPlanDto) => createSubscriptionPlan(data),
    onSuccess: (response) => {
      // Invalidate và refetch danh sách gói dịch vụ
      queryClient.invalidateQueries({
        queryKey: [SUBSCRIPTION_PLANS_ADMIN_QUERY_KEYS.SUBSCRIPTION_PLANS],
      });

      success({
        title: 'Thành công',
        message: response.message || 'Tạo gói dịch vụ thành công',
      });
    },
    onError: (errorResponse: unknown) => {
      const errorMessage = (errorResponse as ApiError)?.response?.data?.message || 'Có lỗi xảy ra khi tạo gói dịch vụ';
      error({
        title: 'Lỗi',
        message: errorMessage,
      });
    },
  });
};

/**
 * Hook để cập nhật gói dịch vụ
 */
export const useUpdateSubscriptionPlan = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateSubscriptionPlanDto }) =>
      updateSubscriptionPlan(id, data),
    onSuccess: (response, { id }) => {
      // Invalidate và refetch danh sách gói dịch vụ
      queryClient.invalidateQueries({
        queryKey: [SUBSCRIPTION_PLANS_ADMIN_QUERY_KEYS.SUBSCRIPTION_PLANS],
      });

      // Invalidate và refetch chi tiết gói dịch vụ
      queryClient.invalidateQueries({
        queryKey: [SUBSCRIPTION_PLANS_ADMIN_QUERY_KEYS.SUBSCRIPTION_PLAN_DETAIL, id],
      });

      success({
        title: 'Thành công',
        message: response.message || 'Cập nhật gói dịch vụ thành công',
      });
    },
    onError: (errorResponse: unknown) => {
      const errorMessage = (errorResponse as ApiError)?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật gói dịch vụ';
      error({
        title: 'Lỗi',
        message: errorMessage,
      });
    },
  });
};

/**
 * Hook để xóa gói dịch vụ
 */
export const useDeleteSubscriptionPlan = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  return useMutation({
    mutationFn: (id: number) => deleteSubscriptionPlan(id),
    onSuccess: (response) => {
      // Invalidate và refetch danh sách gói dịch vụ
      queryClient.invalidateQueries({
        queryKey: [SUBSCRIPTION_PLANS_ADMIN_QUERY_KEYS.SUBSCRIPTION_PLANS],
      });

      success({
        title: 'Thành công',
        message: response.message || 'Xóa gói dịch vụ thành công',
      });
    },
    onError: (errorResponse: unknown) => {
      const errorMessage = (errorResponse as ApiError)?.response?.data?.message || 'Có lỗi xảy ra khi xóa gói dịch vụ';
      error({
        title: 'Lỗi',
        message: errorMessage,
      });
    },
  });
};
