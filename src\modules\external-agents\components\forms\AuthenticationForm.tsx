import React from 'react';
import { useTranslation } from 'react-i18next';
import { FormItem, Input, Select, Typography } from '@/shared/components/common';
import { AuthenticationType, AuthenticationConfig } from '../../types';
import { getAuthTypeLabel } from '../../utils';

interface AuthenticationFormProps {
  value: AuthenticationConfig;
  onChange: (value: AuthenticationConfig) => void;
  className?: string;
}

const AuthenticationForm: React.FC<AuthenticationFormProps> = ({
  value,
  onChange,
  className,
}) => {
  const { t } = useTranslation(['external-agents']);

  const authTypeOptions = Object.values(AuthenticationType).map(type => ({
    value: type,
    label: getAuthTypeLabel(type),
  }));

  const handleTypeChange = (type: AuthenticationType) => {
    onChange({
      ...value,
      type,
      credentials: {},
      headers: {},
      parameters: {},
    });
  };

  const handleCredentialChange = (key: string, credentialValue: string) => {
    onChange({
      ...value,
      credentials: {
        ...value.credentials,
        [key]: credentialValue,
      },
    });
  };

  const handleHeaderChange = (key: string, headerValue: string) => {
    onChange({
      ...value,
      headers: {
        ...value.headers,
        [key]: headerValue,
      },
    });
  };

  const renderCredentialFields = () => {
    switch (value.type) {
      case AuthenticationType.API_KEY:
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('external-agents:form.apiKey')} name="apiKey" required>
              <Input
                type="password"
                value={value.credentials?.apiKey || ''}
                onChange={(e) => handleCredentialChange('apiKey', e.target.value)}
                placeholder={t('external-agents:form.placeholder.apiKey')}

              />
            </FormItem>
            <FormItem label={t('external-agents:form.headerName')} name="headerName">
              <Input
                value={value.headers?.['X-API-Key'] ? 'X-API-Key' : (Object.keys(value.headers || {})[0] || '')}
                onChange={(e) => {
                  const oldKey = Object.keys(value.headers || {})[0];
                  const newHeaders = { ...value.headers };
                  if (oldKey) delete newHeaders[oldKey];
                  if (e.target.value) newHeaders[e.target.value] = value.credentials?.apiKey || '';
                  onChange({ ...value, headers: newHeaders });
                }}
                placeholder="X-API-Key"
              />
            </FormItem>
          </div>
        );

      case AuthenticationType.BEARER_TOKEN:
        return (
          <FormItem label={t('external-agents:form.bearerToken')} name="token" required>
            <Input
              type="password"
              value={value.credentials?.token || ''}
              onChange={(e) => handleCredentialChange('token', e.target.value)}
              placeholder={t('external-agents:form.placeholder.token')}
            />
          </FormItem>
        );

      case AuthenticationType.BASIC_AUTH:
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('external-agents:form.username')} name="username" required>
              <Input
                value={value.credentials?.username || ''}
                onChange={(e) => handleCredentialChange('username', e.target.value)}
                placeholder={t('external-agents:form.placeholder.username')}
              />
            </FormItem>
            <FormItem label={t('external-agents:form.password')} name="password" required>
              <Input
                type="password"
                value={value.credentials?.password || ''}
                onChange={(e) => handleCredentialChange('password', e.target.value)}
                placeholder={t('external-agents:form.placeholder.password')}
              />
            </FormItem>
          </div>
        );

      case AuthenticationType.OAUTH2:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem label={t('external-agents:form.clientId')} name="clientId" required>
                <Input
                  value={value.credentials?.clientId || ''}
                  onChange={(e) => handleCredentialChange('clientId', e.target.value)}
                  placeholder={t('external-agents:form.placeholder.clientId')}
                />
              </FormItem>
              <FormItem label={t('external-agents:form.clientSecret')} name="clientSecret" required>
                <Input
                  type="password"
                  value={value.credentials?.clientSecret || ''}
                  onChange={(e) => handleCredentialChange('clientSecret', e.target.value)}
                  placeholder={t('external-agents:form.placeholder.clientSecret')}
                />
              </FormItem>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem label={t('external-agents:form.authUrl')} name="authUrl">
                <Input
                  value={value.credentials?.authUrl || ''}
                  onChange={(e) => handleCredentialChange('authUrl', e.target.value)}
                  placeholder="https://auth.example.com/oauth/authorize"
                />
              </FormItem>
              <FormItem label={t('external-agents:form.tokenUrl')} name="tokenUrl">
                <Input
                  value={value.credentials?.tokenUrl || ''}
                  onChange={(e) => handleCredentialChange('tokenUrl', e.target.value)}
                  placeholder="https://auth.example.com/oauth/token"
                />
              </FormItem>
            </div>
          </div>
        );

      case AuthenticationType.CUSTOM:
        return (
          <div className="space-y-4">
            <Typography variant="body2" className="text-muted-foreground">
              {t('external-agents:form.customAuthDescription')}
            </Typography>
            <FormItem label={t('external-agents:form.customCredentials')} name="customCredentials">
              <textarea
                className="w-full p-3 border rounded-md bg-background"
                rows={4}
                value={JSON.stringify(value.credentials || {}, null, 2)}
                onChange={(e) => {
                  try {
                    const credentials = JSON.parse(e.target.value);
                    onChange({ ...value, credentials });
                  } catch {
                    // Invalid JSON, ignore
                  }
                }}
                placeholder='{"key": "value"}'
              />
            </FormItem>
          </div>
        );

      default:
        return (
          <Typography variant="body2" className="text-muted-foreground py-4">
            {t('external-agents:form.noAuthRequired')}
          </Typography>
        );
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <FormItem label={t('external-agents:form.authenticationType')} name="authType" required>
        <Select
          value={value.type}
          onChange={(value) => handleTypeChange(value as AuthenticationType)}
          options={authTypeOptions}
        />
      </FormItem>

      {renderCredentialFields()}

      {/* Additional Headers */}
      {value.type !== AuthenticationType.NONE && (
        <div className="pt-4 border-t">
          <Typography variant="h4" className="mb-3">
            {t('external-agents:form.additionalHeaders')}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mb-3">
            {t('external-agents:form.additionalHeadersDescription')}
          </Typography>
          
          {Object.entries(value.headers || {}).map(([key, headerValue], index) => (
            <div key={index} className="grid grid-cols-2 gap-2 mb-2">
              <Input
                value={key}
                onChange={(e) => {
                  const newHeaders = { ...value.headers };
                  delete newHeaders[key];
                  if (e.target.value) newHeaders[e.target.value] = headerValue;
                  onChange({ ...value, headers: newHeaders });
                }}
                placeholder={t('external-agents:form.headerName')}
              />
              <Input
                value={headerValue}
                onChange={(e) => handleHeaderChange(key, e.target.value)}
                placeholder={t('external-agents:form.headerValue')}
              />
            </div>
          ))}
          
          <button
            type="button"
            onClick={() => handleHeaderChange('', '')}
            className="text-sm text-primary hover:underline"
          >
            {t('external-agents:form.addHeader')}
          </button>
        </div>
      )}
    </div>
  );
};

export default AuthenticationForm;
