import { webhookApi } from '../api';
import {
  WebhookConfig,
  WebhookDelivery,
  PaginatedResponse,
} from '../types';

export const webhookService = {
  // Get webhooks with validation
  getWebhooks: async (params?: { page?: number; limit?: number }): Promise<PaginatedResponse<WebhookConfig>> => {
    const queryParams = {
      page: 1,
      limit: 20,
      ...params,
    };

    if (queryParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return webhookApi.getWebhooks(queryParams);
  },

  // Get webhook by ID
  getWebhook: async (id: string): Promise<WebhookConfig> => {
    if (!id || id.trim() === '') {
      throw new Error('Webhook ID is required');
    }

    return webhookApi.getWebhook(id);
  },

  // Create webhook with validation
  createWebhook: async (data: Omit<WebhookConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<WebhookConfig> => {
    // Validate required fields
    if (!data.name || data.name.trim() === '') {
      throw new Error('Webhook name is required');
    }

    if (!data.url || data.url.trim() === '') {
      throw new Error('Webhook URL is required');
    }

    // Validate URL format
    try {
      new URL(data.url);
    } catch {
      throw new Error('Invalid webhook URL format');
    }

    // Validate events
    if (!data.events || data.events.length === 0) {
      throw new Error('At least one event must be selected');
    }

    // Set default retry policy if not provided
    const webhookData = {
      ...data,
      retryPolicy: data.retryPolicy || {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
      },
    };

    return webhookApi.createWebhook(webhookData);
  },

  // Update webhook with validation
  updateWebhook: async (id: string, data: Partial<WebhookConfig>): Promise<WebhookConfig> => {
    if (!id || id.trim() === '') {
      throw new Error('Webhook ID is required');
    }

    // Validate URL if provided
    if (data.url) {
      try {
        new URL(data.url);
      } catch {
        throw new Error('Invalid webhook URL format');
      }
    }

    // Validate events if provided
    if (data.events && data.events.length === 0) {
      throw new Error('At least one event must be selected');
    }

    return webhookApi.updateWebhook(id, data);
  },

  // Delete webhook
  deleteWebhook: async (id: string): Promise<void> => {
    if (!id || id.trim() === '') {
      throw new Error('Webhook ID is required');
    }

    return webhookApi.deleteWebhook(id);
  },

  // Get webhook deliveries
  getWebhookDeliveries: async (
    webhookId: string,
    params?: { page?: number; limit?: number }
  ): Promise<PaginatedResponse<WebhookDelivery>> => {
    if (!webhookId || webhookId.trim() === '') {
      throw new Error('Webhook ID is required');
    }

    const queryParams = {
      page: 1,
      limit: 50,
      ...params,
    };

    if (queryParams.limit > 200) {
      throw new Error('Limit cannot exceed 200');
    }

    return webhookApi.getWebhookDeliveries(webhookId, queryParams);
  },

  // Retry webhook delivery
  retryWebhookDelivery: async (deliveryId: string): Promise<void> => {
    if (!deliveryId || deliveryId.trim() === '') {
      throw new Error('Delivery ID is required');
    }

    return webhookApi.retryWebhookDelivery(deliveryId);
  },

  // Test webhook with timeout
  testWebhook: async (id: string, timeout = 30000): Promise<{ success: boolean; error?: string }> => {
    if (!id || id.trim() === '') {
      throw new Error('Webhook ID is required');
    }

    try {
      const result = await Promise.race([
        webhookApi.testWebhook(id),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Webhook test timeout')), timeout)
        ),
      ]);

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  // Helper methods
  getActiveWebhooks: async (): Promise<WebhookConfig[]> => {
    const result = await webhookService.getWebhooks({ limit: 100 });
    return result.items.filter(webhook => webhook.isActive);
  },

  getWebhooksByEvent: async (event: string): Promise<WebhookConfig[]> => {
    const result = await webhookService.getWebhooks({ limit: 100 });
    return result.items.filter(webhook => 
      webhook.isActive && webhook.events.includes(event)
    );
  },

  getFailedDeliveries: async (webhookId: string): Promise<WebhookDelivery[]> => {
    const result = await webhookService.getWebhookDeliveries(webhookId, { limit: 100 });
    return result.items.filter(delivery => delivery.status === 'failed');
  },
};
