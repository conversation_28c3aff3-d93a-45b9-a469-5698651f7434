/**
 * Enterprise Storage Integration Types (AWS S3, Azure Blob, etc.)
 */

import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Enterprise Storage Provider Types
 */
export type EnterpriseStorageProviderType = 'aws-s3' | 'azure-blob' | 'google-cloud-storage' | 'minio';

/**
 * Enterprise Storage Provider Configuration
 */
export interface EnterpriseStorageProviderConfiguration {
  id: number;
  userId: number;
  providerType: EnterpriseStorageProviderType;
  providerName: string;
  accessKey: string;
  secretKey: string;
  region?: string;
  endpoint?: string;
  bucketName: string;
  isActive: boolean;
  isDefault: boolean;
  settings?: EnterpriseStorageSettings;
  createdAt: string;
  updatedAt: string;
}

/**
 * Provider-specific settings
 */
export interface EnterpriseStorageSettings {
  // AWS S3 settings
  storageClass?: 'STANDARD' | 'REDUCED_REDUNDANCY' | 'STANDARD_IA' | 'ONEZONE_IA' | 'INTELLIGENT_TIERING' | 'GLACIER' | 'DEEP_ARCHIVE';
  serverSideEncryption?: 'AES256' | 'aws:kms';
  kmsKeyId?: string;
  
  // Azure Blob settings
  accountName?: string;
  containerName?: string;
  accessTier?: 'Hot' | 'Cool' | 'Archive';
  
  // Google Cloud Storage settings
  projectId?: string;
  keyFilePath?: string;
  
  // Common settings
  enableVersioning?: boolean;
  enableLogging?: boolean;
  maxFileSize?: number;
  allowedFileTypes?: string[];
  publicRead?: boolean;
  cdnEnabled?: boolean;
  cdnDomain?: string;
}

/**
 * Create Enterprise Storage Provider DTO
 */
export interface CreateEnterpriseStorageProviderDto {
  providerType: EnterpriseStorageProviderType;
  providerName: string;
  accessKey: string;
  secretKey: string;
  region?: string;
  endpoint?: string;
  bucketName: string;
  isActive: boolean;
  isDefault: boolean;
  settings?: EnterpriseStorageSettings;
}

/**
 * Update Enterprise Storage Provider DTO
 */
export interface UpdateEnterpriseStorageProviderDto {
  providerName?: string;
  accessKey?: string;
  secretKey?: string;
  region?: string;
  endpoint?: string;
  bucketName?: string;
  isActive?: boolean;
  isDefault?: boolean;
  settings?: EnterpriseStorageSettings;
}

/**
 * Test Enterprise Storage Provider DTO
 */
export interface TestEnterpriseStorageProviderDto {
  testFileName?: string;
  testFileContent?: string;
}

/**
 * Enterprise Storage Provider Config for testing
 */
export interface EnterpriseStorageProviderConfigDto {
  providerType: EnterpriseStorageProviderType;
  providerName: string;
  accessKey: string;
  secretKey: string;
  region?: string;
  endpoint?: string;
  bucketName: string;
  settings?: EnterpriseStorageSettings;
}

/**
 * Test Enterprise Storage Provider with config DTO
 */
export interface TestEnterpriseStorageProviderWithConfigDto {
  storageConfig: EnterpriseStorageProviderConfigDto;
  testInfo: TestEnterpriseStorageProviderDto;
}

/**
 * Query parameters for Enterprise Storage Providers
 */
export interface EnterpriseStorageProviderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  providerType?: EnterpriseStorageProviderType;
  isActive?: boolean;
}

/**
 * Enterprise Storage Provider test result
 */
export interface EnterpriseStorageProviderTestResult {
  success: boolean;
  message: string;
  details?: {
    error?: string;
    statusCode?: number;
    responseTime?: number;
    storageInfo?: {
      bucketName: string;
      region: string;
      storageClass: string;
      versioning: boolean;
      encryption: boolean;
    };
  };
}

/**
 * Form data for Enterprise Storage Provider
 */
export interface EnterpriseStorageProviderFormData extends Omit<CreateEnterpriseStorageProviderDto, 'settings'> {
  settings?: string; // JSON string for form handling
}

/**
 * Storage Object
 */
export interface StorageObject {
  key: string;
  name: string;
  size: number;
  lastModified: string;
  etag: string;
  storageClass?: string;
  contentType?: string;
  metadata?: Record<string, string>;
  url?: string;
  signedUrl?: string;
  isPublic?: boolean;
}

/**
 * Storage Bucket
 */
export interface StorageBucket {
  name: string;
  region: string;
  creationDate: string;
  versioning?: boolean;
  encryption?: boolean;
  publicAccess?: boolean;
  objectCount?: number;
  totalSize?: number;
}

/**
 * Enterprise Storage File Upload Request
 */
export interface EnterpriseFileUploadRequest {
  fileName: string;
  fileContent: File | Blob;
  contentType?: string;
  metadata?: Record<string, string>;
  storageClass?: string;
  publicRead?: boolean;
}

/**
 * File Upload Response
 */
export interface FileUploadResponse {
  key: string;
  url: string;
  signedUrl?: string;
  etag: string;
  size: number;
  contentType: string;
}

/**
 * Presigned URL Request
 */
export interface PresignedUrlRequest {
  key: string;
  operation: 'GET' | 'PUT' | 'DELETE';
  expiresIn?: number;
  contentType?: string;
}

/**
 * Presigned URL Response
 */
export interface PresignedUrlResponse {
  url: string;
  expiresAt: string;
  fields?: Record<string, string>;
}

/**
 * Enterprise Storage Batch Operation Request
 */
export interface EnterpriseBatchOperationRequest {
  operation: 'copy' | 'move' | 'delete';
  objects: Array<{
    sourceKey: string;
    targetKey?: string;
  }>;
}

/**
 * Storage Analytics
 */
export interface StorageAnalytics {
  totalObjects: number;
  totalSize: number;
  storageClasses: Record<string, {
    objectCount: number;
    totalSize: number;
  }>;
  monthlyUsage: Array<{
    month: string;
    objectCount: number;
    totalSize: number;
    requests: number;
    dataTransfer: number;
  }>;
}

/**
 * Storage Lifecycle Rule
 */
export interface StorageLifecycleRule {
  id: string;
  status: 'Enabled' | 'Disabled';
  filter?: {
    prefix?: string;
    tags?: Record<string, string>;
  };
  transitions?: Array<{
    days: number;
    storageClass: string;
  }>;
  expiration?: {
    days: number;
  };
}

/**
 * Storage CORS Configuration
 */
export interface StorageCorsConfiguration {
  allowedOrigins: string[];
  allowedMethods: string[];
  allowedHeaders: string[];
  exposedHeaders?: string[];
  maxAgeSeconds?: number;
}
