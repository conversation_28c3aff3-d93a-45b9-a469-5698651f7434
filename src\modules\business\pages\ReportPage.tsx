import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Tabs, Typography } from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import {
  useReportOverview,
  useTopSellingProducts,
  usePotentialCustomers
} from '../hooks/useReportQuery';
import {
  ReportPeriodEnum,
  TopSellingProductDto,
  PotentialCustomerDto
} from '../types/report.types';

/**
 * Trang báo cáo kinh doanh
 */
const ReportPage: React.FC = () => {
  const { t } = useTranslation('business');
  const [activeTab, setActiveTab] = useState('sales');

  // Query parameters for reports
  const reportParams = useMemo(() => ({
    period: ReportPeriodEnum.MONTH,
  }), []);

  // API calls
  const { data: overviewData, isLoading: overviewLoading } = useReportOverview(reportParams);
  const { data: topProductsData, isLoading: topProductsLoading } = useTopSellingProducts({
    ...reportParams,
    limit: 10,
  });
  const { data: potentialCustomersData, isLoading: potentialCustomersLoading } = usePotentialCustomers({
    ...reportParams,
    limit: 10,
  });

  // Định dạng tiền tệ
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  // Định dạng số
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  // Dữ liệu cho overview cards
  const overviewCards = useMemo(() => {
    if (!overviewData) {
      return [
        {
          title: t('report.totalRevenue', 'Tổng doanh thu'),
          value: '--',
          description: t('report.loading', 'Đang tải...'),
          color: 'blue' as const,
        },
        {
          title: t('report.totalOrders', 'Tổng đơn hàng'),
          value: '--',
          description: t('report.loading', 'Đang tải...'),
          color: 'green' as const,
        },
        {
          title: t('report.newCustomers', 'Khách hàng mới'),
          value: '--',
          description: t('report.loading', 'Đang tải...'),
          color: 'orange' as const,
        },
      ];
    }

    return [
      {
        title: t('report.totalRevenue', 'Tổng doanh thu'),
        value: formatCurrency(overviewData.totalRevenue),
        description: `${overviewData.startDate} - ${overviewData.endDate}`,
        color: 'blue' as const,
      },
      {
        title: t('report.totalOrders', 'Tổng đơn hàng'),
        value: formatNumber(overviewData.totalOrders),
        description: `${overviewData.startDate} - ${overviewData.endDate}`,
        color: 'green' as const,
      },
      {
        title: t('report.newCustomers', 'Khách hàng mới'),
        value: formatNumber(overviewData.newCustomers),
        description: `${overviewData.startDate} - ${overviewData.endDate}`,
        color: 'orange' as const,
      },
    ];
  }, [overviewData, t]);

  return (
    <div className="space-y-6">
      <ListOverviewCard
        items={overviewCards}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        gap={6}
        isLoading={overviewLoading}
      />

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'sales',
              label: t('report.tabs.sales', 'Doanh thu'),
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      {t(
                        'report.charts.salesPlaceholder',
                        'Biểu đồ doanh thu sẽ được hiển thị ở đây'
                      )}
                    </Typography>
                  </div>
                </div>
              ),
            },
            {
              key: 'orders',
              label: t('report.tabs.orders', 'Đơn hàng'),
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      {t(
                        'report.charts.ordersPlaceholder',
                        'Biểu đồ đơn hàng sẽ được hiển thị ở đây'
                      )}
                    </Typography>
                  </div>
                </div>
              ),
            },
            {
              key: 'customers',
              label: t('report.tabs.customers', 'Khách hàng'),
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      {t(
                        'report.charts.customersPlaceholder',
                        'Biểu đồ khách hàng sẽ được hiển thị ở đây'
                      )}
                    </Typography>
                  </div>
                </div>
              ),
            },
            {
              key: 'products',
              label: t('report.tabs.products', 'Sản phẩm'),
              children: (
                <div className="p-4">
                  <div className="h-80 flex items-center justify-center">
                    <Typography variant="h5" className="text-muted-foreground">
                      {t(
                        'report.charts.productsPlaceholder',
                        'Biểu đồ sản phẩm sẽ được hiển thị ở đây'
                      )}
                    </Typography>
                  </div>
                </div>
              ),
            },
          ]}
        />
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
        <Card title={t('report.topSellingProducts', 'Sản phẩm bán chạy')}>
          <div className="p-4">
            {topProductsLoading ? (
              <div className="h-60 flex items-center justify-center">
                <Typography variant="h5" className="text-muted-foreground">
                  {t('report.loading', 'Đang tải...')}
                </Typography>
              </div>
            ) : topProductsData?.products && topProductsData.products.length > 0 ? (
              <div className="space-y-3">
                {topProductsData.products.slice(0, 5).map((product: TopSellingProductDto, index: number) => (
                  <div key={product.id} className="flex items-center justify-between p-2 border-b">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                      <div>
                        <Typography variant="body2" className="font-medium">
                          {product.name}
                        </Typography>
                        <Typography variant="caption" className="text-muted-foreground">
                          SKU: {product.sku}
                        </Typography>
                      </div>
                    </div>
                    <div className="text-right">
                      <Typography variant="body2" className="font-medium">
                        {formatCurrency(product.revenue)}
                      </Typography>
                      <Typography variant="caption" className="text-muted-foreground">
                        {formatNumber(product.quantity)} sản phẩm
                      </Typography>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-60 flex items-center justify-center">
                <Typography variant="h5" className="text-muted-foreground">
                  {t('report.noData', 'Không có dữ liệu')}
                </Typography>
              </div>
            )}
          </div>
        </Card>

        <Card title={t('report.potentialCustomers', 'Khách hàng tiềm năng')}>
          <div className="p-4">
            {potentialCustomersLoading ? (
              <div className="h-60 flex items-center justify-center">
                <Typography variant="h5" className="text-muted-foreground">
                  {t('report.loading', 'Đang tải...')}
                </Typography>
              </div>
            ) : potentialCustomersData?.customers && potentialCustomersData.customers.length > 0 ? (
              <div className="space-y-3">
                {potentialCustomersData.customers.slice(0, 5).map((customer: PotentialCustomerDto, index: number) => (
                  <div key={customer.id} className="flex items-center justify-between p-2 border-b">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-muted-foreground">#{index + 1}</span>
                      <div>
                        <Typography variant="body2" className="font-medium">
                          {customer.name}
                        </Typography>
                        <Typography variant="caption" className="text-muted-foreground">
                          {customer.email}
                        </Typography>
                      </div>
                    </div>
                    <div className="text-right">
                      <Typography variant="body2" className="font-medium">
                        {formatCurrency(customer.totalRevenue)}
                      </Typography>
                      <Typography variant="caption" className="text-muted-foreground">
                        {formatNumber(customer.totalOrders)} đơn hàng
                      </Typography>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-60 flex items-center justify-center">
                <Typography variant="h5" className="text-muted-foreground">
                  {t('report.noData', 'Không có dữ liệu')}
                </Typography>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ReportPage;
