import React, { useState } from 'react';
import {
  Typography,
  CountrySelect,
  Card,
  ResponsiveGrid,
  Form,
  FormItem,
  Button
} from '@/shared/components/common';
import { ComponentDemo } from '../components';
import { Country } from '@/shared/data/countries';

/**
 * Demo page cho CountrySelect component
 */
const CountrySelectPage: React.FC = () => {
  const [selectedCountry1, setSelectedCountry1] = useState<string>('VN');
  const [selectedCountry2, setSelectedCountry2] = useState<string>('');
  const [selectedCountry3, setSelectedCountry3] = useState<string>('US');
  const [formData, setFormData] = useState({ country: 'VN' });

  const handleCountryChange1 = (country: Country) => {
    setSelectedCountry1(country.code);
  };

  const handleCountryChange2 = (country: Country) => {
    setSelectedCountry2(country.code);
  };

  const handleCountryChange3 = (country: Country) => {
    setSelectedCountry3(country.code);
  };

  const handleFormCountryChange = (country: Country) => {
    setFormData({ ...formData, country: country.code });
  };

  const basicExample = `import { CountrySelect } from '@/shared/components/common';
import { Country } from '@/shared/data/countries';

const [selectedCountry, setSelectedCountry] = useState<string>('VN');

const handleCountryChange = (country: Country) => {
  setSelectedCountry(country.code);
};

<CountrySelect
  value={selectedCountry}
  onChange={handleCountryChange}
  placeholder="Chọn quốc gia"
/>`;

  const sizesExample = `<CountrySelect
  value={selectedCountry}
  onChange={handleCountryChange}
  size="sm"
  placeholder="Small size"
/>

<CountrySelect
  value={selectedCountry}
  onChange={handleCountryChange}
  size="md"
  placeholder="Medium size (default)"
/>

<CountrySelect
  value={selectedCountry}
  onChange={handleCountryChange}
  size="lg"
  placeholder="Large size"
/>`;

  const compactExample = `// Compact mode - trigger nhỏ gọn, dropdown vẫn rộng
<CountrySelect
  value={selectedCountry}
  onChange={handleCountryChange}
  compact={true}
  size="md"
/>

// Normal mode - trigger và dropdown cùng width
<CountrySelect
  value={selectedCountry}
  onChange={handleCountryChange}
  compact={false}
  placeholder="Chọn quốc gia"
  fullWidth
/>`;

  const formExample = `<Form onSubmit={handleSubmit}>
  <FormItem label="Quốc gia" name="country" required>
    <CountrySelect
      value={formData.country}
      onChange={handleFormCountryChange}
      fullWidth
    />
  </FormItem>

  <Button type="submit" variant="primary">
    Gửi
  </Button>
</Form>`;

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-8">
        {/* Header */}
        <div>
          <Typography variant="h1" className="mb-2">
            CountrySelect Component
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            Component chọn quốc gia với dropdown hiển thị flag, tên quốc gia và mã quốc gia.
            Hỗ trợ tìm kiếm và sử dụng system scroll.
          </Typography>
        </div>

        {/* Basic Usage */}
        <ComponentDemo
          title="Sử dụng cơ bản"
          description="CountrySelect hiển thị flag icon khi đã chọn, dropdown hiển thị đầy đủ thông tin"
          code={basicExample}
        >
          <div className="w-full max-w-md">
            <CountrySelect
              value={selectedCountry1}
              onChange={handleCountryChange1}
              placeholder="Chọn quốc gia"
            />
            <div className="mt-4 p-3 bg-muted rounded-md">
              <Typography variant="body2">
                Quốc gia đã chọn: <strong>{selectedCountry1 || 'Chưa chọn'}</strong>
              </Typography>
            </div>
          </div>
        </ComponentDemo>

        {/* Different Sizes */}
        <ComponentDemo
          title="Kích thước khác nhau"
          description="CountrySelect với các kích thước: small, medium, large"
          code={sizesExample}
        >
          <div className="w-full max-w-md space-y-4">
            <div>
              <Typography variant="body2" className="mb-2 font-medium">Small</Typography>
              <CountrySelect
                value={selectedCountry2}
                onChange={handleCountryChange2}
                size="sm"
                placeholder="Small size"
              />
            </div>

            <div>
              <Typography variant="body2" className="mb-2 font-medium">Medium (Default)</Typography>
              <CountrySelect
                value={selectedCountry2}
                onChange={handleCountryChange2}
                size="md"
                placeholder="Medium size"
              />
            </div>

            <div>
              <Typography variant="body2" className="mb-2 font-medium">Large</Typography>
              <CountrySelect
                value={selectedCountry2}
                onChange={handleCountryChange2}
                size="lg"
                placeholder="Large size"
              />
            </div>
          </div>
        </ComponentDemo>

        {/* Compact Mode */}
        <ComponentDemo
          title="Compact Mode"
          description="Trigger button nhỏ gọn chỉ hiển thị flag icon, dropdown menu vẫn rộng đầy đủ thông tin"
          code={compactExample}
        >
          <div className="w-full space-y-4">
            <div>
              <Typography variant="body2" className="mb-2 font-medium">Compact Sizes</Typography>
              <div className="flex items-center gap-4">
                <div className="text-center">
                  <Typography variant="caption" className="block mb-2">Small</Typography>
                  <CountrySelect
                    value={selectedCountry3}
                    onChange={handleCountryChange3}
                    size="sm"
                    compact={true}
                  />
                </div>
                <div className="text-center">
                  <Typography variant="caption" className="block mb-2">Medium</Typography>
                  <CountrySelect
                    value={selectedCountry3}
                    onChange={handleCountryChange3}
                    size="md"
                    compact={true}
                  />
                </div>
                <div className="text-center">
                  <Typography variant="caption" className="block mb-2">Large</Typography>
                  <CountrySelect
                    value={selectedCountry3}
                    onChange={handleCountryChange3}
                    size="lg"
                    compact={true}
                  />
                </div>
              </div>
            </div>

            <div>
              <Typography variant="body2" className="mb-2 font-medium">Compact vs Normal</Typography>
              <div className="flex items-start gap-8">
                <div>
                  <Typography variant="caption" className="block mb-2">Compact Mode</Typography>
                  <CountrySelect
                    value={selectedCountry3}
                    onChange={handleCountryChange3}
                    compact={true}
                  />
                  <Typography variant="caption" className="block mt-2 text-muted-foreground max-w-32">
                    Trigger nhỏ gọn, dropdown rộng 320px
                  </Typography>
                </div>
                <div>
                  <Typography variant="caption" className="block mb-2">Normal Mode</Typography>
                  <div className="w-64">
                    <CountrySelect
                      value={selectedCountry3}
                      onChange={handleCountryChange3}
                      compact={false}
                      placeholder="Chọn quốc gia"
                      fullWidth
                    />
                  </div>
                  <Typography variant="caption" className="block mt-2 text-muted-foreground max-w-64">
                    Trigger và dropdown cùng width container
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </ComponentDemo>

        {/* States */}
        <ComponentDemo
          title="Trạng thái khác nhau"
          description="CountrySelect với các trạng thái: normal, disabled, error"
        >
          <ResponsiveGrid maxColumns={{ xs: 1, md: 3 }} className="w-full">
            <div>
              <Typography variant="body2" className="mb-2 font-medium">Normal</Typography>
              <CountrySelect
                value={selectedCountry3}
                onChange={handleCountryChange3}
                placeholder="Normal state"
                fullWidth
              />
            </div>
            
            <div>
              <Typography variant="body2" className="mb-2 font-medium">Disabled</Typography>
              <CountrySelect
                value={selectedCountry3}
                onChange={handleCountryChange3}
                placeholder="Disabled state"
                disabled
                fullWidth
              />
            </div>
            
            <div>
              <Typography variant="body2" className="mb-2 font-medium">Error</Typography>
              <CountrySelect
                value=""
                onChange={handleCountryChange3}
                placeholder="Error state"
                error
                fullWidth
              />
            </div>
          </ResponsiveGrid>
        </ComponentDemo>

        {/* Form Integration */}
        <ComponentDemo
          title="Tích hợp với Form"
          description="Sử dụng CountrySelect trong form với validation"
          code={formExample}
        >
          <Card className="w-full max-w-md">
            <Form onSubmit={(data) => console.log('Form data:', data)}>
              <FormItem label="Quốc gia" name="country" required>
                <CountrySelect
                  value={formData.country}
                  onChange={handleFormCountryChange}
                  fullWidth
                />
              </FormItem>
              
              <FormItem label="Ghi chú" name="note">
                <input
                  type="text"
                  className="w-full h-10 px-3 border-0 dark:border rounded-md bg-card-muted text-foreground dark:border-border"
                  placeholder="Nhập ghi chú..."
                />
              </FormItem>
              
              <div className="flex justify-end">
                <Button type="submit" variant="primary">
                  Gửi
                </Button>
              </div>
            </Form>
          </Card>
        </ComponentDemo>

        {/* SVG vs Emoji Flags */}
        <ComponentDemo
          title="SVG vs Emoji Flags"
          description="So sánh hiển thị giữa SVG flags và emoji flags"
        >
          <ResponsiveGrid maxColumns={{ xs: 1, md: 2 }} className="w-full">
            <Card>
              <Typography variant="h4" className="mb-4">
                SVG Flags (Mặc định)
              </Typography>
              <div className="space-y-3">
                <CountrySelect
                  value="VN"
                  onChange={() => {}}
                  placeholder="SVG flags"
                  fullWidth
                />
                <div className="text-sm text-muted-foreground">
                  ✅ Hiển thị rõ nét trên mọi thiết bị<br/>
                  ✅ Tải nhanh và responsive<br/>
                  ✅ Fallback về emoji nếu lỗi
                </div>
              </div>
            </Card>

            <Card>
              <Typography variant="h4" className="mb-4">
                Emoji Flags (Fallback)
              </Typography>
              <div className="space-y-3">
                <div className="flex items-center gap-2 p-3 border rounded-md">
                  <span className="text-lg">🇻🇳</span>
                  <span>Vietnam</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  ⚠️ Có thể hiển thị khác nhau trên các hệ thống<br/>
                  ⚠️ Một số thiết bị không hỗ trợ<br/>
                  ✅ Không cần tải từ server
                </div>
              </div>
            </Card>
          </ResponsiveGrid>
        </ComponentDemo>

        {/* Features */}
        <Card>
          <Typography variant="h3" className="mb-4">
            Tính năng
          </Typography>
          <ResponsiveGrid maxColumns={{ xs: 1, md: 2 }}>
            <div>
              <Typography variant="h4" className="mb-2">
                Hiển thị
              </Typography>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• SVG flags ưu tiên, fallback về emoji</li>
                <li>• Compact mode: trigger nhỏ gọn, dropdown rộng 320px</li>
                <li>• Normal mode: trigger và dropdown cùng width</li>
                <li>• Trong menu: Flag + tên quốc gia + mã quốc gia</li>
                <li>• Mã điện thoại (+84, +1, etc.)</li>
                <li>• Không có border, chuẩn theme hệ thống</li>
              </ul>
            </div>

            <div>
              <Typography variant="h4" className="mb-2">
                Tương tác
              </Typography>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Tìm kiếm theo tên hoặc mã</li>
                <li>• System scroll với autohide</li>
                <li>• Keyboard navigation</li>
                <li>• Click outside để đóng</li>
                <li>• Lazy loading cho SVG flags</li>
              </ul>
            </div>
          </ResponsiveGrid>
        </Card>
      </div>
    </div>
  );
};

export default CountrySelectPage;
