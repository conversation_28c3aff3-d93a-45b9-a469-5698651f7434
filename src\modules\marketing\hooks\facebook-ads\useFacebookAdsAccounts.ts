import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  FacebookAdsAccountDto,
  FacebookAdsAccountQueryDto,
  CreateFacebookAdsAccountDto,
  UpdateFacebookAdsAccountDto,
} from '../../types/facebook-ads.types';
import {
  getFacebookAdsAccounts,
  getFacebookAdsAccount,
  createFacebookAdsAccount,
  updateFacebookAdsAccount,
  deleteFacebookAdsAccount,
  syncFacebookAdsAccount,
} from '../../services/facebook-ads.service';

/**
 * Facebook Ads Accounts Hooks
 * React Query hooks cho quản lý tài khoản Facebook Ads
 */

// Query Keys
export const FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS = {
  ALL: ['facebook-ads-accounts'] as const,
  LIST: (params: FacebookAdsAccountQueryDto) => [...FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (accountId: string) => [...FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.ALL, 'detail', accountId] as const,
} as const;

/**
 * Hook để lấy danh sách tài khoản Facebook Ads
 */
export const useFacebookAdsAccounts = (
  params?: FacebookAdsAccountQueryDto,
  options?: Omit<UseQueryOptions<ApiResponseDto<PaginatedResult<FacebookAdsAccountDto>>>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.LIST(params || {}),
    queryFn: () => getFacebookAdsAccounts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy chi tiết tài khoản Facebook Ads
 */
export const useFacebookAdsAccount = (
  accountId: string,
  options?: Omit<UseQueryOptions<ApiResponseDto<FacebookAdsAccountDto>>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.DETAIL(accountId),
    queryFn: () => getFacebookAdsAccount(accountId),
    enabled: !!accountId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để tạo tài khoản Facebook Ads mới
 */
export const useCreateFacebookAdsAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFacebookAdsAccountDto) => createFacebookAdsAccount(data),
    onSuccess: () => {
      // Invalidate accounts list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để cập nhật tài khoản Facebook Ads
 */
export const useUpdateFacebookAdsAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ accountId, data }: { accountId: string; data: UpdateFacebookAdsAccountDto }) =>
      updateFacebookAdsAccount(accountId, data),
    onSuccess: (_, variables) => {
      // Invalidate accounts list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.ALL,
      });
      // Invalidate specific account detail
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.DETAIL(variables.accountId),
      });
    },
  });
};

/**
 * Hook để xóa tài khoản Facebook Ads
 */
export const useDeleteFacebookAdsAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (accountId: string) => deleteFacebookAdsAccount(accountId),
    onSuccess: (_, accountId) => {
      // Invalidate accounts list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.ALL,
      });
      // Remove specific account detail from cache
      queryClient.removeQueries({
        queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.DETAIL(accountId),
      });
    },
  });
};

/**
 * Hook để đồng bộ tài khoản Facebook Ads
 */
export const useSyncFacebookAdsAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (accountId: string) => syncFacebookAdsAccount(accountId),
    onSuccess: (_, accountId) => {
      // Invalidate accounts list
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.ALL,
      });
      // Invalidate specific account detail
      queryClient.invalidateQueries({
        queryKey: FACEBOOK_ADS_ACCOUNTS_QUERY_KEYS.DETAIL(accountId),
      });
    },
  });
};

/**
 * Hook tổng hợp cho quản lý tài khoản Facebook Ads
 */
export const useFacebookAdsAccountManagement = (params?: FacebookAdsAccountQueryDto) => {
  const accountsQuery = useFacebookAdsAccounts(params);
  const createMutation = useCreateFacebookAdsAccount();
  const updateMutation = useUpdateFacebookAdsAccount();
  const deleteMutation = useDeleteFacebookAdsAccount();
  const syncMutation = useSyncFacebookAdsAccount();

  return {
    // Data
    accounts: accountsQuery.data?.result?.items || [],
    totalItems: accountsQuery.data?.result?.meta?.totalItems || 0,
    
    // Loading states
    isLoading: accountsQuery.isLoading,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isSyncing: syncMutation.isPending,
    
    // Error states
    error: accountsQuery.error,
    createError: createMutation.error,
    updateError: updateMutation.error,
    deleteError: deleteMutation.error,
    syncError: syncMutation.error,
    
    // Actions
    createAccount: createMutation.mutate,
    updateAccount: updateMutation.mutate,
    deleteAccount: deleteMutation.mutate,
    syncAccount: syncMutation.mutate,
    refetch: accountsQuery.refetch,
    
    // Success handlers (deprecated in TanStack Query v5)
    // Use useEffect to handle success in components instead
  };
};

/**
 * Hook để lấy thống kê tài khoản Facebook Ads
 */
export const useFacebookAdsAccountStats = (accountId?: string) => {
  const accountQuery = useFacebookAdsAccount(accountId || '', {
    enabled: !!accountId,
  });

  const stats = accountQuery.data?.result ? {
    balance: accountQuery.data.result.currency === 'VND' 
      ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(0) // TODO: Get actual balance
      : '$0',
    spendCap: accountQuery.data.result.currency === 'VND'
      ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(0) // TODO: Get actual spend cap
      : '$0',
    status: accountQuery.data.result.status,
    currency: accountQuery.data.result.currency,
    timezone: accountQuery.data.result.timezone,
  } : null;

  return {
    stats,
    isLoading: accountQuery.isLoading,
    error: accountQuery.error,
  };
};
