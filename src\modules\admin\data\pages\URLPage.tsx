import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Chip,
  IconCard,
  ActionMenu,
  ActionMenuItem,
} from '@/shared/components/common';
import { ConfirmDeleteModal } from '@/modules/admin/marketplace/components/modals';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { URLForm, CrawlUrlForm } from '@/modules/admin/data/components/forms';
import { Tooltip as CustomTooltip } from '@/shared/components/common';

// Import hooks từ module URL
import {
  useUrlsQuery,
  useCreateUrlMutation,
  useUpdateUrlMutation,
  useDeleteUrlMutation,
  useToggleUrlStatusMutation,
  useCrawlUrlMutation,
} from '@/modules/admin/data/url/hooks/useUrlQuery';

// Import types từ module URL
import {
  Url,
  UrlSearchParams,
  CreateUrlParams,
  UpdateUrlParams,
  CrawlUrlParams,
} from '@/modules/admin/data/url/types/url.types';
import { formatDate } from '@/shared/utils/format';

/**
 * Trang quản lý URL
 */
const URLPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [urls, setUrls] = useState<Url[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [urlToDelete, setUrlToDelete] = useState<Url | null>(null);
  const [urlToView, setUrlToView] = useState<Url | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  const [crawlParams, setCrawlParams] = useState<CrawlUrlParams>({
    url: '',
    depth: 1,
    maxUrls: 100,
    ignoreRobotsTxt: false,
  });

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditSlideForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form crawl URL
  const {
    isVisible: isCrawlFormVisible,
    showForm: showCrawlForm,
    hideForm: hideCrawlForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<UrlSearchParams>(() => {
    const params: UrlSearchParams = {
      page: currentPage,
      limit: itemsPerPage,
      keyword: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // Hooks để gọi API
  const { data: urlsData, isLoading: isLoadingUrls, error: urlsError } = useUrlsQuery(queryParams);

  const { mutateAsync: createUrl } = useCreateUrlMutation();
  const { mutateAsync: updateUrl } = useUpdateUrlMutation();
  const { mutateAsync: deleteUrl } = useDeleteUrlMutation();
  const { mutateAsync: toggleUrlStatus } = useToggleUrlStatusMutation();
  const { mutateAsync: crawlUrl } = useCrawlUrlMutation();

  // Xử lý submit form tạo URL
  const handleSubmitCreateUrl = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        // Chuẩn bị dữ liệu cho API
        const urlData: CreateUrlParams = {
          url: values.url as string,
          title: values.title as string,
          content: values.content as string,
          type: values.type as string,
          tags: values.tags as string[],
          ownedBy: Number(values.ownedBy),
          isActive: values.isActive as boolean,
        };

        // Gọi API tạo URL
        await createUrl(urlData);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating URL:', error);
      }
    },
    [createUrl, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa URL
  const handleSubmitEditUrl = useCallback(
    async (values: Record<string, unknown>) => {
      if (!urlToView) return;

      try {
        // Chuẩn bị dữ liệu cho API
        const urlData: UpdateUrlParams = {
          url: values.url as string,
          title: values.title as string,
          content: values.content as string,
          type: values.type as string,
          tags: values.tags as string[],
          ownedBy: Number(values.ownedBy),
          isActive: values.isActive as boolean,
        };

        // Gọi API cập nhật URL
        await updateUrl({ id: urlToView.id, urlData });
        hideEditForm();
        setUrlToView(null);
      } catch (error) {
        console.error('Error updating URL:', error);
      }
    },
    [updateUrl, hideEditForm, urlToView]
  );

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (urlsData) {
      setUrls(urlsData.items);
      setTotalItems(urlsData.meta.totalItems);
    }
  }, [urlsData, urlsError]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((url: Url) => {
    setUrlToDelete(url);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setUrlToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!urlToDelete) return;

    try {
      await deleteUrl([urlToDelete.id]); // Truyền mảng ids
      setShowDeleteConfirm(false);
      setUrlToDelete(null);
    } catch (error) {
      console.error('Error deleting URLs:', error);
    }
  }, [urlToDelete, deleteUrl]);

  // Xử lý xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      await deleteUrl(selectedRowKeys as string[]);
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('Error deleting URLs:', error);
    }
  }, [selectedRowKeys, deleteUrl]);

  // Xử lý đảo ngược trạng thái kích hoạt
  const handleToggleStatus = useCallback(
    async (url: Url) => {
      try {
        await toggleUrlStatus(url.id);
      } catch (error) {
        console.error('Error toggling URL status:', error);
      }
    },
    [toggleUrlStatus]
  );

  // Xử lý hiển thị form crawl URL
  const handleShowCrawlForm = useCallback(() => {
    setCrawlParams({
      url: '',
      depth: 1,
      maxUrls: 100,
      ignoreRobotsTxt: false,
    });
    showCrawlForm();
  }, [showCrawlForm]);

  // Xử lý thay đổi tham số crawl
  const handleCrawlParamsChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setCrawlParams(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value,
    }));
  }, []);

  // Xử lý xác nhận crawl
  const handleConfirmCrawl = useCallback(async () => {
    try {
      await crawlUrl(crawlParams);
      hideCrawlForm();
    } catch (error) {
      console.error('Error crawling URL:', error);
    }
  }, [crawlParams, crawlUrl, hideCrawlForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form chỉnh sửa URL
  const handleShowEditForm = useCallback(
    (url: Url) => {
      setUrlToView(url);
      showEditSlideForm();
    },
    [showEditSlideForm]
  );

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'url',
        title: t('admin:data.url.table.url', 'URL'),
        dataIndex: 'url',
        width: '20%',
        sortable: true,
        render: (value: unknown) => (
          <CustomTooltip content={t('common.copy', 'Sao chép')}>
            <IconCard
              icon="copy"
              variant="default"
              size="sm"
              className="ml-2"
              onClick={() => {
                navigator.clipboard.writeText(String(value || ''));
              }}
            />
          </CustomTooltip>
        ),
      },
      {
        key: 'title',
        title: t('admin:data.url.table.title', 'Tiêu đề'),
        dataIndex: 'title',
        width: '20%',
        sortable: true,
      },
      {
        key: 'type',
        title: t('admin:data.url.table.type', 'Loại'),
        dataIndex: 'type',
        width: '10%',
        sortable: true,
        render: (value: unknown) => (
          <Chip size="sm" variant="default">
            {(value as string) || 'N/A'}
          </Chip>
        ),
      },

      {
        key: 'isActive',
        title: t('admin:data.url.table.status', 'Trạng thái'),
        dataIndex: 'isActive',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const isActive = value as boolean;
          return (
            <Chip
              size="sm"
              variant={isActive ? 'success' : 'danger'}
              className="whitespace-nowrap min-w-0 flex-shrink-0"
            >
              <span className="truncate">
                {isActive
                  ? t('admin:data.common.active', 'Kích hoạt')
                  : t('admin:data.common.inactive', 'Vô hiệu')}
              </span>
            </Chip>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('admin:data.url.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: 'actions',
        title: t('', ''),
        width: '120px',
        render: (_: unknown, record: Url) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common.edit', 'Sửa'),
              icon: 'edit',
              onClick: () => handleShowEditForm(record),
            },
            {
              id: 'toggle',
              label: record.isActive
                ? t('common.deactivate', 'Vô hiệu')
                : t('common.activate', 'Kích hoạt'),
              icon: record.isActive ? 'toggle-off' : 'toggle-on',
              onClick: () => handleToggleStatus(record),
            },
            {
              id: 'delete',
              label: t('common.delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [t, visibleColumns, handleShowDeleteConfirm, handleShowEditForm, handleToggleStatus]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: 'Tất cả', visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={true}
            showColumnFilter={true}
            items={[
              {
                id: 'crawl',
                label: t('admin:data.url.crawl', 'Crawl URL'),
                icon: 'spider',
                onClick: handleShowCrawlForm,
              },
            ]}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common:bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: handleShowBulkDeleteConfirm,
                className: 'text-red-500',
                condition: selectedRowKeys.length > 0,
              },
            ]}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <URLForm onSubmit={handleSubmitCreateUrl} onCancel={hideCreateForm} />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {urlToView && (
            <URLForm
              initialValues={urlToView}
              onSubmit={handleSubmitEditUrl}
              onCancel={hideEditForm}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form crawl URL */}
        <SlideInForm isVisible={isCrawlFormVisible}>
          <CrawlUrlForm
            crawlParams={crawlParams}
            onParamsChange={handleCrawlParamsChange}
            onSubmit={handleConfirmCrawl}
            onCancel={hideCrawlForm}
          />
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<Url>
            columns={filteredColumns}
            data={urls}
            rowKey="id"
            loading={isLoadingUrls}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={handleSortChange}
            defaultSort={{
              column: sortBy || '',
              order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:data.common.confirmDelete', 'Xác nhận xóa')}
        message={t('admin:data.url.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa URL này?')}
        itemName={urlToDelete?.url}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={t(
          'admin:data.url.confirmBulkDeleteMessage',
          `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} URL đã chọn?`
        )}
      />
    </div>
  );
};

export default URLPage;
