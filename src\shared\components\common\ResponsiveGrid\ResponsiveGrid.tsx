import React, { ReactNode, useMemo } from 'react';
import { useChatPanel } from '@/shared/contexts';
import { useMediaQuery } from '@/shared/hooks/common';

export interface ResponsiveGridProps {
  /**
   * <PERSON><PERSON><PERSON> phần tử con sẽ được hiển thị trong grid
   */
  children: ReactNode;

  /**
   * Khoảng cách giữa các phần tử trong grid
   * @default 4
   */
  gap?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Số cột tối đa cho mỗi breakpoint khi chatpanel đóng
   * @default { xs: 1, sm: 2, md: 3, lg: 4, xl: 4 }
   */
  maxColumns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Số cột tối đa cho mỗi breakpoint khi chatpanel mở
   * @default { xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }
   */
  maxColumnsWithChatPanel?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };

  /**
   * Class name bổ sung cho grid container
   */
  className?: string;

  /**
   * Callback được gọi khi số cột thay đổi
   */
  onColumnsChange?: (columns: number) => void;
}

/**
 * Component hiển thị grid responsive tự động điều chỉnh số cột dựa trên kích thước màn hình và trạng thái chatpanel
 *
 * Mặc định:
 * - Mobile (<640px): 1 cột
 * - Small Tablet (640px-767px): 1-2 cột (tùy thuộc vào trạng thái chatpanel)
 * - Tablet (768px-1023px): 2-3 cột (tùy thuộc vào trạng thái chatpanel)
 * - Desktop (1024px-1279px): 3-4 cột (tùy thuộc vào trạng thái chatpanel)
 * - Large Desktop (≥1280px): 3-4 cột (tùy thuộc vào trạng thái chatpanel)
 */
const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  gap = 4,
  maxColumns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 4 },
  maxColumnsWithChatPanel = { xs: 1, sm: 1, md: 2, lg: 3, xl: 3 },
  className = '',
  onColumnsChange,
}) => {
  const { isChatPanelOpen } = useChatPanel();
  const isSm = useMediaQuery('(min-width: 640px) and (max-width: 767px)');
  const isMd = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const isLg = useMediaQuery('(min-width: 1024px) and (max-width: 1279px)');
  const isXl = useMediaQuery('(min-width: 1280px)');

  // Xác định số cột dựa trên kích thước màn hình và trạng thái chatpanel
  const getGridColumns = useMemo(() => {
    const config = isChatPanelOpen ? maxColumnsWithChatPanel : maxColumns;

    if (isXl) {
      const cols = config.xl || 4;
      if (onColumnsChange) onColumnsChange(cols);
      return `grid-cols-${cols}`;
    }

    if (isLg) {
      const cols = config.lg || 4;
      if (onColumnsChange) onColumnsChange(cols);
      return `grid-cols-${cols}`;
    }

    if (isMd) {
      const cols = config.md || 3;
      if (onColumnsChange) onColumnsChange(cols);
      return `grid-cols-${cols}`;
    }

    if (isSm) {
      const cols = config.sm || 2;
      if (onColumnsChange) onColumnsChange(cols);
      return `grid-cols-${cols}`;
    }

    // Default for xs
    const cols = config.xs || 1;
    if (onColumnsChange) onColumnsChange(cols);
    return `grid-cols-${cols}`;
  }, [
    isChatPanelOpen,
    maxColumns,
    maxColumnsWithChatPanel,
    isXl,
    isLg,
    isMd,
    isSm,
    onColumnsChange,
  ]);

  // Xác định khoảng cách giữa các phần tử
  const gapClass = useMemo(() => {
    if (typeof gap === 'number') {
      return `gap-${gap}`;
    }

    const gapClasses = [];
    if (gap.xs !== undefined) gapClasses.push(`gap-${gap.xs}`);
    if (gap.sm !== undefined) gapClasses.push(`sm:gap-${gap.sm}`);
    if (gap.md !== undefined) gapClasses.push(`md:gap-${gap.md}`);
    if (gap.lg !== undefined) gapClasses.push(`lg:gap-${gap.lg}`);
    if (gap.xl !== undefined) gapClasses.push(`xl:gap-${gap.xl}`);

    return gapClasses.join(' ');
  }, [gap]);

  const finalClassName = useMemo(() => {
    return `grid ${getGridColumns} ${gapClass} ${className}`;
  }, [getGridColumns, gapClass, className]);

  return <div className={finalClassName}>{children}</div>;
};

export default ResponsiveGrid;
