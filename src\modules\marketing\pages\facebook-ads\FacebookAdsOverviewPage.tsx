import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Icon,
  ResponsiveGrid,
  EmptyState,
} from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import FacebookAuthButton from '../../components/facebook-ads/FacebookAuthButton';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';

/**
 * Facebook Ads Overview Page
 * Trang tổng quan Facebook Ads với dashboard metrics
 */
const FacebookAdsOverviewPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();
  const { isAuthenticated } = useFacebookAuth();

  // Mock data cho demo
  const overviewStats = useMemo(() => [
    {
      title: t('marketing:facebookAds.stats.totalCampaigns', 'Tổng chiến dịch'),
      value: '12',
      change: '+2',
      changeType: 'increase' as const,
      icon: 'campaign',
    },
    {
      title: t('marketing:facebookAds.stats.totalSpend', 'Tổng chi phí'),
      value: '₫2,450,000',
      change: '+15%',
      changeType: 'increase' as const,
      icon: 'dollar-sign',
    },
    {
      title: t('marketing:facebookAds.stats.impressions', 'Lượt hiển thị'),
      value: '125,430',
      change: '+8%',
      changeType: 'increase' as const,
      icon: 'eye',
    },
    {
      title: t('marketing:facebookAds.stats.clicks', 'Lượt nhấp'),
      value: '3,240',
      change: '+12%',
      changeType: 'increase' as const,
      icon: 'mouse-pointer',
    },
    {
      title: t('marketing:facebookAds.stats.ctr', 'Tỷ lệ nhấp (CTR)'),
      value: '2.58%',
      change: '+0.3%',
      changeType: 'increase' as const,
      icon: 'trending-up',
    },
    {
      title: t('marketing:facebookAds.stats.cpc', 'Chi phí/nhấp (CPC)'),
      value: '₫756',
      change: '-5%',
      changeType: 'decrease' as const,
      icon: 'target',
    },
  ], [t]);

  const quickActions = useMemo(() => [
    {
      title: t('marketing:facebookAds.actions.createCampaign', 'Tạo chiến dịch'),
      description: t('marketing:facebookAds.actions.createCampaignDesc', 'Tạo chiến dịch quảng cáo mới'),
      icon: 'plus',
      action: () => navigate('/marketing/facebook-ads/campaigns/create'),
      disabled: !isAuthenticated,
    },
    {
      title: t('marketing:facebookAds.actions.manageAccounts', 'Quản lý tài khoản'),
      description: t('marketing:facebookAds.actions.manageAccountsDesc', 'Kết nối và quản lý tài khoản Facebook'),
      icon: 'settings',
      action: () => navigate('/marketing/facebook-ads/accounts'),
    },
    {
      title: t('marketing:facebookAds.actions.viewReports', 'Xem báo cáo'),
      description: t('marketing:facebookAds.actions.viewReportsDesc', 'Phân tích hiệu suất chiến dịch'),
      icon: 'bar-chart',
      action: () => navigate('/marketing/facebook-ads/analytics'),
      disabled: !isAuthenticated,
    },
    {
      title: t('marketing:facebookAds.actions.manageCampaigns', 'Quản lý chiến dịch'),
      description: t('marketing:facebookAds.actions.manageCampaignsDesc', 'Xem và quản lý các chiến dịch hiện có'),
      icon: 'campaign',
      action: () => navigate('/marketing/facebook-ads/campaigns'),
      disabled: !isAuthenticated,
    },
  ], [t, isAuthenticated, navigate]);

  return (
    <div className="w-full bg-background text-foreground">
      <MarketingViewHeader
        title={t('marketing:facebookAds.overview.title', 'Facebook Ads')}
        description={t('marketing:facebookAds.overview.description', 'Quản lý và tối ưu hóa chiến dịch quảng cáo Facebook')}
        icon="facebook"
        actions={isAuthenticated ? <FacebookAuthButton showUserInfo /> : undefined}
      />

      {!isAuthenticated ? (
        <Card className="p-8 text-center">
          <EmptyState
            icon="facebook"
            title={t('marketing:facebookAds.notConnected.title', 'Chưa kết nối Facebook')}
            description={t('marketing:facebookAds.notConnected.description', 'Kết nối tài khoản Facebook để bắt đầu quản lý chiến dịch quảng cáo')}
            actions={
              <FacebookAuthButton
                variant="primary"
                size="lg"
                className="mt-4"
              />
            }
          />
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Overview Stats */}
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('marketing:facebookAds.overview.stats', 'Thống kê tổng quan')}
              </Typography>
              <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}>
                {overviewStats.map((stat, index) => (
                  <Card key={index} variant="default" className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Typography variant="body2" className="text-muted-foreground mb-1">
                          {stat.title}
                        </Typography>
                        <Typography variant="h4" className="font-bold">
                          {stat.value}
                        </Typography>
                        <div className="flex items-center mt-1">
                          <Icon
                            name={stat.changeType === 'increase' ? 'trending-up' : 'trending-down'}
                            size="sm"
                            className={stat.changeType === 'increase' ? 'text-green-500' : 'text-red-500'}
                          />
                          <Typography
                            variant="caption"
                            className={`ml-1 ${stat.changeType === 'increase' ? 'text-green-500' : 'text-red-500'}`}
                          >
                            {stat.change}
                          </Typography>
                        </div>
                      </div>
                      <Icon name={stat.icon} size="lg" className="text-primary" />
                    </div>
                  </Card>
                ))}
              </ResponsiveGrid>
            </div>
          </Card>

          {/* Quick Actions */}
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('marketing:facebookAds.overview.quickActions', 'Thao tác nhanh')}
              </Typography>
              <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
                {quickActions.map((action, index) => (
                  <Card
                    key={index}
                    variant="bordered"
                    className={`p-4 cursor-pointer transition-colors hover:bg-accent ${
                      action.disabled ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    onClick={action.disabled ? undefined : action.action}
                  >
                    <div className="flex items-start space-x-3">
                      <Icon name={action.icon} size="lg" className="text-primary mt-1" />
                      <div>
                        <Typography variant="h6" className="mb-1">
                          {action.title}
                        </Typography>
                        <Typography variant="body2" className="text-muted-foreground">
                          {action.description}
                        </Typography>
                      </div>
                    </div>
                  </Card>
                ))}
              </ResponsiveGrid>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default FacebookAdsOverviewPage;
