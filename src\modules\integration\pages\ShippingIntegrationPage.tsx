import React, { useState } from 'react';
import { SlideInForm, Button, Icon } from '@/shared/components/common';
import ShippingProviderList from '../shipping/components/ShippingProviderList';
import ShippingProviderForm from '../shipping/components/ShippingProviderForm';
import ShippingRateCalculator from '../shipping/components/ShippingRateCalculator';
import { ShippingProviderConfiguration } from '../shipping/types';

/**
 * Trang quản lý tích hợp vận chuyển
 */
const ShippingIntegrationPage: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProvider, setEditingProvider] = useState<ShippingProviderConfiguration | null>(null);
  const [showRateCalculator, setShowRateCalculator] = useState(false);

  const handleCreateNew = () => {
    setEditingProvider(null);
    setShowCreateForm(true);
  };

  const handleEdit = (provider: ShippingProviderConfiguration) => {
    setEditingProvider(provider);
    setShowCreateForm(true);
  };

  const handleTest = (provider: ShippingProviderConfiguration) => {
    console.log('Testing provider:', provider);
    // TODO: Implement test connection logic
  };

  const handleToggleStatus = (provider: ShippingProviderConfiguration) => {
    console.log('Toggling status for provider:', provider);
    // TODO: Implement toggle status logic
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  const handleCreateCancel = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Rate Calculator */}
      {showRateCalculator ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => setShowRateCalculator(false)}
              leftIcon={<Icon name="arrow-left" size="sm" />}
            >
              Back to Providers
            </Button>
          </div>
          <ShippingRateCalculator onClose={() => setShowRateCalculator(false)} />
        </div>
      ) : (
        <>
          {/* Providers List */}
          <ShippingProviderList
            onCreateNew={handleCreateNew}
            onEdit={handleEdit}
            onTest={handleTest}
            onToggleStatus={handleToggleStatus}
          />

          {/* Rate Calculator Button */}
          <div className="mt-6 flex justify-center">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setShowRateCalculator(true)}
              leftIcon={<Icon name="calculator" size="sm" />}
            >
              Rate Calculator
            </Button>
          </div>
        </>
      )}

      {/* Create/Edit Form Slide-in */}
      <SlideInForm
        isVisible={showCreateForm}
      >
        <ShippingProviderForm
          provider={editingProvider}
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default ShippingIntegrationPage;
