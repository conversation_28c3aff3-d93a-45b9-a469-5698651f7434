import React, { useState } from 'react';
import { SlideInForm } from '@/shared/components/common';
import ShippingProviderList from '../shipping/components/ShippingProviderList';
import ShippingProviderForm from '../shipping/components/ShippingProviderForm';
import { ShippingProviderConfiguration } from '../shipping/types';

/**
 * Trang quản lý tích hợp vận chuyển
 */
const ShippingIntegrationPage: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProvider, setEditingProvider] = useState<ShippingProviderConfiguration | null>(null);

  const handleCreateNew = () => {
    setEditingProvider(null);
    setShowCreateForm(true);
  };

  const handleEdit = (provider: ShippingProviderConfiguration) => {
    setEditingProvider(provider);
    setShowCreateForm(true);
  };

  const handleTest = (provider: ShippingProviderConfiguration) => {
    console.log('Testing provider:', provider);
    // TODO: Implement test connection logic
  };

  const handleToggleStatus = (provider: ShippingProviderConfiguration) => {
    console.log('Toggling status for provider:', provider);
    // TODO: Implement toggle status logic
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  const handleCreateCancel = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <ShippingProviderList
        onCreateNew={handleCreateNew}
        onEdit={handleEdit}
        onTest={handleTest}
        onToggleStatus={handleToggleStatus}
      />

      {/* Create/Edit Form Slide-in */}
      <SlideInForm
        isVisible={showCreateForm}
      >
        <ShippingProviderForm
          provider={editingProvider}
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default ShippingIntegrationPage;
