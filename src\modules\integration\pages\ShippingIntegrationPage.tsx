import React, { useState } from 'react';
import { SlideInForm } from '@/shared/components/common';
import ShippingProviderList from '../shipping/components/ShippingProviderList';
import ShippingProviderForm from '../shipping/components/ShippingProviderForm';

/**
 * Trang quản lý tích hợp vận chuyển
 */
const ShippingIntegrationPage: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
  };

  const handleCreateCancel = () => {
    setShowCreateForm(false);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <ShippingProviderList onCreateNew={handleCreateNew} />

      {/* Create Form Slide-in */}
      <SlideInForm
        isVisible={showCreateForm}
      >
        <ShippingProviderForm
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default ShippingIntegrationPage;
