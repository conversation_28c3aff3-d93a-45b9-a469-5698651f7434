import { z } from 'zod';
import { ProductCategory } from '../types/product.types';

// Create a custom schema that properly handles string to number conversion
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const createAddProductSchema = (t: any) => z.object({
  name: z
    .string()
    .min(3, t('admin:marketplace.product.validation.nameMin', 'Tên sản phẩm phải có ít nhất 3 ký tự'))
    .max(500, t('admin:marketplace.product.validation.nameMax', 'Tên sản phẩm không được vượt quá 500 ký tự')),
  description: z.string().min(1, t('admin:marketplace.product.validation.descriptionRequired', '<PERSON><PERSON> tả sản phẩm là bắt buộc')),
  listedPrice: z
    .string()
    .min(1, t('admin:marketplace.product.validation.listedPriceRequired', '<PERSON><PERSON><PERSON> niêm yết là bắt buộc'))
    .transform((val) => Number(val))
    .pipe(z.number().min(0, t('admin:marketplace.product.validation.listedPriceMin', '<PERSON><PERSON><PERSON> niêm yết không được âm'))),
  discountedPrice: z
    .string()
    .min(1, t('admin:marketplace.product.validation.discountedPriceRequired', 'Giá khuyến mãi là bắt buộc'))
    .transform((val) => Number(val))
    .pipe(z.number().min(0, t('admin:marketplace.product.validation.discountedPriceMin', 'Giá khuyến mãi không được âm'))),
  category: z.nativeEnum(ProductCategory, {
    errorMap: () => ({ message: t('admin:marketplace.product.validation.categoryRequired', 'Loại sản phẩm là bắt buộc') }),
  }),
  sourceId: z.string().min(1, t('admin:marketplace.product.validation.sourceIdRequired', 'Source ID là bắt buộc')),
  imagesMediaTypes: z.array(z.string()).optional(),
  userManualMediaType: z.string().optional(),
  detailMediaType: z.string().optional(),
});
