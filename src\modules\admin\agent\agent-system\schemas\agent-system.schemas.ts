import { z } from 'zod';
import { AgentStatusEnum, AgentSystemSortBy, SortDirection } from '../types/agent-system.types';

/**
 * <PERSON>hema cho cấu hình model AI
 */
export const modelConfigSchema = z.object({
  temperature: z.number().min(0).max(2).optional(),
  top_p: z.number().min(0).max(1).optional(),
  top_k: z.number().min(0).optional(),
  max_tokens: z.number().min(1).optional(),
});

/**
 * Schema cho tạo agent system
 */
export const createAgentSystemSchema = z.object({
  name: z.string().min(1, 'Tên agent là bắt buộc').max(255, 'Tên agent không được quá 255 ký tự'),
  nameCode: z.string()
    .min(1, 'Mã định danh là bắt buộc')
    .max(100, 'Mã định danh không được quá 100 ký tự')
    .regex(/^[a-z0-9_]+$/, '<PERSON><PERSON> định danh chỉ được chứa chữ cái thư<PERSON>, số và dấu gạch dưới'),
  avatarMimeType: z.string().optional(),
  modelConfig: modelConfigSchema,
  instruction: z.string().optional().nullable(),
  vectorStoreId: z.string().optional(),
  status: z.nativeEnum(AgentStatusEnum).optional().default(AgentStatusEnum.DRAFT),
  roleId: z.string().uuid().optional(),
  modelBaseId: z.string().uuid().optional(),
  modelFinetuningId: z.string().uuid().optional(),
});

/**
 * Schema cho cập nhật agent system
 */
export const updateAgentSystemSchema = z.object({
  name: z.string().min(1, 'Tên agent là bắt buộc').max(255, 'Tên agent không được quá 255 ký tự').optional(),
  nameCode: z.string()
    .min(1, 'Mã định danh là bắt buộc')
    .max(100, 'Mã định danh không được quá 100 ký tự')
    .regex(/^[a-z0-9_]+$/, 'Mã định danh chỉ được chứa chữ cái thường, số và dấu gạch dưới')
    .optional(),
  avatarMimeType: z.string().optional(),
  modelConfig: modelConfigSchema.optional(),
  instruction: z.string().optional().nullable(),
  vectorStoreId: z.string().optional(),
  status: z.nativeEnum(AgentStatusEnum).optional(),
  roleId: z.string().uuid().optional(),
  modelBaseId: z.string().uuid().optional(),
  modelFinetuningId: z.string().uuid().optional(),
});

/**
 * Schema cho cập nhật trạng thái agent system
 */
export const updateAgentSystemStatusSchema = z.object({
  status: z.nativeEnum(AgentStatusEnum),
});

/**
 * Schema cho query agent system
 */
export const agentSystemQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  status: z.nativeEnum(AgentStatusEnum).optional(),
  sortBy: z.nativeEnum(AgentSystemSortBy).optional().default(AgentSystemSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema cho thông tin nhân viên
 */
export const employeeInfoSchema = z.object({
  employeeId: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  date: z.date().optional(),
});

/**
 * Schema cho thông tin vector store
 */
export const vectorStoreSchema = z.object({
  vectorStoreId: z.string(),
  vectorStoreName: z.string(),
});

/**
 * Schema cho thông tin vai trò đơn giản
 */
export const simpleRoleSchema = z.object({
  id: z.string(),
  name: z.string(),
});

/**
 * Schema cho thông tin vai trò đầy đủ
 */
export const roleSchema = simpleRoleSchema.extend({
  description: z.string().nullable(),
  permissionCount: z.number().optional(),
});

/**
 * Schema cho agent system list item
 */
export const agentSystemListItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  nameCode: z.string(),
  avatar: z.string().nullable(),
  model: z.string(),
  status: z.nativeEnum(AgentStatusEnum),
  model_id: z.string().optional().nullable(),
  type_provider: z.string().optional().nullable(),
  roles: simpleRoleSchema.optional(),
});

/**
 * Schema cho agent system detail
 */
export const agentSystemDetailSchema = z.object({
  id: z.string(),
  name: z.string(),
  nameCode: z.string(),
  avatar: z.string().nullable(),
  modelConfig: modelConfigSchema,
  instruction: z.string().nullable(),
  vector: vectorStoreSchema.optional(),
  status: z.nativeEnum(AgentStatusEnum),
  model_id: z.string().optional().nullable(),
  type_provider: z.string().optional().nullable(),
  created: employeeInfoSchema.optional(),
  updated: employeeInfoSchema.optional(),
  deleted: employeeInfoSchema.optional(),
  roles: simpleRoleSchema.optional(),
});

/**
 * Schema cho agent system trash item
 */
export const agentSystemTrashItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  nameCode: z.string(),
  avatar: z.string().optional().nullable(),
  model: z.string(),
  model_id: z.string().optional().nullable(),
  type_provider: z.string().optional().nullable(),
  status: z.nativeEnum(AgentStatusEnum),
  deleted: employeeInfoSchema.optional(),
  roles: simpleRoleSchema.optional(),
});

/**
 * Schema cho response tạo agent system
 */
export const createAgentSystemResponseSchema = z.object({
  id: z.string(),
  avatarUrlUpload: z.string().optional(),
});

/**
 * Schema cho response cập nhật agent system
 */
export const updateAgentSystemResponseSchema = z.object({
  avatarUrlUpload: z.string().optional(),
});

// Export types từ schemas
export type CreateAgentSystemFormData = z.infer<typeof createAgentSystemSchema>;
export type UpdateAgentSystemFormData = z.infer<typeof updateAgentSystemSchema>;
export type UpdateAgentSystemStatusFormData = z.infer<typeof updateAgentSystemStatusSchema>;
export type AgentSystemQueryFormData = z.infer<typeof agentSystemQuerySchema>;
