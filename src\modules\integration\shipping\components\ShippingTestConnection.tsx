import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Select,
  Alert
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import {
  ShippingProviderConfiguration,
  TestShippingProviderDto,
  ShippingProviderTestResult
} from '../types';
import { useTestShippingProviderConfiguration } from '../hooks';

interface ShippingTestConnectionProps {
  provider: ShippingProviderConfiguration;
  onClose?: () => void;
}

/**
 * Component để test kết nối với nhà vận chuyển
 */
const ShippingTestConnection: React.FC<ShippingTestConnectionProps> = ({
  provider,
  onClose
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef, setFormErrors } = useFormErrors<TestShippingProviderDto>();
  const [testResult, setTestResult] = useState<ShippingProviderTestResult | null>(null);
  
  // Mutation for testing
  const testMutation = useTestShippingProviderConfiguration();

  // Form data
  const [formData, setFormData] = useState<TestShippingProviderDto>({
    fromAddress: {
      name: 'Test Sender',
      phone: '**********',
      address: '123 Test Street',
      wardName: 'Ward 1',
      districtName: 'District 1',
      provinceName: 'Ho Chi Minh City',
    },
    toAddress: {
      name: 'Test Receiver',
      phone: '**********',
      address: '456 Test Avenue',
      wardName: 'Ward 2',
      districtName: 'District 2',
      provinceName: 'Ha Noi',
    },
    weight: 1,
    serviceType: 'standard',
  });

  // Handle field changes
  const handleFieldChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof TestShippingProviderDto] as any),
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle test submission
  const handleTest = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    testMutation.mutate(
      { id: provider.id, testData: formData },
      {
        onSuccess: (response) => {
          setTestResult(response.result);
        },
        onError: (error: any) => {
          setTestResult({
            success: false,
            message: 'Test connection failed',
            details: {
              error: error.message,
              statusCode: error.status || 500,
              responseTime: 0,
            }
          });
        }
      }
    );
  };

  // Get result alert variant
  const getResultVariant = () => {
    if (!testResult) return 'info';
    return testResult.success ? 'success' : 'error';
  };

  return (
    <Card className="w-full max-w-4xl">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Icon name="activity" size="lg" className="text-primary" />
            <div>
              <Typography variant="h3">
                {t('integration:shipping.testConnection', 'Test Connection')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {provider.providerName}
              </Typography>
            </div>
          </div>
          
          {onClose && (
            <Button variant="ghost" onClick={onClose}>
              <Icon name="x" size="sm" />
            </Button>
          )}
        </div>

        {/* Test Form */}
        <Form ref={formRef} onSubmit={handleTest} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* From Address */}
            <div className="space-y-4">
              <Typography variant="h6">From Address</Typography>
              
              <FormItem label="Name" name="fromAddress.name" required>
                <Input
                  value={formData.fromAddress?.name || ''}
                  onChange={(e) => handleFieldChange('fromAddress.name', e.target.value)}
                  placeholder="Sender name"
                />
              </FormItem>
              
              <FormItem label="Phone" name="fromAddress.phone" required>
                <Input
                  value={formData.fromAddress?.phone || ''}
                  onChange={(e) => handleFieldChange('fromAddress.phone', e.target.value)}
                  placeholder="Sender phone"
                />
              </FormItem>
              
              <FormItem label="Address" name="fromAddress.address" required>
                <Input
                  value={formData.fromAddress?.address || ''}
                  onChange={(e) => handleFieldChange('fromAddress.address', e.target.value)}
                  placeholder="Sender address"
                />
              </FormItem>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <FormItem label="Ward" name="fromAddress.wardName">
                  <Input
                    value={formData.fromAddress?.wardName || ''}
                    onChange={(e) => handleFieldChange('fromAddress.wardName', e.target.value)}
                    placeholder="Ward"
                  />
                </FormItem>
                
                <FormItem label="District" name="fromAddress.districtName">
                  <Input
                    value={formData.fromAddress?.districtName || ''}
                    onChange={(e) => handleFieldChange('fromAddress.districtName', e.target.value)}
                    placeholder="District"
                  />
                </FormItem>
                
                <FormItem label="Province" name="fromAddress.provinceName">
                  <Input
                    value={formData.fromAddress?.provinceName || ''}
                    onChange={(e) => handleFieldChange('fromAddress.provinceName', e.target.value)}
                    placeholder="Province"
                  />
                </FormItem>
              </div>
            </div>

            {/* To Address */}
            <div className="space-y-4">
              <Typography variant="h6">To Address</Typography>
              
              <FormItem label="Name" name="toAddress.name" required>
                <Input
                  value={formData.toAddress?.name || ''}
                  onChange={(e) => handleFieldChange('toAddress.name', e.target.value)}
                  placeholder="Receiver name"
                />
              </FormItem>
              
              <FormItem label="Phone" name="toAddress.phone" required>
                <Input
                  value={formData.toAddress?.phone || ''}
                  onChange={(e) => handleFieldChange('toAddress.phone', e.target.value)}
                  placeholder="Receiver phone"
                />
              </FormItem>
              
              <FormItem label="Address" name="toAddress.address" required>
                <Input
                  value={formData.toAddress?.address || ''}
                  onChange={(e) => handleFieldChange('toAddress.address', e.target.value)}
                  placeholder="Receiver address"
                />
              </FormItem>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <FormItem label="Ward" name="toAddress.wardName">
                  <Input
                    value={formData.toAddress?.wardName || ''}
                    onChange={(e) => handleFieldChange('toAddress.wardName', e.target.value)}
                    placeholder="Ward"
                  />
                </FormItem>
                
                <FormItem label="District" name="toAddress.districtName">
                  <Input
                    value={formData.toAddress?.districtName || ''}
                    onChange={(e) => handleFieldChange('toAddress.districtName', e.target.value)}
                    placeholder="District"
                  />
                </FormItem>
                
                <FormItem label="Province" name="toAddress.provinceName">
                  <Input
                    value={formData.toAddress?.provinceName || ''}
                    onChange={(e) => handleFieldChange('toAddress.provinceName', e.target.value)}
                    placeholder="Province"
                  />
                </FormItem>
              </div>
            </div>
          </div>

          {/* Package Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label="Weight (kg)" name="weight" required>
              <Input
                type="number"
                value={formData.weight || ''}
                onChange={(e) => handleFieldChange('weight', parseFloat(e.target.value))}
                placeholder="Package weight"
                min="0.1"
                step="0.1"
              />
            </FormItem>
            
            <FormItem label="Service Type" name="serviceType">
              <Select
                value={formData.serviceType || ''}
                onChange={(value) => handleFieldChange('serviceType', value)}
                options={[
                  { value: 'standard', label: 'Standard' },
                  { value: 'express', label: 'Express' },
                  { value: 'economy', label: 'Economy' },
                ]}
                placeholder="Select service type"
              />
            </FormItem>
          </div>

          {/* Test Result */}
          {testResult && (
            <Alert
              variant={getResultVariant()}
              title={testResult.success ? 'Connection Successful' : 'Connection Failed'}
              description={testResult.message}
            >
              {testResult.details && (
                <div className="mt-2 space-y-1 text-sm">
                  {testResult.details.responseTime && (
                    <div>Response Time: {testResult.details.responseTime}ms</div>
                  )}
                  {testResult.details.serviceInfo && (
                    <div className="space-y-1">
                      <div>Service: {testResult.details.serviceInfo.serviceName}</div>
                      <div>Estimated Delivery: {testResult.details.serviceInfo.estimatedDelivery}</div>
                      <div>Shipping Fee: {testResult.details.serviceInfo.shippingFee.toLocaleString()} VND</div>
                    </div>
                  )}
                  {testResult.details.error && (
                    <div className="text-red-600">Error: {testResult.details.error}</div>
                  )}
                </div>
              )}
            </Alert>
          )}

          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t border-border">
            {onClose && (
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                className="flex-1"
              >
                {t('common:close')}
              </Button>
            )}

            <Button
              type="submit"
              variant="primary"
              className="flex-1"
              isLoading={testMutation.isPending}
              leftIcon={<Icon name="activity" size="sm" />}
            >
              {t('integration:shipping.runTest', 'Run Test')}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default ShippingTestConnection;
