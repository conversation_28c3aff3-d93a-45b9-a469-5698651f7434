import { apiClient } from '@/shared/api';
import {
  GetSubscriptionPlansQueryDto,
  SubscriptionPlansListApiResponse,
  SubscriptionPlanDetailApiResponse,
  CreateSubscriptionPlanDto,
  CreateSubscriptionPlanApiResponse,
  UpdateSubscriptionPlanDto,
  UpdateSubscriptionPlanApiResponse,
  DeleteSubscriptionPlanApiResponse,
} from '../types/subscription-plans.admin.types';

const API_BASE_URL = '/admin/subscription';

/**
 * Lấy danh sách gói dịch vụ với phân trang và lọc
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getSubscriptionPlans = async (
  params?: GetSubscriptionPlansQueryDto
): Promise<SubscriptionPlansListApiResponse> => {
  return apiClient.get(`${API_BASE_URL}/plans`, { params });
};

/**
 * Lấy chi tiết gói dịch vụ theo ID
 * @param id ID của gói dịch vụ
 * @returns Promise với response từ API
 */
export const getSubscriptionPlanDetail = async (
  id: number
): Promise<SubscriptionPlanDetailApiResponse> => {
  return apiClient.get(`${API_BASE_URL}/plans/${id}`);
};

/**
 * Tạo gói dịch vụ mới
 * @param data Dữ liệu gói dịch vụ mới
 * @returns Promise với response từ API
 */
export const createSubscriptionPlan = async (
  data: CreateSubscriptionPlanDto
): Promise<CreateSubscriptionPlanApiResponse> => {
  return apiClient.post(`${API_BASE_URL}/plans`, data);
};

/**
 * Cập nhật thông tin gói dịch vụ
 * @param id ID của gói dịch vụ
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateSubscriptionPlan = async (
  id: number,
  data: UpdateSubscriptionPlanDto
): Promise<UpdateSubscriptionPlanApiResponse> => {
  return apiClient.put(`${API_BASE_URL}/plans/${id}`, data);
};

/**
 * Xóa gói dịch vụ
 * @param id ID của gói dịch vụ
 * @returns Promise với response từ API
 */
export const deleteSubscriptionPlan = async (
  id: number
): Promise<DeleteSubscriptionPlanApiResponse> => {
  return apiClient.delete(`${API_BASE_URL}/plans/${id}`);
};
