import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Country, filterCountries, findCountryByCode } from '@/shared/data/countries';
import CountryFlag from './CountryFlag';
import Icon from './Icon';
import Input from './Input';
import ScrollArea from './ScrollArea';

export interface CountrySelectProps {
  value?: string; // Mã quốc gia (VN, US, etc.)
  onChange?: (country: Country) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  className?: string;
  defaultCountry?: string;
  searchable?: boolean;
  fullWidth?: boolean;
  size?: 'sm' | 'md' | 'lg';
  compact?: boolean; // Chỉ hiển thị flag icon, không có chevron
}

/**
 * Component chọn quốc gia với dropdown hiển thị flag, tên và mã quốc gia
 */
const CountrySelect: React.FC<CountrySelectProps> = ({
  value,
  onChange,
  placeholder = 'Chọn quốc gia',
  disabled = false,
  error = false,
  className = '',
  defaultCountry = 'VN',
  searchable = true,
  fullWidth = false,
  size = 'md',
  compact = false,
}) => {
  const { t } = useTranslation(['common']);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Quốc gia được chọn
  const selectedCountry = value ? findCountryByCode(value) : findCountryByCode(defaultCountry);
  
  // Danh sách quốc gia đã lọc
  const filteredCountries = filterCountries(searchTerm);

  // Đóng dropdown khi click bên ngoài
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus vào search input khi mở dropdown
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Xử lý chọn quốc gia
  const handleSelectCountry = (country: Country) => {
    onChange?.(country);
    setIsOpen(false);
    setSearchTerm('');
  };

  // Xử lý toggle dropdown
  const handleToggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setSearchTerm('');
      }
    }
  };

  // Classes cho size
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-8 text-sm px-2';
      case 'lg':
        return 'h-12 text-lg px-4';
      default:
        return 'h-10 text-base px-3';
    }
  };

  // Classes cho container
  const getContainerClasses = () => {
    if (compact) {
      // Compact mode: chỉ hiển thị flag icon
      const compactClasses = `
        inline-flex items-center justify-center
        rounded-md bg-card-muted text-foreground
        transition-colors duration-200 cursor-pointer
        ${size === 'sm' ? 'w-8 h-8' : size === 'lg' ? 'w-12 h-12' : 'w-10 h-10'}
        ${disabled ? 'opacity-60 cursor-not-allowed' : 'hover:bg-muted/50'}
        ${error ? 'ring-2 ring-destructive/50' : ''}
        ${isOpen ? 'ring-2 ring-primary/30' : ''}
      `;
      return compactClasses.trim();
    }

    const baseClasses = `
      flex items-center justify-between
      rounded-md bg-card-muted text-foreground
      transition-colors duration-200 cursor-pointer
      ${getSizeClasses()}
      ${disabled ? 'opacity-60 cursor-not-allowed' : 'hover:bg-muted/50'}
      ${error ? 'ring-2 ring-destructive/50' : ''}
      ${isOpen ? 'ring-2 ring-primary/30' : ''}
      ${fullWidth ? 'w-full' : ''}
    `;
    return baseClasses.trim();
  };

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* Trigger Button */}
      <div
        className={getContainerClasses()}
        onClick={handleToggleDropdown}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleToggleDropdown();
          }
        }}
        title={compact && selectedCountry ? `${selectedCountry.name} ${selectedCountry.dialCode}` : undefined}
      >
        {compact ? (
          // Compact mode: chỉ hiển thị flag
          selectedCountry ? (
            <CountryFlag country={selectedCountry} size={size === 'sm' ? 'sm' : 'md'} />
          ) : (
            <Icon name="globe" size={size === 'sm' ? 'sm' : 'md'} className="text-muted-foreground" />
          )
        ) : (
          // Normal mode: hiển thị flag + chevron
          <>
            <div className="flex items-center gap-2 flex-1 min-w-0">
              {selectedCountry ? (
                <CountryFlag country={selectedCountry} size={size === 'sm' ? 'sm' : 'md'} />
              ) : (
                <span className="text-muted-foreground">{placeholder}</span>
              )}
            </div>

            <Icon
              name="chevron-down"
              size={size === 'sm' ? 'sm' : 'md'}
              className={`transition-transform text-muted-foreground flex-shrink-0 ${
                isOpen ? 'rotate-180' : ''
              }`}
            />
          </>
        )}
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className={`absolute top-full mt-1 bg-white dark:bg-dark-light rounded-md shadow-lg z-50 ${
          compact
            ? 'left-0 w-80 min-w-80 max-w-screen-sm' // Compact mode: dropdown rộng cố định, responsive
            : 'left-0 right-0' // Normal mode: dropdown theo width container
        }`}>
          {/* Search Input */}
          {searchable && (
            <div className="p-2">
              <Input
                ref={searchInputRef}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t('common:search')}
                className="w-full"
                leftIcon={<Icon name="search" size="sm" />}
              />
            </div>
          )}

          {/* Countries List */}
          <ScrollArea
            height="auto"
            maxHeight="240px"
            className="py-1"
            autoHide={true}
            invisible={false}
            direction="vertical"
          >
            {filteredCountries.length > 0 ? (
              <div className="space-y-0">
                {filteredCountries.map((country) => (
                  <button
                    key={country.code}
                    type="button"
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 text-left
                      hover:bg-gray-100 dark:hover:bg-dark-lighter transition-colors duration-150
                      ${selectedCountry?.code === country.code ? 'bg-gray-100 dark:bg-dark-lighter' : ''}
                    `}
                    onClick={() => handleSelectCountry(country)}
                  >
                    <CountryFlag country={country} size="md" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <span className="font-medium truncate">{country.name}</span>
                        <span className="text-muted-foreground text-sm ml-2 flex-shrink-0">
                          {country.code}
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {country.dialCode}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="px-3 py-4 text-center text-muted-foreground">
                {t('common:noResults')}
              </div>
            )}
          </ScrollArea>
        </div>
      )}
    </div>
  );
};

export default CountrySelect;
