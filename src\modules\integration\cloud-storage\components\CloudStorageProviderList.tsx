import React from 'react';
import { Card, Typography, Button, Icon, EmptyState } from '@/shared/components/common';

interface CloudStorageProviderListProps {
  onCreateNew?: () => void;
}

/**
 * Placeholder component for Cloud Storage Provider List
 */
const CloudStorageProviderList: React.FC<CloudStorageProviderListProps> = ({ onCreateNew }) => {
  return (
    <div className="w-full space-y-6">
      <Card>
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <Typography variant="h3" className="mb-2">
                Danh sách Cloud Storage
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                Quản lý các tích hợp cloud storage
              </Typography>
            </div>

            <Button
              variant="primary"
              onClick={onCreateNew}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              Thêm Cloud Storage
            </Button>
          </div>
        </div>
      </Card>

      <Card>
        <div className="p-8">
          <EmptyState
            icon="cloud"
            title="Chưa có cloud storage nào"
            description="Bạn chưa thêm cloud storage nào. Hãy thêm cloud storage đầu tiên."
            actions={
              <Button
                variant="primary"
                onClick={onCreateNew}
                leftIcon={<Icon name="plus" size="sm" />}
              >
                Thêm cloud storage đầu tiên
              </Button>
            }
          />
        </div>
      </Card>
    </div>
  );
};

export default CloudStorageProviderList;
