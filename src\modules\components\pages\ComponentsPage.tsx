import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Card, Icon, ResponsiveGrid } from '@/shared/components/common';

interface ComponentCategory {
  titleKey: string;
  descriptionKey: string;
  path: string;
  iconName: string;
}

const ComponentsPage: React.FC = () => {
  const { t } = useTranslation(['components']);

  const categories: ComponentCategory[] = [
    {
      titleKey: 'components.charts.lineChart.title',
      descriptionKey: 'components.charts.lineChart.description',
      path: '/components/charts/line-chart',
      iconName: 'chart',
    },
    {
      titleKey: 'components.simpleChart.title',
      descriptionKey: 'components.simpleChart.description',
      path: '/components/charts/simple-chart',
      iconName: 'chart',
    },
    {
      titleKey: 'components.animation.title',
      descriptionKey: 'components.animation.description',
      path: '/components/animation',
      iconName: 'components',
    },
    {
      titleKey: 'components.banner.title',
      descriptionKey: 'components.banner.description',
      path: '/components/banner',
      iconName: 'components',
    },
    {
      titleKey: 'components.categories.buttons.title',
      descriptionKey: 'components.categories.buttons.description',
      path: '/components/buttons',
      iconName: 'components',
    },
    {
      titleKey: 'components.categories.cards.title',
      descriptionKey: 'components.categories.cards.description',
      path: '/components/cards',
      iconName: 'components',
    },
    {
      titleKey: 'components.categories.chips.title',
      descriptionKey: 'components.categories.chips.description',
      path: '/components/chips',
      iconName: 'components',
    },
    {
      titleKey: 'components.categories.inputs.title',
      descriptionKey: 'components.categories.inputs.description',
      path: '/components/inputs',
      iconName: 'components',
    },
    {
      titleKey: 'components.categories.layout.title',
      descriptionKey: 'components.categories.layout.description',
      path: '/components/layout',
      iconName: 'components',
    },
    {
      titleKey: 'components.grid.title',
      descriptionKey: 'components.grid.description',
      path: '/components/grid',
      iconName: 'components',
    },
    {
      titleKey: 'components.responsiveGrid.title',
      descriptionKey: 'components.responsiveGrid.description',
      path: '/components/responsive-grid',
      iconName: 'grid',
    },
    {
      titleKey: 'components.categories.theme.title',
      descriptionKey: 'components.categories.theme.description',
      path: '/components/theme',
      iconName: 'components',
    },
    {
      titleKey: 'components.categories.theme.system.title',
      descriptionKey: 'components.categories.theme.system.description',
      path: '/components/theme-system',
      iconName: 'settings',
    },
    {
      titleKey: 'components.categories.form.title',
      descriptionKey: 'components.categories.form.description',
      path: '/components/form',
      iconName: 'components',
    },
    {
      titleKey: 'components.categories.form.theme.title',
      descriptionKey: 'components.categories.form.theme.description',
      path: '/components/form/theme',
      iconName: 'components',
    },
    {
      titleKey: 'components.form.wizard.title',
      descriptionKey: 'components.form.wizard.description',
      path: '/components/form/wizard',
      iconName: 'form',
    },
    {
      titleKey: 'components.categories.typography.title',
      descriptionKey: 'components.categories.typography.description',
      path: '/components/typography',
      iconName: 'components',
    },
    {
      titleKey: 'components.menu.title',
      descriptionKey: 'components.menu.description',
      path: '/components/menu',
      iconName: 'menu',
    },
    {
      titleKey: 'components.tooltip.title',
      descriptionKey: 'components.tooltip.description',
      path: '/components/tooltip',
      iconName: 'info',
    },
    {
      titleKey: 'components.searchBar.title',
      descriptionKey: 'components.searchBar.description',
      path: '/components/search-bar',
      iconName: 'search',
    },
    {
      titleKey: 'components.modernMenu.title',
      descriptionKey: 'components.modernMenu.description',
      path: '/components/modern-menu',
      iconName: 'menu',
    },
    {
      titleKey: 'components.cards.title',
      descriptionKey: 'components.cards.description',
      path: '/components/custom-card',
      iconName: 'components',
    },
    {
      titleKey: 'components.avatar.title',
      descriptionKey: 'components.avatar.description',
      path: '/components/avatar',
      iconName: 'user',
    },
    {
      titleKey: 'components.imageGallery.title',
      descriptionKey: 'components.imageGallery.description',
      path: '/components/image-gallery',
      iconName: 'image',
    },
    {
      titleKey: 'components.notification.title',
      descriptionKey: 'components.notification.description',
      path: '/components/notification',
      iconName: 'info',
    },
    {
      titleKey: 'components.topCard.title',
      descriptionKey: 'components.topCard.description',
      path: '/components/top-card',
      iconName: 'layout',
    },
    {
      titleKey: 'Auth Components',
      descriptionKey: 'Các components xác thực và phân quyền',
      path: '/components/auth',
      iconName: 'settings',
    },
    {
      titleKey: 'components.searchInputWithImage.title',
      descriptionKey: 'components.searchInputWithImage.description',
      path: '/components/search-input-with-image',
      iconName: 'search',
    },
    {
      titleKey: 'components.charts.demo.title',
      descriptionKey: 'components.charts.demo.description',
      path: '/components/charts/demo',
      iconName: 'chart',
    },
    {
      titleKey: 'Pricing Cards Showcase',
      descriptionKey:
        'Modern pricing card designs inspired by Stripe, Vercel and other SaaS platforms',
      path: '/subscription/pricing-showcase',
      iconName: 'credit-card',
    },
    {
      titleKey: 'Icons',
      descriptionKey: 'Tất cả các icon có sẵn trong hệ thống',
      path: '/components/icons',
      iconName: 'star',
    },
    {
      titleKey: 'SSE (Server-Sent Events)',
      descriptionKey: 'Bộ tiện ích hoàn chỉnh để làm việc với Server-Sent Events trong React',
      path: '/components/sse',
      iconName: 'wifi',
    },
    {
      titleKey: 'Tabs Components',
      descriptionKey: 'Các component Tab với nhiều kiểu dáng và tùy chọn khác nhau',
      path: '/components/tabs',
      iconName: 'layout',
    },
    {
      titleKey: 'Stepper Components',
      descriptionKey: 'Các component Stepper với nhiều kiểu dáng, đa ngôn ngữ và responsive',
      path: '/components/stepper',
      iconName: 'list',
    },
    {
      titleKey: 'Calendar Components Showcase',
      descriptionKey: 'Comprehensive demo of all calendar components with advanced features like events, themes, time zones, and animations',
      path: '/components/calendar-showcase',
      iconName: 'calendar',
    },
    {
      titleKey: 'Calendar Demo - Simple',
      descriptionKey: 'Simple showcase of basic calendar components with key features and getting started guide',
      path: '/calendar-demo-simple',
      iconName: 'calendar',
    },
    {
      titleKey: 'FileActionCard Demo',
      descriptionKey: 'Component card với button tải xuống và tải lên file, hỗ trợ nhiều định dạng và tùy chỉnh linh hoạt',
      path: '/components/file-action-card',
      iconName: 'file',
    },
    {
      titleKey: 'DatePickerFormField Demo',
      descriptionKey: 'Giải pháp cho lỗi "Expected string, received date" - DatePicker tương thích với Form validation',
      path: '/components/datepicker-form-field',
      iconName: 'calendar',
    },
    {
      titleKey: 'PhoneNumberInput Component',
      descriptionKey: 'International phone number input with country selection, flags, and validation. Supports 38+ countries with search functionality.',
      path: '/components/phone-number-input',
      iconName: 'phone',
    },
    {
      titleKey: 'CountrySelect Component',
      descriptionKey: 'Country selection dropdown with flags, country names, and country codes. Features search functionality and system scroll.',
      path: '/components/country-select',
      iconName: 'globe',
    },
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">{t('library.title')}</h1>
        <p className="text-muted">{t('library.description')}</p>
      </div>

      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
        gap={{ xs: 4, md: 5, lg: 6 }}
      >
        {categories.map(category => (
          <Link key={category.path} to={category.path} className="block h-full">
            <Card
              variant="elevated"
              hoverable
              className="h-full transition-all duration-300 rounded-xl"
            >
              <div className="flex items-start p-6">
                <div className="mr-4 text-primary">
                  {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                  <Icon name={category.iconName as any} size="xl" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    {t(category.titleKey.replace('components.', ''))}
                  </h3>
                  <p className="text-sm text-muted">
                    {t(category.descriptionKey.replace('components.', ''))}
                  </p>
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default ComponentsPage;
