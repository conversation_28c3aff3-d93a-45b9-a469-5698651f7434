/**
 * Business service for email campaign overview - Layer 2: Business logic
 */

import { EmailService } from './email.service';
import {
  EmailCampaignOverviewDto,
} from '../types/email.types';

/**
 * Email campaign overview business service
 */
export const EmailCampaignOverviewBusinessService = {
  /**
   * L<PERSON><PERSON> thông tin overview email campaigns với business logic
   */
  getEmailCampaignOverview: async (): Promise<EmailCampaignOverviewDto> => {
    try {
      const response = await EmailService.getEmailCampaignOverview();
      
      // Business logic: Validate and process data
      const data = response.result.overview;
      
      // Ensure all values are non-negative
      const validatedData: EmailCampaignOverviewDto = {
        totalCampaigns: Math.max(0, data.totalCampaigns || 0),
        sendingCampaigns: Math.max(0, data.sendingCampaigns || 0),
        sentCampaigns: Math.max(0, data.sentCampaigns || 0),
        scheduledCampaigns: Math.max(0, data.scheduledCampaigns || 0),
        updatedAt: data.updatedAt || Date.now(),
      };

      // Business validation: Ensure sending + sent + scheduled <= total
      const activeCampaigns = validatedData.sendingCampaigns + validatedData.sentCampaigns + validatedData.scheduledCampaigns;
      if (activeCampaigns > validatedData.totalCampaigns) {
        console.warn('Email campaign overview data inconsistency detected:', {
          totalCampaigns: validatedData.totalCampaigns,
          activeCampaigns,
        });
      }

      return validatedData;
    } catch (error) {
      console.error('Error fetching email campaign overview:', error);
      
      // Return fallback data in case of error
      return {
        totalCampaigns: 0,
        sendingCampaigns: 0,
        sentCampaigns: 0,
        scheduledCampaigns: 0,
        updatedAt: Date.now(),
      };
    }
  },
};
