{"adminTool": {"title": "Manage Tools", "description": "Manage system tools, including creating, editing and organizing tools for users.", "tools": "Tools", "toolsDescription": "Manage tools in the system, including adding, editing, deleting and approving tools.", "status": {"draft": "Draft", "approved": "Approved", "deprecated": "Deprecated"}, "tool": {"title": "Manage Tools", "name": "Tool Name", "namePlaceholder": "Enter tool name", "description": "Description", "descriptionPlaceholder": "Enter tool description", "statusLabel": "Status", "accessType": "Access Type", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "toolName": "Tool Display Name", "toolNamePlaceholder": "Enter tool display name", "toolDescription": "Tool Display Description", "toolDescriptionPlaceholder": "Enter tool display description", "parameters": "Tool Parameters", "parametersPlaceholder": "Enter tool parameters in JSON format", "editToolVersion": "Edit Tool Version", "createToolVersion": "Create New Tool Version", "selectTool": "Select Tool", "selectToolPlaceholder": "Select tool to create version", "changeDescription": "Change Description", "changeDescriptionPlaceholder": "Describe the changes in this version", "validation": {"nameRequired": "Tool name is required", "toolNameRequired": "Tool display name is required", "toolRequired": "Please select a tool", "parametersRequired": "Tool parameters are required", "invalidJson": "Invalid JSON format"}, "access": {"public": "Public", "private": "Private", "restricted": "Restricted"}}}}