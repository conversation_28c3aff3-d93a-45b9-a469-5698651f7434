import { z } from 'zod';
import { AgentRoleSortBy, SortDirection } from '../types/agent-role.types';

/**
 * Schema cho cấu hình reconnect trong MCP
 */
export const reconnectConfigSchema = z.object({
  enable: z.boolean().default(false),
  delayMs: z.number().min(0).default(1000),
  maxAttempts: z.number().min(0).default(3),
});

/**
 * Schema cho cấu hình module MCP
 */
export const moduleMcpConfigSchema = z.object({
  mcpNameServer: z.string().default(''),
  mcpPort: z.number().min(0).default(0),
  url: z.string().default(''),
  useNodeEventSource: z.boolean().default(false),
  header: z.record(z.string()).default({}),
  reconnect: reconnectConfigSchema.default({}),
});

/**
 * Schema cho tạo agent role
 */
export const createAgentRoleSchema = z.object({
  name: z.string().min(1, 'Tên vai trò không được để trống').max(50, 'Tên vai trò không được quá 50 ký tự'),
  description: z.string().optional(),
  moduleMcpConfig: moduleMcpConfigSchema.optional(),
});

/**
 * Schema cho cập nhật agent role
 */
export const updateAgentRoleSchema = z.object({
  name: z.string().min(1, 'Tên vai trò không được để trống').max(50, 'Tên vai trò không được quá 50 ký tự').optional(),
  description: z.string().optional(),
  moduleMcpConfig: moduleMcpConfigSchema.optional(),
});

/**
 * Schema cho query agent role
 */
export const agentRoleQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  sortBy: z.nativeEnum(AgentRoleSortBy).optional().default(AgentRoleSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema cho thông tin nhân viên
 */
export const employeeInfoSchema = z.object({
  employeeId: z.string(),
  name: z.string(),
  avatar: z.string().nullable(),
  date: z.date().optional(),
});

/**
 * Schema cho thông tin agent sử dụng vai trò
 */
export const roleAgentUseSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().optional().nullable(),
});

/**
 * Schema cho agent role list item
 */
export const agentRoleListItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
});

/**
 * Schema cho agent role detail
 */
export const agentRoleDetailSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  agentUse: roleAgentUseSchema.optional(),
  created: employeeInfoSchema.optional().nullable(),
  updated: employeeInfoSchema.optional().nullable(),
  deleted: employeeInfoSchema.optional().nullable(),
  moduleMcpConfig: moduleMcpConfigSchema.optional(),
});

/**
 * Schema cho agent role trash item
 */
export const agentRoleTrashItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  deleted: employeeInfoSchema.optional(),
});

/**
 * Schema cho response tạo agent role
 */
export const createAgentRoleResponseSchema = z.object({
  id: z.string(),
});

// Export types từ schemas
export type CreateAgentRoleFormData = z.infer<typeof createAgentRoleSchema>;
export type UpdateAgentRoleFormData = z.infer<typeof updateAgentRoleSchema>;
export type AgentRoleQueryFormData = z.infer<typeof agentRoleQuerySchema>;
