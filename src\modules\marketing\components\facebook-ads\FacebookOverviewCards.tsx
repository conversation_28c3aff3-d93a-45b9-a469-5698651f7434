import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Icon,
  ResponsiveGrid,
  Loading,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';
import { useRecentFacebookAdsCampaigns } from '../../hooks/facebook-ads/useFacebookAdsCampaigns';

interface FacebookOverviewCardsProps {
  /**
   * Hiển thị loading state
   */
  isLoading?: boolean;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Facebook Ads Overview Cards Component
 * Hiển thị các thẻ thống kê tổng quan cho Facebook Ads
 */
const FacebookOverviewCards: React.FC<FacebookOverviewCardsProps> = ({
  isLoading = false,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  
  const {
    user,
    adAccounts,
    isLoading: authLoading,
  } = useFacebookAuth();

  const {
    data: recentCampaigns,
    isLoading: campaignsLoading,
  } = useRecentFacebookAdsCampaigns(5);

  // Calculate stats
  const totalAccounts = adAccounts.length;
  const activeAccounts = adAccounts.filter(acc => acc.accountStatus === 1).length;
  const totalCampaigns = recentCampaigns?.result?.items?.length || 0;
  const activeCampaigns = recentCampaigns?.result?.items?.filter(
    campaign => campaign.status === 'ACTIVE'
  ).length || 0;

  // Loading state
  if (isLoading || authLoading || campaignsLoading) {
    return (
      <div className={className}>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
          {[...Array(4)].map((_, index) => (
            <Card key={index} className="p-4">
              <Loading size="md" className="flex justify-center py-4" />
            </Card>
          ))}
        </ResponsiveGrid>
      </div>
    );
  }

  const overviewCards = [
    {
      title: t('marketing:facebookAds.overview.totalAccounts', 'Tổng tài khoản'),
      value: totalAccounts,
      icon: 'facebook',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: t('marketing:facebookAds.overview.activeAccounts', 'Tài khoản hoạt động'),
      value: activeAccounts,
      icon: 'check-circle',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: t('marketing:facebookAds.overview.totalCampaigns', 'Tổng chiến dịch'),
      value: totalCampaigns,
      icon: 'campaign',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: t('marketing:facebookAds.overview.activeCampaigns', 'Chiến dịch đang chạy'),
      value: activeCampaigns,
      icon: 'play',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className={className}>
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
        {overviewCards.map((card, index) => (
          <Card key={index} className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <Typography variant="body2" className="text-muted-foreground mb-1">
                  {card.title}
                </Typography>
                <Typography variant="h3" className={`font-bold ${card.color}`}>
                  {card.value}
                </Typography>
              </div>
              <div className={`p-3 rounded-full ${card.bgColor}`}>
                <Icon name={card.icon as any} size="lg" className={card.color} />
              </div>
            </div>
          </Card>
        ))}
      </ResponsiveGrid>

      {/* Additional info card */}
      {user && (
        <Card className="p-4 mt-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <Icon name="user" className="text-blue-600" />
            </div>
            <div className="flex-1">
              <Typography variant="body2" className="font-medium">
                {t('marketing:facebookAds.overview.connectedAs', 'Đã kết nối với tài khoản')}
              </Typography>
              <Typography variant="body1" className="font-semibold">
                {user.name}
              </Typography>
              {user.email && (
                <Typography variant="caption" className="text-muted-foreground">
                  {user.email}
                </Typography>
              )}
            </div>
            <div className="text-right">
              <Typography variant="caption" className="text-muted-foreground">
                {t('marketing:facebookAds.overview.lastSync', 'Đồng bộ lần cuối')}
              </Typography>
              <Typography variant="body2" className="font-medium">
                {new Date().toLocaleDateString('vi-VN')}
              </Typography>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default FacebookOverviewCards;
