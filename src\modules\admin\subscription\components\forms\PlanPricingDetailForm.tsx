import React from 'react';
import {
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  Chip,
  Loading,
} from '@/shared/components/common';
import { useGetPlanPricingById } from '../../hooks/usePlanPricingAdmin';
import { BillingCycle, UsageUnit } from '../../types/plan-pricing.admin.types';

interface PlanPricingDetailFormProps {
  pricingId: number;
  onClose: () => void;
}

/**
 * Form hiển thị chi tiết tùy chọn giá
 */
const PlanPricingDetailForm: React.FC<PlanPricingDetailFormProps> = ({
  pricingId,
  onClose,
}) => {  
  // Lấy chi tiết pricing
  const { data: pricingResponse, isLoading, error } = useGetPlanPricingById(pricingId);
  const pricing = pricingResponse?.result;

  // Format giá tiền
  const formatPrice = (price: string) => {
    if (!price) return '0 ₫';
    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) return '0 ₫';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(numPrice);
  };

  // Format billing cycle
  const formatBillingCycle = (billingCycle: BillingCycle) => {
    const cycleMap = {
      [BillingCycle.MONTH]: 'Tháng',
      [BillingCycle.SIX_MONTHS]: '6 Tháng',
      [BillingCycle.YEAR]: 'Năm',
    };
    return cycleMap[billingCycle] || billingCycle;
  };

  // Format usage limit
  const formatUsageLimit = (usageLimit: string, usageUnit: UsageUnit) => {
    if (!usageLimit) return '0';
    const numLimit = parseFloat(usageLimit);
    if (isNaN(numLimit)) return '0';
    
    if (usageUnit === UsageUnit.BYTES) {
      // Convert bytes to GB
      const gb = numLimit / (1024 * 1024 * 1024);
      return `${gb.toFixed(2)} GB`;
    }
    return `${numLimit.toLocaleString('vi-VN')} ${usageUnit}`;
  };

  // Format date
  const formatDate = (timestamp: string) => {
    if (!timestamp) return '-';
    const date = new Date(parseInt(timestamp));
    if (isNaN(date.getTime())) return '-';
    return date.toLocaleString('vi-VN');
  };

  // Render billing cycle chip
  const renderBillingCycleChip = (billingCycle: BillingCycle) => {
    const cycleConfig = {
      [BillingCycle.MONTH]: { variant: 'primary' as const },
      [BillingCycle.SIX_MONTHS]: { variant: 'info' as const },
      [BillingCycle.YEAR]: { variant: 'success' as const },
    };
    
    const config = cycleConfig[billingCycle] || { variant: 'default' as const };
    
    return (
      <Chip variant={config.variant} size="sm">
        {formatBillingCycle(billingCycle)}
      </Chip>
    );
  };

  // Render usage unit chip
  const renderUsageUnitChip = (usageUnit: UsageUnit) => {
    const unitConfig = {
      [UsageUnit.BYTES]: { variant: 'info' as const },
      [UsageUnit.REQUEST]: { variant: 'primary' as const },
    };
    
    const config = unitConfig[usageUnit] || { variant: 'default' as const };
    
    return (
      <Chip variant={config.variant} size="sm">
        {usageUnit}
      </Chip>
    );
  };

  // Render status chip
  const renderStatusChip = (isActive: boolean) => {
    return (
      <Chip variant={isActive ? 'success' : 'danger'} size="sm">
        {isActive ? 'Hoạt động' : 'Không hoạt động'}
      </Chip>
    );
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-2xl">
        <div className="p-6 flex justify-center">
          <Loading />
        </div>
      </Card>
    );
  }

  if (error || !pricing) {
    return (
      <Card className="w-full">
        <div className="p-6">
          <Typography variant="h3" className="mb-4 text-red-600">
            Lỗi
          </Typography>
          <Typography className="mb-4">
            Không thể tải thông tin chi tiết tùy chọn giá.
          </Typography>
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Typography variant="h3">
            Chi tiết tùy chọn giá #{pricing.id}
          </Typography>
          {renderStatusChip(pricing.isActive)}
        </div>

        <div className="space-y-6">
          {/* Thông tin cơ bản */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                ID
              </Typography>
              <Typography variant="body1" className="font-medium">
                #{pricing.id}
              </Typography>
            </div>

            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                Plan ID
              </Typography>
              <Typography variant="body1" className="font-medium">
                #{pricing.planId}
              </Typography>
            </div>

            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                Chu kỳ thanh toán
              </Typography>
              <div className="mt-1">
                {renderBillingCycleChip(pricing.billingCycle)}
              </div>
            </div>

            <div>
              <Typography variant="body2" className="text-gray-500 mb-1">
                Đơn vị sử dụng
              </Typography>
              <div className="mt-1">
                {renderUsageUnitChip(pricing.usageUnit)}
              </div>
            </div>
          </div>

          {/* Thông tin giá và giới hạn */}
          <div className="border-t pt-4">
            <Typography variant="h4" className="mb-4">
              Thông tin giá và giới hạn
            </Typography>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  Giá
                </Typography>
                <Typography variant="h4" className="text-green-600 font-bold">
                  {formatPrice(pricing.price)}
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  Giới hạn sử dụng
                </Typography>
                <Typography variant="body1" className="font-medium">
                  {formatUsageLimit(pricing.usageLimit, pricing.usageUnit)}
                </Typography>
              </div>
            </div>
          </div>

          {/* Thông tin thời gian */}
          <div className="border-t pt-4">
            <Typography variant="h4" className="mb-4">
              Thông tin thời gian
            </Typography>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  Ngày tạo
                </Typography>
                <Typography variant="body1">
                  {formatDate(pricing.createdAt)}
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-gray-500 mb-1">
                  Ngày cập nhật
                </Typography>
                <Typography variant="body1">
                  {formatDate(pricing.updatedAt)}
                </Typography>
              </div>
            </div>
          </div>

          {/* Trạng thái */}
          <div className="border-t pt-4">
            <Typography variant="h4" className="mb-4">
              Trạng thái
            </Typography>
            
            <div className="flex items-center space-x-2">
              {renderStatusChip(pricing.isActive)}
              <Typography variant="body2" className="text-gray-500">
                {pricing.isActive 
                  ? 'Tùy chọn giá này đang được kích hoạt và có thể sử dụng'
                  : 'Tùy chọn giá này đã bị vô hiệu hóa'
                }
              </Typography>
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end pt-6 border-t mt-6">
          <Button variant="outline" onClick={onClose}>
            Đóng
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default PlanPricingDetailForm;
