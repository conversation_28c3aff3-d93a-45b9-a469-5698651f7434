import { apiClient } from '@/shared/api/axios';
import {
  AgentRoleDetail,
  CreateAgentRoleParams,
  UpdateAgentRoleParams,
  AgentRoleQueryParams,
  CreateAgentRoleResponse,
} from '../types/agent-role.types';

/**
 * Service để tương tác với API agent role của admin
 */
export class AdminAgentRoleService {
  private baseUrl = '/admin/agent-roles';

  /**
   * <PERSON><PERSON>y danh sách agent role
   * @param params Tham số truy vấn
   * @returns Danh sách agent role
   */
  async getAgentRoles(params: AgentRoleQueryParams) {
    try {
      const response = await apiClient.get(this.baseUrl, {
        params,
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching agent roles:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách agent role đã xóa
   * @param params Tham số truy vấn
   * @returns Danh sách agent role đã xóa
   */
  async getDeletedAgentRoles(params: AgentRoleQueryParams) {
    try {
      const response = await apiClient.get(`${this.baseUrl}/trash`, {
        params,
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching deleted agent roles:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết agent role theo ID
   * @param id ID của agent role
   * @returns Thông tin chi tiết agent role
   */
  async getAgentRoleById(id: string): Promise<AgentRoleDetail> {
    try {
      const response = await apiClient.get<AgentRoleDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching agent role ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo agent role mới
   * @param data Dữ liệu tạo agent role
   * @returns Response tạo agent role
   */
  async createAgentRole(data: CreateAgentRoleParams): Promise<CreateAgentRoleResponse> {
    try {
      const response = await apiClient.post<CreateAgentRoleResponse>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating agent role:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin agent role
   * @param id ID của agent role
   * @param data Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateAgentRole(id: string, data: UpdateAgentRoleParams): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}`, data, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error updating agent role ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa agent role
   * @param id ID của agent role
   * @returns Kết quả xóa
   */
  async deleteAgentRole(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting agent role ${id}:`, error);
      throw error;
    }
  }

  /**
   * Khôi phục agent role đã xóa
   * @param id ID của agent role
   * @returns Kết quả khôi phục
   */
  async restoreAgentRole(id: string): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/restore`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error restoring agent role ${id}:`, error);
      throw error;
    }
  }
}

// Export instance
export const adminAgentRoleService = new AdminAgentRoleService();
