import { apiClient } from '@/shared/api/axios';
import {
  TypeAgentDetail,
  CreateTypeAgentParams,
  UpdateTypeAgentParams,
  TypeAgentQueryParams,
} from '../types/type-agent.types';

/**
 * Service để tương tác với API type agent của admin
 */
export class AdminTypeAgentService {
  private baseUrl = '/admin/type-agents';

  async getTypeAgents(params: TypeAgentQueryParams) {
    try {
      const response = await apiClient.get(this.baseUrl, { params, tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error('Error fetching type agents:', error);
      throw error;
    }
  }

  async getTypeAgentById(id: string): Promise<TypeAgentDetail> {
    try {
      const response = await apiClient.get<TypeAgentDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching type agent ${id}:`, error);
      throw error;
    }
  }

  async createTypeAgent(data: CreateTypeAgentParams): Promise<{ id: string }> {
    try {
      const response = await apiClient.post<{ id: string }>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating type agent:', error);
      throw error;
    }
  }

  async updateTypeAgent(id: string, data: UpdateTypeAgentParams): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}`, data, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error updating type agent ${id}:`, error);
      throw error;
    }
  }

  async deleteTypeAgent(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting type agent ${id}:`, error);
      throw error;
    }
  }
}

export const adminTypeAgentService = new AdminTypeAgentService();
