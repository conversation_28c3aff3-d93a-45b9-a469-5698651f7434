{"data": {"title": "System Data Management", "description": "Centralized management of system data including media, knowledge files, URLs, and tools.", "media": {"title": "Media Management", "description": "Manage media files such as images, videos, audio, and documents.", "descriptionDetail": "Description", "totalFiles": "Total files", "manage": "Manage Media", "viewMedia": "Media Details", "name": "File name", "size": "Size", "author": "Author", "createdAt": "Created at", "storageKey": "Storage key", "tags": "Tags", "selectedItems": "{{count}} items selected", "table": {"name": "File name", "description": "Description", "size": "Size", "author": "Author", "createdAt": "Created at", "status": "Status"}, "status": {"ACTIVE": "Active", "PENDING": "Pending", "INACTIVE": "Inactive", "DELETED": "Deleted"}, "messages": {"deleteSuccess": "Media deleted successfully", "deleteError": "An error occurred while deleting media", "batchDeleteSuccess": "{{count}} media deleted successfully", "batchDeleteError": "An error occurred during batch deletion"}, "confirmDeleteMessage": "Are you sure you want to delete this media?", "confirmBatchDeleteMessage": "Are you sure you want to delete {{count}} selected media?"}, "knowledgeFiles": {"title": "Knowledge Files Management", "description": "Manage knowledge files used for AI and vector stores.", "totalFiles": "Total files", "manage": "Manage Knowledge Files", "viewFile": "View knowledge file", "uploadFiles": "Upload knowledge files", "selectFiles": "Select files", "selectFilesToDelete": "Please select at least one file to delete", "supportedFormats": "Supported formats: PDF, DOCX, TXT, CSV, JSON", "fileName": "File name", "fileSize": "Size", "fileType": "File type", "vectorStore": "Vector Store", "noVectorStore": "Not assigned to any vector store", "createdAt": "Created at", "table": {"name": "File name", "extension": "Format", "size": "Size"}, "messages": {"deleteSuccess": "Knowledge file deleted successfully", "deleteError": "An error occurred while deleting knowledge file", "batchDeleteSuccess": "{{count}} knowledge files deleted successfully", "batchDeleteError": "An error occurred during batch deletion", "uploadSuccess": "Knowledge files uploaded successfully", "uploadError": "An error occurred while uploading knowledge files"}, "confirmDeleteMessage": "Are you sure you want to delete this knowledge file?", "confirmBatchDeleteMessage": "Are you sure you want to delete {{count}} selected knowledge files?"}, "vectorStore": {"title": "Vector Store Management", "description": "Manage vector stores and embeddings for AI applications and semantic search.", "totalStores": "Total vector stores", "manage": "Manage Vector Stores", "viewVectorStore": "View Vector Store", "createVectorStore": "Create New Vector Store", "name": "Vector Store Name", "namePlaceholder": "Enter vector store name", "nameRequired": "Vector store name is required", "size": "Size", "files": "Number of files", "agents": "Number of agents using", "createdAt": "Created at", "assignFiles": "Assign Files", "selectFiles": "Select files", "assign": "Assign files", "assignButton": "Assign Files", "selectFilesWarning": "Please select at least one file", "selectFilesToRemove": "Please select at least one file to remove", "removeFiles": "Remove selected files", "removeFileSuccess": "File removed from Vector Store successfully", "removeFileError": "Error removing file from Vector Store", "removeFilesSuccess": "Selected files removed successfully", "removeFilesError": "Error removing selected files", "removeFileTitle": "Remove file from Vector Store", "removeFileMessage": "Are you sure you want to remove this file from Vector Store?", "removeFilesTitle": "Remove selected files", "removeFilesMessage": "Are you sure you want to remove {{count}} selected files from Vector Store?", "detail": "Vector Store Details", "notFound": "Vector Store not found", "fileSelectionNotImplemented": "File selection feature not yet implemented", "table": {"name": "Name", "size": "Size", "files": "Files", "agents": "Agents", "assigned": "Assigned", "createdAt": "Created at"}, "filter": {"vectorStore": "Vector Store"}, "messages": {"createSuccess": "Vector store created successfully", "createError": "An error occurred while creating vector store", "deleteSuccess": "Vector store deleted successfully", "deleteError": "An error occurred while deleting vector store", "assignSuccess": "Files assigned to vector store successfully", "assignError": "An error occurred while assigning files to vector store"}, "confirmDeleteMessage": "Are you sure you want to delete this vector store?"}, "url": {"title": "URL Management", "description": "Manage URLs and content crawled from websites for use in the system.", "totalUrls": "Total URLs", "manage": "Manage URLs", "cancel": "Cancel", "processing": "Processing...", "crawl": "Crawl URL", "crawlTitle": "Crawl URL", "maxUrls": "Max URLs", "startCrawl": "Start Crawl", "crawlDepth": "Crawl Depth", "crawlDepthHelp": "Number of levels of links to crawl", "maxUrlsHelp": "Maximum number of URLs to crawl", "ignoreRobots": "Ignore robots.txt", "ignoreRobotsHelp": "Ignore robots.txt files", "table": {"url": "URL", "title": "Title", "type": "Type", "tags": "Tags", "tagDescription": "Tags are used to categorize URLs for easy retrieval.", "tagsPlaceholder": "Enter tags", "activeStatus": "Active", "status": "Status", "createdAt": "Created at"}, "form": {"title": "URL Information", "createTitle": "Add New URL", "editTitle": "Edit URL", "url": "URL", "description": "Description", "type": "Type", "tags": "Tags", "status": "Status", "ownedBy": "Owned by", "isActive": "Is Active"}, "messages": {"createSuccess": "URL added successfully", "updateSuccess": "URL updated successfully", "deleteSuccess": "URL deleted successfully", "createError": "Failed to add URL", "updateError": "Failed to update URL", "deleteError": "Failed to delete URL", "crawlSuccess": "URL crawled successfully", "crawlError": "Failed to crawl URL"}, "confirmDeleteMessage": "Are you sure you want to delete this URL?"}, "common": {"confirmDelete": "Confirm Delete", "confirmBatchDelete": "Confirm Batch Delete", "active": "Active", "inactive": "Inactive", "uploading": "Uploading...", "upload": "Upload", "creating": "Creating...", "create": "Create", "processing": "Processing...", "close": "Close", "createdAt": "Created at", "all": "All", "cancel": "Cancel", "loading": "Loading...", "ascending": "Ascending", "descending": "Descending", "sortBy": "Sort by"}}}