import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon } from '@/shared/components/common';
import { ProtocolType } from '../../types';
import { getProtocolLabel } from '../../utils';

interface ProtocolBadgeProps {
  protocol: ProtocolType;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

const ProtocolBadge: React.FC<ProtocolBadgeProps> = ({
  protocol,
  size = 'md',
  showIcon = true,
  className,
}) => {
  const { t } = useTranslation(['external-agents']);

  const getProtocolIcon = (protocol: ProtocolType) => {
    switch (protocol) {
      case ProtocolType.MCP:
        return 'brain';
      case ProtocolType.GOOGLE_AGENT:
        return 'google';
      case ProtocolType.REST_API:
        return 'globe';
      case ProtocolType.WEBSOCKET:
        return 'zap';
      case ProtocolType.GRPC:
        return 'server';
      case ProtocolType.CUSTOM:
        return 'settings';
      default:
        return 'help-circle';
    }
  };

  const getProtocolColorClass = (protocol: ProtocolType) => {
    switch (protocol) {
      case ProtocolType.MCP:
        return 'text-purple-700 bg-purple-100 border-purple-200';
      case ProtocolType.GOOGLE_AGENT:
        return 'text-blue-700 bg-blue-100 border-blue-200';
      case ProtocolType.REST_API:
        return 'text-green-700 bg-green-100 border-green-200';
      case ProtocolType.WEBSOCKET:
        return 'text-orange-700 bg-orange-100 border-orange-200';
      case ProtocolType.GRPC:
        return 'text-indigo-700 bg-indigo-100 border-indigo-200';
      case ProtocolType.CUSTOM:
        return 'text-gray-700 bg-gray-100 border-gray-200';
      default:
        return 'text-gray-700 bg-gray-100 border-gray-200';
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          container: 'px-2 py-1 text-xs',
          icon: 'w-3 h-3',
          text: 'text-xs',
        };
      case 'lg':
        return {
          container: 'px-4 py-2 text-base',
          icon: 'w-5 h-5',
          text: 'text-base',
        };
      default:
        return {
          container: 'px-3 py-1.5 text-sm',
          icon: 'w-4 h-4',
          text: 'text-sm',
        };
    }
  };

  const sizeClasses = getSizeClasses(size);
  const colorClasses = getProtocolColorClass(protocol);
  const iconName = getProtocolIcon(protocol);
  const label = getProtocolLabel(protocol);

  return (
    <div 
      className={`
        inline-flex items-center gap-2 rounded-md border font-medium
        ${sizeClasses.container}
        ${colorClasses}
        ${className}
      `}
    >
      {showIcon && (
        <Icon 
          name={iconName} 
          className={sizeClasses.icon}
        />
      )}
      <Typography variant="caption" className={sizeClasses.text}>
        {t(`external-agents:protocol.${protocol}`, label)}
      </Typography>
    </div>
  );
};

export default ProtocolBadge;
