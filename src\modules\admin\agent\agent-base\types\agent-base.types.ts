/**
 * Enum cho trạng thái agent
 */
export enum AgentStatusEnum {
  DRAFT = 'DRAFT',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DEPRECATED = 'DEPRECATED',
}

/**
 * Enum cho các trường sắp xếp của agent base
 */
export enum AgentBaseSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  ACTIVE = 'active',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * Interface cho thông tin model đ<PERSON><PERSON><PERSON> sử dụng bởi agent
 */
export interface ModelInfo {
  /** ID của base model (nếu có) */
  model_base_id?: string | null;
  /** ID của finetuning model (nếu có) */
  model_finetuning_id?: string | null;
  /** Model ID từ provider */
  model_id?: string | null;
  /** Loại provider */
  typeProvider?: string | null;
}

/**
 * Interface cho cấu hình model AI
 */
export interface ModelConfig {
  /** Model ID từ provider */
  model_id?: string | null;
  /** Loại provider */
  type_provider?: string | null;
  /** Temperature cho model */
  temperature?: number;
  /** Max tokens */
  max_tokens?: number;
  /** Top P */
  top_p?: number;
  /** Frequency penalty */
  frequency_penalty?: number;
  /** Presence penalty */
  presence_penalty?: number;
}

/**
 * Interface cho thông tin vector store
 */
export interface VectorStore {
  /** ID của vector store */
  vectorStoreId: string;
  /** Tên của vector store */
  vectorStoreName: string;
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  /** ID nhân viên */
  employeeId: string;
  /** Tên nhân viên */
  name: string;
  /** Avatar nhân viên */
  avatar: string | null;
  /** Thời gian */
  date?: Date;
}

/**
 * Interface cho quan hệ multi-agent
 */
export interface MultiAgentRelation {
  /** ID của quan hệ */
  id: string;
  /** ID của agent liên quan */
  relatedAgentId: string;
  /** Tên của agent liên quan */
  relatedAgentName: string;
  /** Loại quan hệ */
  relationType: string;
}

/**
 * Interface cho agent base trong danh sách
 */
export interface AgentBaseListItem {
  /** ID của agent */
  id: string;
  /** Tên hiển thị của agent */
  name: string;
  /** URL avatar của agent */
  avatar: string | null;
  /** Tên model sử dụng */
  model: string;
  /** Model ID từ provider */
  model_id?: string | null;
  /** Loại provider */
  type_provider?: string | null;
  /** Trạng thái của agent */
  status: AgentStatusEnum;
  /** Thời gian tạo (timestamp millis) */
  createdAt: number;
  /** Trạng thái active của agent base */
  active: boolean;
}

/**
 * Interface cho thông tin chi tiết agent base
 */
export interface AgentBaseDetail {
  /** ID của agent */
  id: string;
  /** Tên hiển thị của agent */
  name: string;
  /** URL avatar của agent */
  avatar: string | null;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Thông tin model được sử dụng */
  model?: ModelInfo;
  /** Hướng dẫn hoặc system prompt cho agent */
  instruction: string | null;
  /** Thông tin vector store */
  vector?: VectorStore | null;
  /** Trạng thái của agent */
  status: AgentStatusEnum;
  /** Trạng thái active của agent base */
  active: boolean;
  /** Thông tin người tạo */
  created?: EmployeeInfo;
  /** Thông tin người cập nhật */
  updated?: EmployeeInfo;
  /** URL tải lên avatar (nếu có) */
  avatarUrlUpload?: string;
  /** Danh sách quan hệ multi-agent */
  multiAgentRelations?: MultiAgentRelation[];
}

/**
 * Interface cho agent base đã xóa trong trash
 */
export interface AgentBaseTrashItem {
  /** ID của agent */
  id: string;
  /** Tên hiển thị của agent */
  name: string;
  /** URL avatar của agent */
  avatar: string | null;
  /** Tên model sử dụng */
  model: string;
  /** Model ID từ provider */
  model_id?: string | null;
  /** Loại provider */
  type_provider?: string | null;
  /** Trạng thái của agent */
  status: AgentStatusEnum;
  /** Thời gian xóa (timestamp millis) */
  deletedAt?: number;
  /** Thông tin người xóa */
  deleted?: EmployeeInfo;
}

/**
 * Interface cho tham số tạo agent base
 */
export interface CreateAgentBaseParams {
  /** Tên hiển thị của agent */
  name: string;
  /** MIME type của avatar (nếu cần tạo avatar) */
  avatarMimeType?: string;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent */
  instruction?: string | null;
  /** ID của vector store */
  vectorStoreId?: string;
  /** Trạng thái active của agent base */
  active?: boolean;
  /** ID của base model (system model) */
  model_base_id?: string;
  /** ID của finetuning model */
  model_finetuning_id?: string;
}

/**
 * Interface cho tham số cập nhật agent base
 */
export interface UpdateAgentBaseParams {
  /** Tên hiển thị của agent */
  name?: string;
  /** MIME type của avatar (nếu cần tạo avatar) */
  avatarMimeType?: string;
  /** Cấu hình model AI */
  modelConfig?: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho agent */
  instruction?: string | null;
  /** ID của vector store */
  vectorStoreId?: string;
  /** Trạng thái active của agent base */
  active?: boolean;
  /** ID của base model (system model) */
  model_base_id?: string;
  /** ID của finetuning model */
  model_finetuning_id?: string;
}

/**
 * Interface cho tham số query agent base
 */
export interface AgentBaseQueryParams {
  /** Số trang */
  page?: number;
  /** Số lượng item trên mỗi trang */
  limit?: number;
  /** Từ khóa tìm kiếm */
  search?: string;
  /** Lọc theo trạng thái active */
  active?: boolean;
  /** Sắp xếp theo trường */
  sortBy?: AgentBaseSortBy;
  /** Hướng sắp xếp */
  sortDirection?: SortDirection;
}

/**
 * Interface cho response tạo agent base
 */
export interface CreateAgentBaseResponse {
  /** URL tải lên avatar (nếu có) */
  avatarUrlUpload: string | null;
}

/**
 * Interface cho response cập nhật agent base
 */
export interface UpdateAgentBaseResponse {
  /** URL tải lên avatar (nếu có) */
  avatarUrlUpload: string | null;
}
