import { MessageType } from './enums';

export interface AgentMessage {
  id: string;
  agentId: string;
  type: MessageType;
  content: Record<string, unknown>;
  headers?: Record<string, string>;
  metadata?: Record<string, unknown>;
  timestamp: string;
  responseTime?: number;
  status?: 'pending' | 'success' | 'error';
  error?: string;
}

export interface MessageHistory {
  agentId: string;
  messages: AgentMessage[];
  total: number;
  page: number;
  limit: number;
}

export interface MessageQueryDto {
  agentId?: string;
  type?: MessageType;
  status?: 'pending' | 'success' | 'error';
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  search?: string;
}

export interface MessageStats {
  agentId: string;
  totalMessages: number;
  messagesByType: Record<MessageType, number>;
  messagesByStatus: Record<string, number>;
  averageResponseTime: number;
  period: {
    start: string;
    end: string;
  };
}

export interface RealTimeMessage {
  agentId: string;
  message: AgentMessage;
  timestamp: string;
}

export interface MessageFilter {
  agentIds?: string[];
  types?: MessageType[];
  statuses?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
}
