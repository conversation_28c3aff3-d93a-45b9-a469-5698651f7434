import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';

import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import {
  useDatabaseConnections,
  useDatabaseIntegration,
} from '../database/hooks';
import {
  DatabaseConnectionConfig,
  DatabaseConnectionQueryParams,
  DatabaseConnectionFormData,
} from '../database/types';

import DatabaseConnectionForm from '../database/components/DatabaseConnectionForm';

/**
 * Database Integration Management Page
 * Following EmailServerManagementPage pattern
 */
const DatabaseIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [connections, setConnections] = useState<DatabaseConnectionConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const [connectionToDelete, setConnectionToDelete] = useState<DatabaseConnectionConfig | null>(null);
  const [connectionToEdit, setConnectionToEdit] = useState<DatabaseConnectionConfig | null>(null);
  const [connectionToView, setConnectionToView] = useState<DatabaseConnectionConfig | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sử dụng hook animation cho form tạo mới
  const { isVisible: isCreateFormVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const { isVisible: isEditFormVisible, showForm: showEditForm, hideForm: hideEditForm } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const { isVisible: isViewFormVisible, showForm: showViewForm, hideForm: hideViewForm } = useSlideForm();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((connection: DatabaseConnectionConfig) => {
    setConnectionToDelete(connection);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hiển thị form chỉnh sửa
  const handleShowEditForm = useCallback((connection: DatabaseConnectionConfig) => {
    setConnectionToEdit(connection);
    showEditForm();
  }, [showEditForm]);

  // Xử lý hiển thị form xem chi tiết
  const handleShowViewForm = useCallback((connection: DatabaseConnectionConfig) => {
    setConnectionToView(connection);
    showViewForm();
  }, [showViewForm]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<DatabaseConnectionConfig, DatabaseConnectionQueryParams>({
    columns: [], // Will be set later
    filterOptions: [],
    showDateFilter: false,
    createQueryParams: params => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
    }),
  });

  const dataTable = useDataTable(dataTableConfig);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<DatabaseConnectionQueryParams>(() => {
    const params: DatabaseConnectionQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm || undefined,
    };

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
  ]);

  // Hooks để gọi API
  const {
    data: connectionsData,
    isLoading: isLoadingConnections,
    error: connectionsError,
  } = useDatabaseConnections(queryParams);

  const {
    createConnection: createConnectionMutation,
    updateConnection: updateConnectionMutation,
    deleteConnection: deleteConnectionMutation,
    testConnection: testConnectionMutation,
    updateConnectionStatus: updateConnectionStatusMutation,
  } = useDatabaseIntegration();

  // Xử lý test connection
  const handleTestConnection = useCallback(async (connection: DatabaseConnectionConfig) => {
    try {
      await testConnectionMutation.mutateAsync(connection.id);
    } catch (error) {
      console.error('Error testing database connection:', error);
    }
  }, [testConnectionMutation]);

  // Xử lý thay đổi status
  const handleStatusChange = useCallback(async (connection: DatabaseConnectionConfig, status: string) => {
    try {
      await updateConnectionStatusMutation.mutateAsync({
        id: connection.id,
        status,
      });
    } catch (error) {
      console.error('Error updating connection status:', error);
    }
  }, [updateConnectionStatusMutation]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<DatabaseConnectionConfig>[]>(() => {
    const allColumns: TableColumn<DatabaseConnectionConfig>[] = [
      {
        key: 'name',
        title: t('admin:integration.database.list.columns.name'),
        dataIndex: 'name',
        width: '20%',
        sortable: true,
        render: (value: unknown, record: DatabaseConnectionConfig) => (
          <div className="flex flex-col">
            <span className="font-medium text-foreground">{String(value)}</span>
            <span className="text-sm text-muted-foreground">{record.displayName}</span>
          </div>
        ),
      },
      {
        key: 'type',
        title: t('admin:integration.database.list.columns.type'),
        dataIndex: 'type',
        width: '15%',
        sortable: true,
        render: (value: unknown) => (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {String(value).toUpperCase()}
          </span>
        ),
      },
      {
        key: 'host',
        title: t('admin:integration.database.list.columns.host'),
        dataIndex: 'credentials',
        width: '20%',
        render: (credentials: unknown) => {
          const creds = credentials as Record<string, unknown>;
          if (creds?.host) {
            const port = creds.port as string | number | undefined;
            return (
              <div className="flex flex-col">
                <span className="text-foreground">{String(creds.host)}</span>
                {port && (
                  <span className="text-sm text-muted-foreground">Port: {String(port)}</span>
                )}
              </div>
            );
          }
          if (creds?.connectionString) {
            return (
              <span className="text-foreground font-mono text-sm">
                {String(creds.connectionString).substring(0, 30)}...
              </span>
            );
          }
          if (creds?.filePath) {
            return (
              <span className="text-foreground font-mono text-sm">
                {String(creds.filePath)}
              </span>
            );
          }
          return <span className="text-muted-foreground">-</span>;
        },
      },
      {
        key: 'database',
        title: t('admin:integration.database.list.columns.database'),
        dataIndex: 'credentials',
        width: '15%',
        render: (credentials: unknown) => {
          const creds = credentials as Record<string, unknown>;
          const dbName = creds?.database || creds?.collection;
          return dbName ? (
            <span className="text-foreground">{String(dbName)}</span>
          ) : (
            <span className="text-muted-foreground">-</span>
          );
        },
      },
      {
        key: 'status',
        title: t('admin:integration.database.list.columns.status'),
        dataIndex: 'status',
        width: '12%',
        render: (value: unknown) => {
          const status = String(value);
          const statusColors = {
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-gray-100 text-gray-800',
            error: 'bg-red-100 text-red-800',
            testing: 'bg-yellow-100 text-yellow-800',
            pending: 'bg-blue-100 text-blue-800',
          };
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
            }`}>
              {t(`admin:integration.database.status.${status}`, status)}
            </span>
          );
        },
      },
      {
        key: 'isDefault',
        title: t('admin:integration.database.list.columns.default'),
        dataIndex: 'isDefault',
        width: '8%',
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {value ? t('common:yes') : t('common:no')}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('admin:integration.database.list.columns.actions'),
        width: '10%',
        render: (_: unknown, record: DatabaseConnectionConfig) => {
          const menuItems = [
            {
              label: t('admin:integration.database.actions.edit'),
              icon: 'edit',
              onClick: () => handleShowEditForm(record),
            },
            {
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('admin:integration.database.actions.test'),
              icon: 'link',
              onClick: () => handleTestConnection(record),
            },
            {
              type: 'divider' as const,
            },
            {
              label: record.status === 'active' 
                ? t('admin:integration.database.actions.deactivate')
                : t('admin:integration.database.actions.activate'),
              icon: record.status === 'active' ? 'pause' : 'play',
              onClick: () => handleStatusChange(record, record.status === 'active' ? 'inactive' : 'active'),
            },
            {
              label: t('admin:integration.database.actions.delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowEditForm, handleShowViewForm, handleTestConnection, handleStatusChange]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });



  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (connectionsData?.items) {
      setConnections(connectionsData.items || []);
      setTotalItems(connectionsData.total || 0);
    }

    setIsLoading(isLoadingConnections);
  }, [connectionsData, connectionsError, isLoadingConnections]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback(
    (term: string) => {
      dataTable.tableData.handleSearch(term);
    },
    [dataTable.tableData]
  );

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback(
    (column: string | null, order: SortOrder) => {
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setConnectionToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!connectionToDelete) return;

    try {
      await deleteConnectionMutation.mutateAsync(connectionToDelete.id);
      setShowDeleteConfirm(false);
      setConnectionToDelete(null);
    } catch (error) {
      console.error('Error deleting database connection:', error);
    }
  }, [connectionToDelete, deleteConnectionMutation]);

  // Xử lý submit form tạo mới
  const handleSubmitCreateConnection = useCallback(
    async (values: DatabaseConnectionFormData) => {
      try {
        setIsSubmitting(true);
        await createConnectionMutation.mutateAsync(values);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating database connection:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createConnectionMutation, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditConnection = useCallback(
    async (values: DatabaseConnectionFormData) => {
      if (!connectionToEdit) return;

      try {
        setIsSubmitting(true);
        await updateConnectionMutation.mutateAsync({
          id: connectionToEdit.id,
          data: values,
        });
        hideEditForm();
        setConnectionToEdit(null);
      } catch (error) {
        console.error('Error updating database connection:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [connectionToEdit, updateConnectionMutation, hideEditForm]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setConnectionToEdit(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setTimeout(() => {
      setConnectionToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<DatabaseConnectionConfig>[]>(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns([
        { id: 'all', label: t('common:all'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-4">
        <div>
          {/* MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <DatabaseConnectionForm
            onSubmit={handleSubmitCreateConnection}
            onCancel={hideCreateForm}
            loading={isSubmitting}
            mode="create"
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {connectionToEdit && (
            <DatabaseConnectionForm
              initialData={connectionToEdit}
              onSubmit={handleSubmitEditConnection}
              onCancel={handleCloseEditForm}
              loading={isSubmitting}
              mode="edit"
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {connectionToView && (
            <DatabaseConnectionForm
              initialData={connectionToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              loading={false}
              mode="view"
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<DatabaseConnectionConfig>
            columns={filteredColumns}
            data={connections}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}

          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.database.confirmations.deleteTitle')}
        message={t('admin:integration.database.confirmations.delete')}
        itemName={connectionToDelete?.name}
      />
    </div>
  );
};

export default DatabaseIntegrationPage;
