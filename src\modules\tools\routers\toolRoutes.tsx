import { RouteObject } from 'react-router-dom';
import { Suspense } from 'react';

import { Loading } from '@/shared';
import { ToolsPage, ToolManagementPage } from '../pages';
import MainLayout from '@/shared/layouts/MainLayout';

/**
 * Routes cho module tools của user
 */
const toolRoutes: RouteObject[] = [
  // Trang tổng quan quản lý tools
  {
    path: '/tools',
    element: (
      <MainLayout title="Tool Management">
        <Suspense fallback={<Loading />}>
          <ToolManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang danh sách tools
  {
    path: '/tools/list',
    element: (
      <MainLayout title="Tools List">
        <Suspense fallback={<Loading />}>
          <ToolsPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default toolRoutes;
