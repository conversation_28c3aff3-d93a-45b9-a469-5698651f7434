import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { PlanPricingAdminService } from '../services/plan-pricing.admin.service';
import { PLAN_PRICING_ADMIN_QUERY_KEYS } from '../constants/plan-pricing.admin.query-keys';
import {
  GetPlanPricingsQueryDto,
  CreatePlanPricingDto,
  UpdatePlanPricingDto,
} from '../types/plan-pricing.admin.types';

/**
 * Interface cho lỗi API response
 */
interface ApiErrorResponse {
  message?: string;
  code?: number;
  errors?: Record<string, string>;
}

/**
 * Hook để lấy danh sách plan pricing với phân trang
 */
export const useGetPlanPricings = (params: GetPlanPricingsQueryDto) => {
  return useQuery({
    queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.list(params),
    queryFn: () => PlanPricingAdminService.getPlanPricings(params),
    staleTime: 5 * 60 * 1000, // 5 phút
    gcTime: 10 * 60 * 1000, // 10 phút
  });
};

/**
 * Hook để lấy thông tin chi tiết plan pricing theo ID
 */
export const useGetPlanPricingById = (id: number, enabled = true) => {
  return useQuery({
    queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.detail(id),
    queryFn: () => PlanPricingAdminService.getPlanPricingById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 phút
    gcTime: 10 * 60 * 1000, // 10 phút
  });
};

/**
 * Hook để lấy danh sách plan pricing theo plan ID
 */
export const useGetPlanPricingsByPlanId = (
  planId: number,
  params?: Omit<GetPlanPricingsQueryDto, 'planId'>,
  enabled = true
) => {
  return useQuery({
    queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.byPlan(planId),
    queryFn: () => PlanPricingAdminService.getPlanPricingsByPlanId(planId, params),
    enabled: enabled && !!planId,
    staleTime: 5 * 60 * 1000, // 5 phút
    gcTime: 10 * 60 * 1000, // 10 phút
  });
};

/**
 * Hook để tạo plan pricing mới
 */
export const useCreatePlanPricing = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (data: CreatePlanPricingDto) =>
      PlanPricingAdminService.createPlanPricing(data),
    onSuccess: (response) => {
      // Invalidate và refetch các queries liên quan
      queryClient.invalidateQueries({
        queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.all,
      });

      notification.success({
        title: 'Thành công',
        message: response.message || 'Tạo tùy chọn giá mới thành công',
      });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      notification.error({
        title: 'Lỗi',
        message: error?.response?.data?.message || 'Có lỗi xảy ra khi tạo tùy chọn giá',
      });
    },
  });
};

/**
 * Hook để cập nhật plan pricing
 */
export const useUpdatePlanPricing = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdatePlanPricingDto }) =>
      PlanPricingAdminService.updatePlanPricing(id, data),
    onSuccess: (response, variables) => {
      // Invalidate và refetch các queries liên quan
      queryClient.invalidateQueries({
        queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.all,
      });

      // Cập nhật cache cho detail query
      queryClient.setQueryData(
        PLAN_PRICING_ADMIN_QUERY_KEYS.detail(variables.id),
        response
      );

      notification.success({
        title: 'Thành công',
        message: response.message || 'Cập nhật tùy chọn giá thành công',
      });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      notification.error({
        title: 'Lỗi',
        message: error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật tùy chọn giá',
      });
    },
  });
};

/**
 * Hook để xóa plan pricing
 */
export const useDeletePlanPricing = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: (id: number) => PlanPricingAdminService.deletePlanPricing(id),
    onSuccess: (response, id) => {
      // Invalidate và refetch các queries liên quan
      queryClient.invalidateQueries({
        queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.all,
      });

      // Remove từ cache
      queryClient.removeQueries({
        queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.detail(id),
      });

      notification.success({
        title: 'Thành công',
        message: response.message || 'Xóa tùy chọn giá thành công',
      });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      notification.error({
        title: 'Lỗi',
        message: error?.response?.data?.message || 'Có lỗi xảy ra khi xóa tùy chọn giá',
      });
    },
  });
};

/**
 * Hook để toggle trạng thái active/inactive của plan pricing
 */
export const useTogglePlanPricingStatus = () => {
  const queryClient = useQueryClient();
  const notification = useSmartNotification();

  return useMutation({
    mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
      PlanPricingAdminService.togglePlanPricingStatus(id, isActive),
    onSuccess: (response, variables) => {
      // Invalidate và refetch các queries liên quan
      queryClient.invalidateQueries({
        queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.all,
      });

      // Cập nhật cache cho detail query
      queryClient.setQueryData(
        PLAN_PRICING_ADMIN_QUERY_KEYS.detail(variables.id),
        response
      );

      const statusText = variables.isActive ? 'kích hoạt' : 'vô hiệu hóa';
      notification.success({
        title: 'Thành công',
        message: `${statusText} tùy chọn giá thành công`,
      });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      notification.error({
        title: 'Lỗi',
        message: error?.response?.data?.message || 'Có lỗi xảy ra khi thay đổi trạng thái',
      });
    },
  });
};

/**
 * Hook để lấy danh sách plan pricing active
 */
export const useGetActivePlanPricings = (
  params?: Omit<GetPlanPricingsQueryDto, 'isActive'>
) => {
  return useQuery({
    queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.byStatus(true),
    queryFn: () => PlanPricingAdminService.getActivePlanPricings(params),
    staleTime: 5 * 60 * 1000, // 5 phút
    gcTime: 10 * 60 * 1000, // 10 phút
  });
};

/**
 * Hook để lấy danh sách plan pricing inactive
 */
export const useGetInactivePlanPricings = (
  params?: Omit<GetPlanPricingsQueryDto, 'isActive'>
) => {
  return useQuery({
    queryKey: PLAN_PRICING_ADMIN_QUERY_KEYS.byStatus(false),
    queryFn: () => PlanPricingAdminService.getInactivePlanPricings(params),
    staleTime: 5 * 60 * 1000, // 5 phút
    gcTime: 10 * 60 * 1000, // 10 phút
  });
};
