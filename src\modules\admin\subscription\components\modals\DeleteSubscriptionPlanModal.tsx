import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDeleteSubscriptionPlan } from '../../hooks/useSubscriptionPlansAdmin';
import { SubscriptionPlan } from '../../types/subscription-plans.admin.types';
import ConfirmDeleteModal from '../../../marketplace/components/modals/ConfirmDeleteModal';

interface DeleteSubscriptionPlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: SubscriptionPlan;
  onSuccess: () => void;
}

/**
 * Modal xác nhận xóa gói dịch vụ subscription
 */
const DeleteSubscriptionPlanModal: React.FC<DeleteSubscriptionPlanModalProps> = ({
  isOpen,
  onClose,
  plan,
  onSuccess,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { mutateAsync: deletePlan, isPending: isDeleting } = useDeleteSubscriptionPlan();

  // Xử lý xóa
  const handleDelete = async () => {
    try {
      await deletePlan(plan.id);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error deleting subscription plan:', error);
    }
  };

  return (
    <ConfirmDeleteModal
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={handleDelete}
      title={t('admin:subscription.plan.delete.modal.title')}
      message={t('admin:subscription.plan.delete.modal.message')}
      itemName={plan.name}
      isSubmitting={isDeleting}
    />
  );
};

export default DeleteSubscriptionPlanModal;
