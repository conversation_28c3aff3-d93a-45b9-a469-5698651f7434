import { z } from 'zod';
import { TFunction } from 'i18next';

// Define E164Number type locally since it's not exported
type E164Number = string;

/**
 * Login form values interface
 */
export interface LoginFormValues {
  username: string;
  password: string;
  rememberMe: boolean;
  recaptchaToken?: string;
}

/**
 * Register form values interface
 */
export interface RegisterFormValues {
  fullName: string;
  email: string;
  phone: E164Number | undefined;
  countryCode: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Forgot password form values interface
 */
export interface ForgotPasswordFormValues {
  emailOrPhone: string;
}

/**
 * Create login schema with translations
 */
export const createLoginSchema = (t: TFunction) => {
  return z.object({
    username: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
    password: z
      .string()
      .min(1, t('validation.required', { field: t('auth.password') }))
      .min(6, t('validation.minLength', { field: t('auth.password'), length: 6 })),
    rememberMe: z.boolean().optional(),
    recaptchaToken: z.string().optional(),
  });
};

/**
 * Create register schema with translations
 */
export const createRegisterSchema = (t: TFunction) => {
  return z.object({
    fullName: z
      .string()
      .min(1, t('validation.required', { field: t('auth.fullName') }))
      .min(2, t('validation.minLength', { field: t('auth.fullName'), length: 2 })),
    email: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
    phone: z
      .string()
      .min(1, t('validation.required', { field: t('auth.phone') }))
      .refine((value) => {
        // Nếu không có giá trị, để validation required xử lý
        if (!value) return true;
        // Kiểm tra định dạng số điện thoại cơ bản (không có country code)
        return /^[0-9]{8,15}$/.test(value);
      }, t('validation.phone'))
      .transform((value) => value as E164Number | undefined),
    countryCode: z
      .string()
      .min(1, t('validation.required', { field: t('auth.countryCode', 'Mã quốc gia') }))
      .regex(/^\+[1-9]\d{0,3}$/, t('validation.countryCode', 'Mã quốc gia không hợp lệ')),
    password: z
      .string()
      .min(1, t('validation.required', { field: t('auth.password') }))
      .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
      .regex(/[A-Z]/, t('validation.passwordUppercase'))
      .regex(/[a-z]/, t('validation.passwordLowercase'))
      .regex(/[0-9]/, t('validation.passwordNumber'))
      .regex(/[^A-Za-z0-9]/, t('validation.passwordSpecial')),
    recaptchaToken: z.string().optional(),
  });
};

/**
 * Create forgot password schema with translations
 */
export const createForgotPasswordSchema = (t: TFunction) => {
  return z.object({
    email: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
  });
};

/**
 * Reset password form values interface
 */
export interface ResetPasswordFormValues {
  password: string;
  confirmPassword: string;
}

/**
 * Create reset password schema with translations
 */
export const createResetPasswordSchema = (t: TFunction) => {
  return z
    .object({
      password: z
        .string()
        .min(1, t('validation.required', { field: t('auth.password') }))
        .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
        .regex(/[A-Z]/, t('validation.passwordUppercase'))
        .regex(/[a-z]/, t('validation.passwordLowercase'))
        .regex(/[0-9]/, t('validation.passwordNumber'))
        .regex(/[^A-Za-z0-9]/, t('validation.passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('validation.required', { field: t('auth.confirmPassword') })),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation.passwordsMatch'),
      path: ['confirmPassword'],
    });
};
