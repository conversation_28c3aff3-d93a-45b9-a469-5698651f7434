import { ExternalAgentStatus, ProtocolType, AuthenticationType, AgentCapability } from './enums';

export interface ExternalAgent {
  id: string;
  name: string;
  description?: string;
  status: ExternalAgentStatus;
  protocol: ProtocolType;
  endpoint: string;
  authentication: AuthenticationConfig;
  capabilities: AgentCapability[];
  metadata?: Record<string, unknown>;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  lastConnectedAt?: string;
  version?: string;
  isActive: boolean;
}

export interface AuthenticationConfig {
  type: AuthenticationType;
  credentials?: Record<string, string>;
  headers?: Record<string, string>;
  parameters?: Record<string, string>;
}

export interface ExternalAgentCreateDto {
  name: string;
  description?: string;
  protocol: ProtocolType;
  endpoint: string;
  authentication: AuthenticationConfig;
  capabilities?: AgentCapability[];
  metadata?: Record<string, unknown>;
  tags?: string[];
}

export interface ExternalAgentUpdateDto extends Partial<ExternalAgentCreateDto> {
  status?: ExternalAgentStatus;
  isActive?: boolean;
}

export interface ExternalAgentQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: ExternalAgentStatus;
  protocol?: ProtocolType;
  capabilities?: AgentCapability[];
  tags?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ExternalAgentListResponse {
  items: ExternalAgent[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ConnectionTestResult {
  success: boolean;
  responseTime?: number;
  error?: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

export interface AgentPerformanceMetrics {
  agentId: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  uptime: number;
  lastUpdated: string;
}
