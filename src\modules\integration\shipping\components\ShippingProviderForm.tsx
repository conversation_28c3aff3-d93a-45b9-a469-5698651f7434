import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Select,
  Switch,
  Textarea
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import {
  ShippingProviderConfiguration,
  ShippingProviderType,
  ShippingProviderConfigurationFormData,
  CreateShippingProviderDto,
  UpdateShippingProviderDto
} from '../types';
import { shippingProviderConfigurationSchema } from '../schemas';
import {
  useCreateShippingProviderConfiguration,
  useUpdateShippingProviderConfiguration
} from '../hooks';

interface ShippingProviderFormProps {
  provider?: ShippingProviderConfiguration | null;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form component cho việc thêm/sửa nhà vận chuyển
 */
const ShippingProviderForm: React.FC<ShippingProviderFormProps> = ({
  provider,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef, setFormErrors } = useFormErrors<ShippingProviderConfigurationFormData>();

  // Mutations
  const createProviderMutation = useCreateShippingProviderConfiguration();
  const updateProviderMutation = useUpdateShippingProviderConfiguration();

  const isLoading = createProviderMutation.isPending || updateProviderMutation.isPending;
  const [formData, setFormData] = useState<ShippingProviderConfigurationFormData>({
    providerType: 'ghn',
    providerName: '',
    apiKey: '',
    apiSecret: '',
    shopId: '',
    clientId: '',
    isActive: true,
    isDefault: false,
    settings: '',
  });

  const isEditing = !!provider;

  // Provider type options
  const providerOptions = [
    { value: 'ghn', label: 'GHN - Giao Hàng Nhanh' },
    { value: 'ghtk', label: 'GHTK - Giao Hàng Tiết Kiệm' },
    { value: 'viettel-post', label: 'Viettel Post' },
    { value: 'vnpost', label: 'VNPost' },
  ];

  // Load provider data when editing
  useEffect(() => {
    if (provider) {
      setFormData({
        providerType: provider.providerType,
        providerName: provider.providerName,
        apiKey: provider.apiKey,
        apiSecret: provider.apiSecret || '',
        shopId: provider.shopId || '',
        clientId: provider.clientId || '',
        isActive: provider.isActive,
        isDefault: provider.isDefault,
        settings: provider.settings ? JSON.stringify(provider.settings, null, 2) : '',
      });
    }
  }, [provider]);

  // Handle form field changes
  const handleFieldChange = (field: keyof ShippingProviderConfigurationFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    try {
      // Validate form data
      const validatedData = shippingProviderConfigurationSchema.parse(formData);

      // Prepare data for API
      const apiData = {
        providerType: validatedData.providerType,
        providerName: validatedData.providerName,
        apiKey: validatedData.apiKey,
        apiSecret: validatedData.apiSecret,
        shopId: validatedData.shopId,
        clientId: validatedData.clientId,
        isActive: validatedData.isActive,
        isDefault: validatedData.isDefault,
        settings: validatedData.settings ? JSON.parse(validatedData.settings) : undefined,
      };

      if (isEditing && provider) {
        // Update existing provider
        updateProviderMutation.mutate(
          { id: provider.id, data: apiData as UpdateShippingProviderDto },
          {
            onSuccess: () => {
              onSuccess?.();
            },
            onError: (error: any) => {
              console.error('Update provider error:', error);
            }
          }
        );
      } else {
        // Create new provider
        createProviderMutation.mutate(
          apiData as CreateShippingProviderDto,
          {
            onSuccess: () => {
              onSuccess?.();
            },
            onError: (error: any) => {
              console.error('Create provider error:', error);
            }
          }
        );
      }
    } catch (error: any) {
      if (error.errors) {
        // Handle Zod validation errors
        const fieldErrors: Partial<ShippingProviderConfigurationFormData> = {};
        error.errors.forEach((err: any) => {
          if (err.path && err.path.length > 0) {
            fieldErrors[err.path[0] as keyof ShippingProviderConfigurationFormData] = err.message;
          }
        });
        setFormErrors(fieldErrors);
      } else {
        console.error('Form submission error:', error);
      }
    }
  };

  // Get provider-specific fields
  const getProviderSpecificFields = () => {
    switch (formData.providerType) {
      case 'ghn':
        return (
          <FormItem label="Shop ID" name="shopId" required>
            <Input
              value={formData.shopId}
              onChange={(e) => handleFieldChange('shopId', e.target.value)}
              placeholder="Nhập Shop ID từ GHN"
            />
          </FormItem>
        );
      case 'ghtk':
        return (
          <FormItem label="Partner ID" name="clientId">
            <Input
              value={formData.clientId}
              onChange={(e) => handleFieldChange('clientId', e.target.value)}
              placeholder="Nhập Partner ID từ GHTK"
            />
          </FormItem>
        );
      case 'viettel-post':
        return (
          <>
            <FormItem label="Client ID" name="clientId">
              <Input
                value={formData.clientId}
                onChange={(e) => handleFieldChange('clientId', e.target.value)}
                placeholder="Nhập Client ID từ Viettel Post"
              />
            </FormItem>
            <FormItem label="API Secret" name="apiSecret">
              <Input
                type="password"
                value={formData.apiSecret}
                onChange={(e) => handleFieldChange('apiSecret', e.target.value)}
                placeholder="Nhập API Secret"
              />
            </FormItem>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <Icon name="truck" size="lg" className="text-primary" />
          <Typography variant="h3">
            {isEditing
              ? t('integration:shipping.editProvider', 'Chỉnh sửa nhà vận chuyển')
              : t('integration:shipping.addProvider', 'Thêm nhà vận chuyển')
            }
          </Typography>
        </div>

        {/* Form */}
        <Form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
          {/* Provider Type */}
          <FormItem label="Loại nhà vận chuyển" name="providerType" required>
            <Select
              value={formData.providerType}
              onChange={(value) => handleFieldChange('providerType', value as ShippingProviderType)}
              options={providerOptions}
              placeholder="Chọn nhà vận chuyển"
              disabled={isEditing} // Không cho phép thay đổi type khi edit
            />
          </FormItem>

          {/* Provider Name */}
          <FormItem label="Tên hiển thị" name="providerName" required>
            <Input
              value={formData.providerName}
              onChange={(e) => handleFieldChange('providerName', e.target.value)}
              placeholder="Nhập tên hiển thị cho nhà vận chuyển"
            />
          </FormItem>

          {/* API Key */}
          <FormItem label="API Key" name="apiKey" required>
            <Input
              type="password"
              value={formData.apiKey}
              onChange={(e) => handleFieldChange('apiKey', e.target.value)}
              placeholder="Nhập API Key"
            />
          </FormItem>

          {/* Provider-specific fields */}
          {getProviderSpecificFields()}

          {/* Settings */}
          <FormItem label="Cấu hình nâng cao (JSON)" name="settings">
            <Textarea
              value={formData.settings}
              onChange={(e) => handleFieldChange('settings', e.target.value)}
              placeholder='{"webhookUrl": "https://example.com/webhook", "enableWebhook": true}'
              rows={4}
            />
          </FormItem>

          {/* Status toggles */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label="Trạng thái hoạt động" name="isActive">
              <Switch
                checked={formData.isActive}
                onChange={(checked) => handleFieldChange('isActive', checked)}
              />
            </FormItem>

            <FormItem label="Đặt làm mặc định" name="isDefault">
              <Switch
                checked={formData.isDefault}
                onChange={(checked) => handleFieldChange('isDefault', checked)}
              />
            </FormItem>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t border-border">
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                className="flex-1"
                disabled={isLoading}
              >
                {t('common:cancel')}
              </Button>
            )}

            <Button
              type="submit"
              variant="primary"
              className="flex-1"
              isLoading={isLoading}
              leftIcon={<Icon name={isEditing ? "save" : "plus"} size="sm" />}
            >
              {isEditing
                ? t('common:save')
                : t('integration:shipping.create', 'Tạo')
              }
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default ShippingProviderForm;
