import React from 'react';
import { Card, Typography, Button, Icon } from '@/shared/components/common';

interface ShippingProviderFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Placeholder component for Shipping Provider Form
 */
const ShippingProviderForm: React.FC<ShippingProviderFormProps> = ({ onSuccess, onCancel }) => {
  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Icon name="truck" size="lg" className="text-primary" />
          <Typography variant="h3">
            Thêm nhà vận chuyển
          </Typography>
        </div>

        <div className="space-y-4">
          <Typography variant="body1" className="text-muted-foreground">
            Form cấu hình nhà vận chuyển sẽ được triển khai ở đây.
          </Typography>
          
          <div className="flex gap-3 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                className="flex-1"
              >
                Hủy
              </Button>
            )}

            <Button
              type="button"
              variant="primary"
              onClick={onSuccess}
              className="flex-1"
            >
              Tạo
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ShippingProviderForm;
