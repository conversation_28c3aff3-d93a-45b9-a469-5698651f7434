/**
 * Đ<PERSON><PERSON> nghĩa các kiểu dữ liệu cho module tool admin
 */

/**
 * Enum định nghĩa các trạng thái của tool
 */
export enum ToolStatus {
  /**
   * Bản nháp - chưa gửi duyệt
   */
  DRAFT = 'DRAFT',

  /**
   * Đ<PERSON> được duyệt
   */
  APPROVED = 'APPROVED',

  /**
   * Không còn sử dụng
   */
  DEPRECATED = 'DEPRECATED',
}

/**
 * Enum định nghĩa các loại quyền truy cập
 */
export enum AccessType {
  /**
   * Công khai - tất cả người dùng có thể sử dụng
   */
  PUBLIC = 'PUBLIC',

  /**
   * Riêng tư - chỉ người tạo có thể sử dụng
   */
  PRIVATE = 'PRIVATE',

  /**
   * Hạn chế - chỉ một số người dùng được chỉ định có thể sử dụng
   */
  RESTRICTED = 'RESTRICTED',
}

/**
 * Enum cho các trường sắp xếp tool
 */
export enum ToolSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  DELETED_AT = 'deletedAt',
}

/**
 * Interface cho tham số của tool
 */
export interface ToolParameter {
  name: string;
  type: string;
  description?: string;
  required: boolean;
  defaultValue?: unknown;
  options?: unknown[];
}

/**
 * Interface cho thông tin phiên bản tool
 */
export interface ToolVersion {
  id: string;
  versionNumber: number;
  toolName: string;
  toolDescription: string | null;
  parameters: Record<string, unknown>;
  changeDescription: string | null;
  status: ToolStatus;
  createdAt: number;
  createdBy: EmployeeInfo;
  updatedAt: number;
}

/**
 * Interface cho thông tin nhân viên
 */
export interface EmployeeInfo {
  id: number;
  name: string;
  email: string;
  avatar?: string;
}

/**
 * Interface cho thông tin tool trong danh sách
 */
export interface ToolListItem {
  id: string;
  name: string;
  description: string | null;
  status: ToolStatus;
  accessType: AccessType;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number | string; // Thêm field cho ngày xóa (dành cho trash) - API có thể trả về string hoặc number
  deletedBy?: { name: string | null; email: string | null }; // Thông tin người xóa
  createdBy: EmployeeInfo;
  category?: string; // Thêm field cho danh mục
  tags?: string[]; // Thêm field cho tags
  groups?: unknown[]; // Danh sách nhóm
  versionDefault?: unknown; // Phiên bản mặc định
  versionName?: string | null; // Tên phiên bản
}

/**
 * Interface cho thông tin chi tiết tool
 */
export interface ToolDetail extends ToolListItem {
  defaultVersion: ToolVersion | null;
  versions: ToolVersion[];
}

/**
 * Interface cho tham số truy vấn danh sách tool
 */
export interface ToolQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ToolStatus;
  accessType?: AccessType;
  sortBy?: ToolSortBy;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho kết quả phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

/**
 * Interface cho dữ liệu tạo tool mới
 */
export interface CreateToolParams {
  name: string;
  description?: string;
  toolName: string;
  toolDescription?: string;
  parameters: Record<string, unknown>;
  status?: ToolStatus;
  accessType?: AccessType;
}

/**
 * Interface cho dữ liệu cập nhật tool
 */
export interface UpdateToolParams {
  name?: string;
  description?: string;
  status?: ToolStatus;
  accessType?: AccessType;
}

/**
 * Interface cho dữ liệu tạo phiên bản tool mới
 */
export interface CreateToolVersionParams {
  toolName: string;
  toolDescription?: string;
  parameters: Record<string, unknown>;
  changeDescription?: string;
  status?: ToolStatus;
}

/**
 * Interface cho dữ liệu cập nhật phiên bản tool
 */
export interface UpdateToolVersionParams {
  toolName?: string;
  toolDescription?: string;
  parameters?: Record<string, unknown>;
  changeDescription?: string;
  status?: ToolStatus;
}

/**
 * Interface cho thông tin nhóm tool trong danh sách
 */
export interface ToolGroupListItem {
  id: number;
  name: string;
  description: string | null;
  toolCount: number;
  createdAt: number;
  updatedAt: number;
  createdBy: EmployeeInfo;
}

/**
 * Interface cho thông tin chi tiết nhóm tool
 */
export interface ToolGroupDetail extends ToolGroupListItem {
  tools: ToolListItem[];
}

/**
 * Interface cho tham số truy vấn danh sách nhóm tool
 */
export interface ToolGroupQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho dữ liệu tạo nhóm tool mới
 */
export interface CreateToolGroupParams {
  name: string;
  description?: string;
  toolIds?: string[];
}

/**
 * Interface cho dữ liệu cập nhật nhóm tool
 */
export interface UpdateToolGroupParams {
  name?: string;
  description?: string;
}

/**
 * Interface cho dữ liệu cập nhật danh sách tool trong nhóm
 */
export interface UpdateToolGroupToolsParams {
  toolIds: string[];
}
