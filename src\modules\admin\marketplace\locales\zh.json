{"marketplace": {"title": "市场管理", "description": "管理市场系统中的产品、订单和购物车。", "products": "产品", "productsDescription": "管理市场中的产品，包括添加、编辑、删除和审批产品。", "totalProducts": "产品总数", "manageProducts": "管理产品", "orders": "订单", "ordersDescription": "管理客户订单，跟踪状态并处理订单。", "totalOrders": "订单总数", "manageOrders": "管理订单", "cartDescription": "查看和管理系统中的客户购物车。", "totalCarts": "购物车总数", "manageCarts": "管理购物车", "product": {"addNew": "添加新产品", "editProduct": "编辑产品", "basicInfo": "基本信息", "priceInfo": "价格信息", "mediaTypes": "文件和图片", "bulkUpdateStatus": "更新状态", "confirmBulkDeleteMessage": "您确定要删除 {{count}} 个选中的产品吗？", "confirmBulkStatusUpdateMessage": "为 {{count}} 个选中的产品选择新状态：", "selectStatus": "选择状态", "publish": "发布", "createSuccess": "成功", "createSuccessMessage": "产品已成功创建", "createError": "错误", "createErrorMessage": "无法创建产品。请重试。", "updateSuccess": "成功", "updateSuccessMessage": "产品已成功更新", "updateError": "错误", "updateErrorMessage": "无法更新产品。请重试。", "publishSuccess": "成功", "publishSuccessMessage": "产品 \"{{name}}\" 已成功发布", "publishError": "错误", "publishErrorMessage": "无法发布产品。请重试。", "bulkDeleteSuccess": "成功", "bulkDeleteSuccessMessage": "已成功删除 {{count}} 个产品", "bulkDeleteError": "错误", "bulkDeleteErrorMessage": "无法删除产品。请重试。", "bulkUpdateSuccess": "成功", "bulkUpdateSuccessMessage": "已为 {{count}} 个产品更新状态", "bulkUpdateError": "错误", "bulkUpdateErrorMessage": "无法更新产品状态。请重试。", "table": {"name": "产品名称", "category": "类别", "price": "价格", "status": "状态", "seller": "卖家", "actions": "操作"}, "category": {"KNOWLEDGE_FILE": "知识文件", "AGENT": "代理", "TEMPLATE": "模板", "OTHER": "其他"}, "form": {"name": "产品名称", "namePlaceholder": "输入产品名称", "description": "描述", "descriptionPlaceholder": "输入产品描述", "category": "产品类别", "categoryPlaceholder": "选择类别", "sourceId": "源ID", "sourceIdPlaceholder": "搜索并选择源", "listedPrice": "标价", "listedPricePlaceholder": "输入标价", "discountedPrice": "折扣价", "discountedPricePlaceholder": "输入折扣价", "images": "产品图片", "imagesHelp": "支持格式：JPG、PNG", "imagePlaceholder": "拖拽或点击上传产品图片", "imageSupport": "支持多张图片，格式：JPG、PNG", "selectedImages": "已选择 {{count}} 张新图片", "selectImages": "选择图片", "userManual": "用户手册", "userManualPlaceholder": "输入用户手册（如有更改）", "userManualHelp": "支持格式：PDF", "selectFiles": "选择文件", "selectedFiles": "已选择文件", "files": "文件", "noFilesSelected": "未选择文件", "removeAll": "删除全部", "detail": "详细文档", "detailPlaceholder": "输入详细产品信息（如有更改）", "detailHelp": "支持格式：PDF", "selectFile": "选择文件", "changeFile": "更改文件", "additionalInfo": "附加信息", "publishAfterUpdate": "更新后发布"}, "validation": {"nameMin": "产品名称至少需要3个字符", "nameMax": "产品名称不能超过500个字符", "descriptionRequired": "产品描述是必需的", "listedPriceRequired": "标价是必需的", "listedPriceMin": "标价不能为负数", "discountedPriceRequired": "折扣价是必需的", "discountedPriceMin": "折扣价不能为负数", "categoryRequired": "产品类别是必需的", "sourceIdRequired": "源ID是必需的"}, "status": {"APPROVED": "已批准", "PENDING": "待审核", "REJECTED": "已拒绝", "DRAFT": "草稿", "DELETED": "已删除"}, "approve": "批准", "reject": "拒绝", "confirmDeleteMessage": "您确定要删除此产品吗？", "confirmApproveMessage": "您确定要批准此产品吗？", "rejectReason": "拒绝原因", "rejectReasonPlaceholder": "输入拒绝产品的原因", "confirmRejectMessage": "您确定要拒绝此产品吗？"}, "order": {"details": "订单详情", "edit": "编辑订单", "addNew": "添加新订单", "info": "订单信息", "table": {"id": "订单ID", "customer": "客户", "total": "总计", "status": "状态", "type": "类型", "createdAt": "创建时间", "actions": "操作"}, "form": {"orderNumber": "订单号", "userId": "用户ID", "status": "状态", "type": "订单类型", "createdAt": "创建时间", "totalPrice": "总价"}, "item": {"product": "产品", "quantity": "数量", "price": "价格", "total": "小计"}, "items": {"empty": "无商品"}, "status": {"PENDING": "待处理", "PROCESSING": "处理中", "COMPLETED": "已完成", "SHIPPED": "已发货", "DELIVERED": "已送达", "CANCELLED": "已取消", "FAILED": "失败", "REFUNDED": "已退款"}, "type": {"PURCHASE": "购买", "SUBSCRIPTION": "订阅", "REFUND": "退款"}, "confirmDeleteMessage": "您确定要删除此订单吗？", "confirmCancelMessage": "您确定要取消此订单吗？", "cancelReason": "取消原因", "cancelReasonPlaceholder": "输入取消订单的原因"}, "cart": {"cart": "购物车", "details": "购物车详情", "info": "购物车信息", "allCarts": "所有购物车", "loading": "加载中...", "view": "查看详情", "edit": "编辑数量", "delete": "删除购物车", "clearCart": "清空购物车", "moreActions": "更多操作", "form": {"id": "购物车ID", "userId": "用户ID", "userName": "用户名", "userEmail": "邮箱", "totalItems": "商品总数", "totalValue": "总价值", "createdAt": "创建时间"}, "table": {"id": "ID", "customer": "客户", "product": "产品", "price": "单价", "quantity": "数量", "total": "小计", "items": "商品数量"}, "items": {"items": "购物车商品", "empty": "无商品"}, "confirmDeleteMessage": "您确定要删除此购物车吗？"}, "cartDetails": {"table": {"id": "购物车ID", "customer": "客户", "product": "产品", "price": "单价", "quantity": "数量", "total": "小计", "items": "商品数量"}, "confirmDeleteMessage": "您确定要删除此购物车吗？"}}}