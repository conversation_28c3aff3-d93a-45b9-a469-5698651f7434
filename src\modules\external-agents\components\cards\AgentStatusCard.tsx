import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography } from '@/shared/components/common';
import StatusIndicator from '../indicators/StatusIndicator';
import { AgentPerformanceMetrics, ExternalAgentStatus } from '../../types';
import { formatNumber, formatPercentage, formatResponseTime, formatUptime } from '../../utils';

interface AgentStatusCardProps {
  agentId: string;
  agentName: string;
  status: ExternalAgentStatus;
  performance?: AgentPerformanceMetrics;
  isRealTime?: boolean;
  className?: string;
}

const AgentStatusCard: React.FC<AgentStatusCardProps> = ({
  agentId,
  agentName,
  status,
  performance,
  isRealTime = false,
  className,
}) => {
  const { t } = useTranslation(['common', 'external-agents']);



  const successRate = performance 
    ? (performance.successfulRequests / performance.totalRequests) * 100 
    : 0;

  return (
    <Card className={`w-full bg-background text-foreground ${className}`}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <StatusIndicator status={status} size="lg" />
            <div>
              <Typography variant="h4" className="font-semibold">
                {agentName}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                ID: {agentId}
              </Typography>
            </div>
          </div>
          {isRealTime && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <Typography variant="caption" className="text-green-600">
                {t('common:realTime')}
              </Typography>
            </div>
          )}
        </div>

        {/* Performance Metrics */}
        {performance && (
          <div className="grid grid-cols-2 gap-4">
            {/* Total Requests */}
            <div className="text-center">
              <Typography variant="h2" className="font-bold text-primary">
                {formatNumber(performance.totalRequests)}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('external-agents:performance.totalRequests')}
              </Typography>
            </div>

            {/* Success Rate */}
            <div className="text-center">
              <Typography 
                variant="h2" 
                className={`font-bold ${successRate >= 95 ? 'text-green-600' : successRate >= 80 ? 'text-yellow-600' : 'text-red-600'}`}
              >
                {formatPercentage(performance.successfulRequests, performance.totalRequests)}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('external-agents:performance.successRate')}
              </Typography>
            </div>

            {/* Average Response Time */}
            <div className="text-center">
              <Typography 
                variant="h3" 
                className={`font-semibold ${performance.averageResponseTime < 1000 ? 'text-green-600' : performance.averageResponseTime < 3000 ? 'text-yellow-600' : 'text-red-600'}`}
              >
                {formatResponseTime(performance.averageResponseTime)}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('external-agents:performance.averageResponseTime')}
              </Typography>
            </div>

            {/* Uptime */}
            <div className="text-center">
              <Typography 
                variant="h3" 
                className={`font-semibold ${performance.uptime >= 99 ? 'text-green-600' : performance.uptime >= 95 ? 'text-yellow-600' : 'text-red-600'}`}
              >
                {formatUptime(performance.uptime)}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {t('external-agents:performance.uptime')}
              </Typography>
            </div>
          </div>
        )}

        {/* No Performance Data */}
        {!performance && (
          <div className="text-center py-8">
            <Typography variant="body2" className="text-muted-foreground">
              {t('external-agents:performance.noData')}
            </Typography>
          </div>
        )}

        {/* Last Updated */}
        {performance && (
          <div className="mt-4 pt-3 border-t">
            <Typography variant="caption" className="text-muted-foreground">
              {t('external-agents:performance.lastUpdated')}: {new Date(performance.lastUpdated).toLocaleString('vi-VN')}
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default AgentStatusCard;
