export enum ExternalAgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CONNECTING = 'connecting',
  ERROR = 'error',
  MAINTENANCE = 'maintenance'
}

export enum ProtocolType {
  MCP = 'mcp',
  GOOGLE_AGENT = 'google_agent',
  REST_API = 'rest_api',
  WEBSOCKET = 'websocket',
  GRPC = 'grpc',
  CUSTOM = 'custom'
}

export enum AuthenticationType {
  NONE = 'none',
  API_KEY = 'api_key',
  BEARER_TOKEN = 'bearer_token',
  OAUTH2 = 'oauth2',
  BASIC_AUTH = 'basic_auth',
  CUSTOM = 'custom'
}

export enum MessageType {
  REQUEST = 'request',
  RESPONSE = 'response',
  ERROR = 'error',
  NOTIFICATION = 'notification'
}

export enum ConnectionTestStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  SUCCESS = 'success',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

export enum AgentCapability {
  TEXT_PROCESSING = 'text_processing',
  IMAGE_ANALYSIS = 'image_analysis',
  DATA_RETRIEVAL = 'data_retrieval',
  FILE_OPERATIONS = 'file_operations',
  API_CALLS = 'api_calls',
  REAL_TIME_COMMUNICATION = 'real_time_communication',
  CUSTOM = 'custom'
}
