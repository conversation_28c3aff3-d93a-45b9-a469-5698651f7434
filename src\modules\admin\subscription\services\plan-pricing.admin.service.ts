import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  PlanPricing,
  GetPlanPricingsQueryDto,
  CreatePlanPricingDto,
  UpdatePlanPricingDto,
} from '../types/plan-pricing.admin.types';

/**
 * Service cho quản lý Plan Pricing Admin
 */
export class PlanPricingAdminService {
  private static readonly BASE_URL = '/admin/subscription/plan-pricings';

  /**
   * Lấy danh sách plan pricing với phân trang
   */
  static async getPlanPricings(
    params: GetPlanPricingsQueryDto
  ): Promise<ApiResponse<PaginatedResult<PlanPricing>>> {
    return apiClient.get<PaginatedResult<PlanPricing>>(
      this.BASE_URL,
      {
        params,
      }
    );
  }

  /**
   * L<PERSON>y thông tin chi tiết plan pricing theo ID
   */
  static async getPlanPricingById(id: number): Promise<ApiResponse<PlanPricing>> {
    return apiClient.get<PlanPricing>(`${this.BASE_URL}/${id}`);
  }

  /**
   * Tạo plan pricing mới
   */
  static async createPlanPricing(
    data: CreatePlanPricingDto
  ): Promise<ApiResponse<PlanPricing>> {
    return apiClient.post<PlanPricing>(this.BASE_URL, data);
  }

  /**
   * Cập nhật thông tin plan pricing
   */
  static async updatePlanPricing(
    id: number,
    data: UpdatePlanPricingDto
  ): Promise<ApiResponse<PlanPricing>> {
    return apiClient.put<PlanPricing>(`${this.BASE_URL}/${id}`, data);
  }

  /**
   * Xóa plan pricing
   */
  static async deletePlanPricing(id: number): Promise<ApiResponse<null>> {
    return apiClient.delete<null>(`${this.BASE_URL}/${id}`);
  }

  /**
   * Lấy danh sách plan pricing theo plan ID
   */
  static async getPlanPricingsByPlanId(
    planId: number,
    params?: Omit<GetPlanPricingsQueryDto, 'planId'>
  ): Promise<ApiResponse<PaginatedResult<PlanPricing>>> {
    const queryParams: GetPlanPricingsQueryDto = {
      page: 1,
      limit: 10,
      ...params,
      planId
    };
    return this.getPlanPricings(queryParams);
  }

  /**
   * Kích hoạt/vô hiệu hóa plan pricing
   */
  static async togglePlanPricingStatus(
    id: number,
    isActive: boolean
  ): Promise<ApiResponse<PlanPricing>> {
    return this.updatePlanPricing(id, { isActive });
  }

  /**
   * Lấy danh sách plan pricing active
   */
  static async getActivePlanPricings(
    params?: Omit<GetPlanPricingsQueryDto, 'isActive'>
  ): Promise<ApiResponse<PaginatedResult<PlanPricing>>> {
    const queryParams: GetPlanPricingsQueryDto = {
      page: 1,
      limit: 10,
      ...params,
      isActive: true
    };
    return this.getPlanPricings(queryParams);
  }

  /**
   * Lấy danh sách plan pricing inactive
   */
  static async getInactivePlanPricings(
    params?: Omit<GetPlanPricingsQueryDto, 'isActive'>
  ): Promise<ApiResponse<PaginatedResult<PlanPricing>>> {
    const queryParams: GetPlanPricingsQueryDto = {
      page: 1,
      limit: 10,
      ...params,
      isActive: false
    };
    return this.getPlanPricings(queryParams);
  }
}
